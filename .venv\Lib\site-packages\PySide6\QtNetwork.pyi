# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtNetwork, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtNetwork`

import PySide6.QtNetwork
import PySide6.QtCore

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QAbstractNetworkCache(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def cacheSize(self, /) -> int: ...
    def clear(self, /) -> None: ...
    def data(self, url: PySide6.QtCore.QUrl | str, /) -> PySide6.QtCore.QIODevice: ...
    def insert(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def metaData(self, url: PySide6.QtCore.QUrl | str, /) -> PySide6.QtNetwork.QNetworkCacheMetaData: ...
    def prepare(self, metaData: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> PySide6.QtCore.QIODevice: ...
    def remove(self, url: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def updateMetaData(self, metaData: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> None: ...


class QAbstractSocket(PySide6.QtCore.QIODevice):

    connected                : typing.ClassVar[Signal] = ... # connected()
    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QAbstractSocket::SocketError)
    hostFound                : typing.ClassVar[Signal] = ... # hostFound()
    proxyAuthenticationRequired: typing.ClassVar[Signal] = ... # proxyAuthenticationRequired(QNetworkProxy,QAuthenticator*)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QAbstractSocket::SocketState)

    class BindFlag(enum.Flag):

        DefaultForPlatform        = ...  # 0x0
        ShareAddress              = ...  # 0x1
        DontShareAddress          = ...  # 0x2
        ReuseAddressHint          = ...  # 0x4

    class NetworkLayerProtocol(enum.Enum):

        UnknownNetworkLayerProtocol = ...  # -1
        IPv4Protocol              = ...  # 0x0
        IPv6Protocol              = ...  # 0x1
        AnyIPProtocol             = ...  # 0x2

    class PauseMode(enum.Flag):

        PauseNever                = ...  # 0x0
        PauseOnSslErrors          = ...  # 0x1

    class SocketError(enum.Enum):

        UnknownSocketError        = ...  # -1
        ConnectionRefusedError    = ...  # 0x0
        RemoteHostClosedError     = ...  # 0x1
        HostNotFoundError         = ...  # 0x2
        SocketAccessError         = ...  # 0x3
        SocketResourceError       = ...  # 0x4
        SocketTimeoutError        = ...  # 0x5
        DatagramTooLargeError     = ...  # 0x6
        NetworkError              = ...  # 0x7
        AddressInUseError         = ...  # 0x8
        SocketAddressNotAvailableError = ...  # 0x9
        UnsupportedSocketOperationError = ...  # 0xa
        UnfinishedSocketOperationError = ...  # 0xb
        ProxyAuthenticationRequiredError = ...  # 0xc
        SslHandshakeFailedError   = ...  # 0xd
        ProxyConnectionRefusedError = ...  # 0xe
        ProxyConnectionClosedError = ...  # 0xf
        ProxyConnectionTimeoutError = ...  # 0x10
        ProxyNotFoundError        = ...  # 0x11
        ProxyProtocolError        = ...  # 0x12
        OperationError            = ...  # 0x13
        SslInternalError          = ...  # 0x14
        SslInvalidUserDataError   = ...  # 0x15
        TemporaryError            = ...  # 0x16

    class SocketOption(enum.Enum):

        LowDelayOption            = ...  # 0x0
        KeepAliveOption           = ...  # 0x1
        MulticastTtlOption        = ...  # 0x2
        MulticastLoopbackOption   = ...  # 0x3
        TypeOfServiceOption       = ...  # 0x4
        SendBufferSizeSocketOption = ...  # 0x5
        ReceiveBufferSizeSocketOption = ...  # 0x6
        PathMtuSocketOption       = ...  # 0x7

    class SocketState(enum.Enum):

        UnconnectedState          = ...  # 0x0
        HostLookupState           = ...  # 0x1
        ConnectingState           = ...  # 0x2
        ConnectedState            = ...  # 0x3
        BoundState                = ...  # 0x4
        ListeningState            = ...  # 0x5
        ClosingState              = ...  # 0x6

    class SocketType(enum.Enum):

        UnknownSocketType         = ...  # -1
        TcpSocket                 = ...  # 0x0
        UdpSocket                 = ...  # 0x1
        SctpSocket                = ...  # 0x2


    def __init__(self, socketType: PySide6.QtNetwork.QAbstractSocket.SocketType, parent: PySide6.QtCore.QObject, /) -> None: ...

    def abort(self, /) -> None: ...
    @typing.overload
    def bind(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    @typing.overload
    def bind(self, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    def bytesAvailable(self, /) -> int: ...
    def bytesToWrite(self, /) -> int: ...
    def close(self, /) -> None: ...
    @typing.overload
    def connectToHost(self, hostName: str, port: int, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: PySide6.QtNetwork.QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    @typing.overload
    def connectToHost(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    def disconnectFromHost(self, /) -> None: ...
    def error(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketError: ...
    def flush(self, /) -> bool: ...
    def isSequential(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def localAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def localPort(self, /) -> int: ...
    def pauseMode(self, /) -> PySide6.QtNetwork.QAbstractSocket.PauseMode: ...
    def peerAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def peerName(self, /) -> str: ...
    def peerPort(self, /) -> int: ...
    def protocolTag(self, /) -> str: ...
    def proxy(self, /) -> PySide6.QtNetwork.QNetworkProxy: ...
    def readBufferSize(self, /) -> int: ...
    def readData(self, maxlen: int, /) -> object: ...
    def readLineData(self, maxlen: int, /) -> object: ...
    def resume(self, /) -> None: ...
    def setLocalAddress(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def setLocalPort(self, port: int, /) -> None: ...
    def setPauseMode(self, pauseMode: PySide6.QtNetwork.QAbstractSocket.PauseMode, /) -> None: ...
    def setPeerAddress(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def setPeerName(self, name: str, /) -> None: ...
    def setPeerPort(self, port: int, /) -> None: ...
    def setProtocolTag(self, tag: str, /) -> None: ...
    def setProxy(self, networkProxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setReadBufferSize(self, size: int, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, /, state: PySide6.QtNetwork.QAbstractSocket.SocketState = ..., openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def setSocketError(self, socketError: PySide6.QtNetwork.QAbstractSocket.SocketError, /) -> None: ...
    def setSocketOption(self, option: PySide6.QtNetwork.QAbstractSocket.SocketOption, value: typing.Any, /) -> None: ...
    def setSocketState(self, state: PySide6.QtNetwork.QAbstractSocket.SocketState, /) -> None: ...
    def skipData(self, maxSize: int, /) -> int: ...
    def socketDescriptor(self, /) -> int: ...
    def socketOption(self, option: PySide6.QtNetwork.QAbstractSocket.SocketOption, /) -> typing.Any: ...
    def socketType(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketType: ...
    def state(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketState: ...
    def waitForBytesWritten(self, /, msecs: int = ...) -> bool: ...
    def waitForConnected(self, /, msecs: int = ...) -> bool: ...
    def waitForDisconnected(self, /, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, /, msecs: int = ...) -> bool: ...
    def writeData(self, data: bytes | bytearray | memoryview, len: int, /) -> int: ...


class QAuthenticator(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QAuthenticator, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QAuthenticator, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QAuthenticator, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def option(self, opt: str, /) -> typing.Any: ...
    def options(self, /) -> typing.Dict[str, typing.Any]: ...
    def password(self, /) -> str: ...
    def realm(self, /) -> str: ...
    def setOption(self, opt: str, value: typing.Any, /) -> None: ...
    def setPassword(self, password: str, /) -> None: ...
    def setRealm(self, realm: str, /) -> None: ...
    def setUser(self, user: str, /) -> None: ...
    def user(self, /) -> str: ...


class QDnsDomainNameRecord(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsDomainNameRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def name(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QDnsDomainNameRecord, /) -> None: ...
    def timeToLive(self, /) -> int: ...
    def value(self, /) -> str: ...


class QDnsHostAddressRecord(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsHostAddressRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def name(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QDnsHostAddressRecord, /) -> None: ...
    def timeToLive(self, /) -> int: ...
    def value(self, /) -> PySide6.QtNetwork.QHostAddress: ...


class QDnsLookup(PySide6.QtCore.QObject):

    finished                 : typing.ClassVar[Signal] = ... # finished()
    nameChanged              : typing.ClassVar[Signal] = ... # nameChanged(QString)
    nameserverChanged        : typing.ClassVar[Signal] = ... # nameserverChanged(QHostAddress)
    nameserverPortChanged    : typing.ClassVar[Signal] = ... # nameserverPortChanged(ushort)
    nameserverProtocolChanged: typing.ClassVar[Signal] = ... # nameserverProtocolChanged(QDnsLookup::Protocol)
    typeChanged              : typing.ClassVar[Signal] = ... # typeChanged(QDnsLookup::Type)

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        ResolverError             = ...  # 0x1
        OperationCancelledError   = ...  # 0x2
        InvalidRequestError       = ...  # 0x3
        InvalidReplyError         = ...  # 0x4
        ServerFailureError        = ...  # 0x5
        ServerRefusedError        = ...  # 0x6
        NotFoundError             = ...  # 0x7
        TimeoutError              = ...  # 0x8

    class Protocol(enum.Enum):

        Standard                  = ...  # 0x0
        DnsOverTls                = ...  # 0x1

    class Type(enum.Enum):

        A                         = ...  # 0x1
        NS                        = ...  # 0x2
        CNAME                     = ...  # 0x5
        PTR                       = ...  # 0xc
        MX                        = ...  # 0xf
        TXT                       = ...  # 0x10
        AAAA                      = ...  # 0x1c
        SRV                       = ...  # 0x21
        TLSA                      = ...  # 0x34
        ANY                       = ...  # 0xff


    @typing.overload
    def __init__(self, type: PySide6.QtNetwork.QDnsLookup.Type, name: str, protocol: PySide6.QtNetwork.QDnsLookup.Protocol, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., parent: PySide6.QtCore.QObject | None = ..., *, error: PySide6.QtNetwork.QDnsLookup.Error | None = ..., authenticData: bool | None = ..., errorString: str | None = ..., nameserverPort: int | None = ..., nameserverProtocol: PySide6.QtNetwork.QDnsLookup.Protocol | None = ...) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtNetwork.QDnsLookup.Type, name: str, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, parent: PySide6.QtCore.QObject | None = ..., *, error: PySide6.QtNetwork.QDnsLookup.Error | None = ..., authenticData: bool | None = ..., errorString: str | None = ..., nameserverPort: int | None = ..., nameserverProtocol: PySide6.QtNetwork.QDnsLookup.Protocol | None = ...) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtNetwork.QDnsLookup.Type, name: str, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /, parent: PySide6.QtCore.QObject | None = ..., *, error: PySide6.QtNetwork.QDnsLookup.Error | None = ..., authenticData: bool | None = ..., errorString: str | None = ..., nameserverPort: int | None = ..., nameserverProtocol: PySide6.QtNetwork.QDnsLookup.Protocol | None = ...) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtNetwork.QDnsLookup.Type, name: str, /, parent: PySide6.QtCore.QObject | None = ..., *, error: PySide6.QtNetwork.QDnsLookup.Error | None = ..., authenticData: bool | None = ..., errorString: str | None = ..., nameserver: PySide6.QtNetwork.QHostAddress | None = ..., nameserverPort: int | None = ..., nameserverProtocol: PySide6.QtNetwork.QDnsLookup.Protocol | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, error: PySide6.QtNetwork.QDnsLookup.Error | None = ..., authenticData: bool | None = ..., errorString: str | None = ..., name: str | None = ..., type: PySide6.QtNetwork.QDnsLookup.Type | None = ..., nameserver: PySide6.QtNetwork.QHostAddress | None = ..., nameserverPort: int | None = ..., nameserverProtocol: PySide6.QtNetwork.QDnsLookup.Protocol | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def canonicalNameRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsDomainNameRecord]: ...
    @staticmethod
    def defaultPortForProtocol(protocol: PySide6.QtNetwork.QDnsLookup.Protocol, /) -> int: ...
    def error(self, /) -> PySide6.QtNetwork.QDnsLookup.Error: ...
    def errorString(self, /) -> str: ...
    def hostAddressRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsHostAddressRecord]: ...
    def isAuthenticData(self, /) -> bool: ...
    def isFinished(self, /) -> bool: ...
    @staticmethod
    def isProtocolSupported(protocol: PySide6.QtNetwork.QDnsLookup.Protocol, /) -> bool: ...
    def lookup(self, /) -> None: ...
    def mailExchangeRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsMailExchangeRecord]: ...
    def name(self, /) -> str: ...
    def nameServerRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsDomainNameRecord]: ...
    def nameserver(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def nameserverPort(self, /) -> int: ...
    def nameserverProtocol(self, /) -> PySide6.QtNetwork.QDnsLookup.Protocol: ...
    def pointerRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsDomainNameRecord]: ...
    def serviceRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsServiceRecord]: ...
    def setName(self, name: str, /) -> None: ...
    @typing.overload
    def setNameserver(self, protocol: PySide6.QtNetwork.QDnsLookup.Protocol, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ...) -> None: ...
    @typing.overload
    def setNameserver(self, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    @typing.overload
    def setNameserver(self, nameserver: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /) -> None: ...
    def setNameserverPort(self, port: int, /) -> None: ...
    def setNameserverProtocol(self, protocol: PySide6.QtNetwork.QDnsLookup.Protocol, /) -> None: ...
    def setSslConfiguration(self, sslConfiguration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setType(self, arg__1: PySide6.QtNetwork.QDnsLookup.Type, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def textRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsTextRecord]: ...
    def tlsAssociationRecords(self, /) -> typing.List[PySide6.QtNetwork.QDnsTlsAssociationRecord]: ...
    def type(self, /) -> PySide6.QtNetwork.QDnsLookup.Type: ...


class QDnsMailExchangeRecord(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsMailExchangeRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def exchange(self, /) -> str: ...
    def name(self, /) -> str: ...
    def preference(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QDnsMailExchangeRecord, /) -> None: ...
    def timeToLive(self, /) -> int: ...


class QDnsServiceRecord(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsServiceRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def name(self, /) -> str: ...
    def port(self, /) -> int: ...
    def priority(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QDnsServiceRecord, /) -> None: ...
    def target(self, /) -> str: ...
    def timeToLive(self, /) -> int: ...
    def weight(self, /) -> int: ...


class QDnsTextRecord(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsTextRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def name(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QDnsTextRecord, /) -> None: ...
    def timeToLive(self, /) -> int: ...
    def values(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...


class QDnsTlsAssociationRecord(Shiboken.Object):

    class CertificateUsage(enum.Enum):

        CertificateAuthorityConstrait = ...  # 0x0
        PKIX_TA                   = ...  # 0x0
        PKIX_EE                   = ...  # 0x1
        ServiceCertificateConstraint = ...  # 0x1
        DANE_TA                   = ...  # 0x2
        TrustAnchorAssertion      = ...  # 0x2
        DANE_EE                   = ...  # 0x3
        DomainIssuedCertificate   = ...  # 0x3
        PrivCert                  = ...  # 0xff
        PrivateUse                = ...  # 0xff

    class MatchingType(enum.Enum):

        Exact                     = ...  # 0x0
        Sha256                    = ...  # 0x1
        Sha512                    = ...  # 0x2
        PrivMatch                 = ...  # 0xff
        PrivateUse                = ...  # 0xff

    class Selector(enum.Enum):

        Cert                      = ...  # 0x0
        FullCertificate           = ...  # 0x0
        SPKI                      = ...  # 0x1
        SubjectPublicKeyInfo      = ...  # 0x1
        PrivSel                   = ...  # 0xff
        PrivateUse                = ...  # 0xff


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QDnsTlsAssociationRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def matchType(self, /) -> PySide6.QtNetwork.QDnsTlsAssociationRecord.MatchingType: ...
    def name(self, /) -> str: ...
    def selector(self, /) -> PySide6.QtNetwork.QDnsTlsAssociationRecord.Selector: ...
    def swap(self, other: PySide6.QtNetwork.QDnsTlsAssociationRecord, /) -> None: ...
    def timeToLive(self, /) -> int: ...
    def usage(self, /) -> PySide6.QtNetwork.QDnsTlsAssociationRecord.CertificateUsage: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...


class QDtls(PySide6.QtCore.QObject):

    handshakeTimeout         : typing.ClassVar[Signal] = ... # handshakeTimeout()
    pskRequired              : typing.ClassVar[Signal] = ... # pskRequired(QSslPreSharedKeyAuthenticator*)

    class HandshakeState(enum.Enum):

        HandshakeNotStarted       = ...  # 0x0
        HandshakeInProgress       = ...  # 0x1
        PeerVerificationFailed    = ...  # 0x2
        HandshakeComplete         = ...  # 0x3


    def __init__(self, mode: PySide6.QtNetwork.QSslSocket.SslMode, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abortHandshake(self, socket: PySide6.QtNetwork.QUdpSocket, /) -> bool: ...
    def cookieGeneratorParameters(self, /) -> PySide6.QtNetwork.QDtlsClientVerifier.GeneratorParameters: ...
    def decryptDatagram(self, socket: PySide6.QtNetwork.QUdpSocket, dgram: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtCore.QByteArray: ...
    def doHandshake(self, socket: PySide6.QtNetwork.QUdpSocket, /, dgram: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> bool: ...
    def dtlsConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def dtlsError(self, /) -> PySide6.QtNetwork.QDtlsError: ...
    def dtlsErrorString(self, /) -> str: ...
    def handleTimeout(self, socket: PySide6.QtNetwork.QUdpSocket, /) -> bool: ...
    def handshakeState(self, /) -> PySide6.QtNetwork.QDtls.HandshakeState: ...
    def ignoreVerificationErrors(self, errorsToIgnore: collections.abc.Sequence[PySide6.QtNetwork.QSslError], /) -> None: ...
    def isConnectionEncrypted(self, /) -> bool: ...
    def mtuHint(self, /) -> int: ...
    def peerAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def peerPort(self, /) -> int: ...
    def peerVerificationErrors(self, /) -> typing.List[PySide6.QtNetwork.QSslError]: ...
    def peerVerificationName(self, /) -> str: ...
    def resumeHandshake(self, socket: PySide6.QtNetwork.QUdpSocket, /) -> bool: ...
    def sessionCipher(self, /) -> PySide6.QtNetwork.QSslCipher: ...
    def sessionProtocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    def setCookieGeneratorParameters(self, params: PySide6.QtNetwork.QDtlsClientVerifier.GeneratorParameters, /) -> bool: ...
    def setDtlsConfiguration(self, configuration: PySide6.QtNetwork.QSslConfiguration, /) -> bool: ...
    def setMtuHint(self, mtuHint: int, /) -> None: ...
    def setPeer(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /, verificationName: str = ...) -> bool: ...
    def setPeerVerificationName(self, name: str, /) -> bool: ...
    def shutdown(self, socket: PySide6.QtNetwork.QUdpSocket, /) -> bool: ...
    def sslMode(self, /) -> PySide6.QtNetwork.QSslSocket.SslMode: ...
    def writeDatagramEncrypted(self, socket: PySide6.QtNetwork.QUdpSocket, dgram: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> int: ...


class QDtlsClientVerifier(PySide6.QtCore.QObject):

    class GeneratorParameters(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, a: PySide6.QtCore.QCryptographicHash.Algorithm, s: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
        @typing.overload
        def __init__(self, GeneratorParameters: PySide6.QtNetwork.QDtlsClientVerifier.GeneratorParameters, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def cookieGeneratorParameters(self, /) -> PySide6.QtNetwork.QDtlsClientVerifier.GeneratorParameters: ...
    def dtlsError(self, /) -> PySide6.QtNetwork.QDtlsError: ...
    def dtlsErrorString(self, /) -> str: ...
    def setCookieGeneratorParameters(self, params: PySide6.QtNetwork.QDtlsClientVerifier.GeneratorParameters, /) -> bool: ...
    def verifiedHello(self, /) -> PySide6.QtCore.QByteArray: ...
    def verifyClient(self, socket: PySide6.QtNetwork.QUdpSocket, dgram: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /) -> bool: ...


class QDtlsError(enum.Enum):

    NoError                   = ...  # 0x0
    InvalidInputParameters    = ...  # 0x1
    InvalidOperation          = ...  # 0x2
    UnderlyingSocketError     = ...  # 0x3
    RemoteClosedConnectionError = ...  # 0x4
    PeerVerificationError     = ...  # 0x5
    TlsInitializationError    = ...  # 0x6
    TlsFatalError             = ...  # 0x7
    TlsNonFatalError          = ...  # 0x8


class QFormDataBuilder(Shiboken.Object):

    class Option(enum.Flag):

        Default                   = ...  # 0x0
        OmitRfc8187EncodedFilename = ...  # 0x1
        UseRfc7578PercentEncodedFilename = ...  # 0x2
        StrictRfc7578             = ...  # 0x3
        PreferLatin1EncodedFilename = ...  # 0x4


    def __init__(self, /) -> None: ...

    def part(self, name: str, /) -> PySide6.QtNetwork.QFormDataPartBuilder: ...
    def swap(self, other: PySide6.QtNetwork.QFormDataBuilder, /) -> None: ...


class QFormDataPartBuilder(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def setBody(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, fileName: str = ..., mimeType: str = ...) -> PySide6.QtNetwork.QFormDataPartBuilder: ...
    def setBodyDevice(self, body: PySide6.QtCore.QIODevice, /, fileName: str = ..., mimeType: str = ...) -> PySide6.QtNetwork.QFormDataPartBuilder: ...
    def setHeaders(self, headers: PySide6.QtNetwork.QHttpHeaders, /) -> PySide6.QtNetwork.QFormDataPartBuilder: ...
    def swap(self, other: PySide6.QtNetwork.QFormDataPartBuilder, /) -> None: ...


class QHostAddress(Shiboken.Object):

    UnknownNetworkLayerProtocol = ...  # -1
    IPv4Protocol              = ...  # 0x0
    IPv6Protocol              = ...  # 0x1
    AnyIPProtocol             = ...  # 0x2

    class ConversionModeFlag(enum.Flag):

        StrictConversion          = ...  # 0x0
        ConvertV4MappedToIPv4     = ...  # 0x1
        ConvertV4CompatToIPv4     = ...  # 0x2
        ConvertUnspecifiedAddress = ...  # 0x4
        ConvertLocalHost          = ...  # 0x8
        TolerantConversion        = ...  # 0xff

    class SpecialAddress(enum.Enum):

        Null                      = ...  # 0x0
        Broadcast                 = ...  # 0x1
        LocalHost                 = ...  # 0x2
        LocalHostIPv6             = ...  # 0x3
        Any                       = ...  # 0x4
        AnyIPv6                   = ...  # 0x5
        AnyIPv4                   = ...  # 0x6


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, copy: PySide6.QtNetwork.QHostAddress, /) -> None: ...
    @typing.overload
    def __init__(self, ip6Addr: PySide6.QtNetwork.QIPv6Address, /) -> None: ...
    @typing.overload
    def __init__(self, address: PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    @typing.overload
    def __init__(self, address: str, /) -> None: ...
    @typing.overload
    def __init__(self, ip4Addr: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @typing.overload
    def __eq__(self, address: PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    @typing.overload
    def __eq__(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    @typing.overload
    def __ne__(self, address: PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    @typing.overload
    def __ne__(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def clear(self, /) -> None: ...
    def isBroadcast(self, /) -> bool: ...
    def isEqual(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, mode: PySide6.QtNetwork.QHostAddress.ConversionModeFlag = ...) -> bool: ...
    def isGlobal(self, /) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, netmask: int, /) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: typing.Tuple[PySide6.QtNetwork.QHostAddress, int], /) -> bool: ...
    def isLinkLocal(self, /) -> bool: ...
    def isLoopback(self, /) -> bool: ...
    def isMulticast(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isPrivateUse(self, /) -> bool: ...
    def isSiteLocal(self, /) -> bool: ...
    def isUniqueLocalUnicast(self, /) -> bool: ...
    @staticmethod
    def parseSubnet(subnet: str, /) -> typing.Tuple[PySide6.QtNetwork.QHostAddress, int]: ...
    def protocol(self, /) -> PySide6.QtNetwork.QAbstractSocket.NetworkLayerProtocol: ...
    def scopeId(self, /) -> str: ...
    @typing.overload
    def setAddress(self, ip6Addr: PySide6.QtNetwork.QIPv6Address, /) -> None: ...
    @typing.overload
    def setAddress(self, address: PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    @typing.overload
    def setAddress(self, address: str, /) -> bool: ...
    @typing.overload
    def setAddress(self, ip4Addr: int, /) -> None: ...
    def setScopeId(self, id: str, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def toIPv4Address(self, /) -> typing.Tuple[int, bool]: ...
    def toIPv6Address(self, /) -> PySide6.QtNetwork.QIPv6Address: ...
    def toString(self, /) -> str: ...


class QHostInfo(Shiboken.Object):

    class HostInfoError(enum.Enum):

        NoError                   = ...  # 0x0
        HostNotFound              = ...  # 0x1
        UnknownError              = ...  # 0x2


    @typing.overload
    def __init__(self, d: PySide6.QtNetwork.QHostInfo, /) -> None: ...
    @typing.overload
    def __init__(self, /, lookupId: int = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @staticmethod
    def abortHostLookup(lookupId: int, /) -> None: ...
    def addresses(self, /) -> typing.List[PySide6.QtNetwork.QHostAddress]: ...
    def error(self, /) -> PySide6.QtNetwork.QHostInfo.HostInfoError: ...
    def errorString(self, /) -> str: ...
    @staticmethod
    def fromName(name: str, /) -> PySide6.QtNetwork.QHostInfo: ...
    def hostName(self, /) -> str: ...
    @staticmethod
    def localDomainName() -> str: ...
    @staticmethod
    def localHostName() -> str: ...
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    def lookupHost(self, name: str, callable: collections.abc.Callable[..., typing.Any], /) -> None: ...  # type: ignore[misc, overload-cannot-match]
    @typing.overload  # type: ignore[misc, overload-cannot-match]
    @staticmethod
    def lookupHost(name: str, receiver: PySide6.QtCore.QObject, member: bytes | bytearray | memoryview, /) -> int: ...  # type: ignore[misc, overload-cannot-match]
    def lookupId(self, /) -> int: ...
    def setAddresses(self, addresses: collections.abc.Sequence[PySide6.QtNetwork.QHostAddress], /) -> None: ...
    def setError(self, error: PySide6.QtNetwork.QHostInfo.HostInfoError, /) -> None: ...
    def setErrorString(self, errorString: str, /) -> None: ...
    def setHostName(self, name: str, /) -> None: ...
    def setLookupId(self, id: int, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QHostInfo | int, /) -> None: ...


class QHstsPolicy(Shiboken.Object):

    class PolicyFlag(enum.Flag):

        IncludeSubDomains         = ...  # 0x1


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, expiry: PySide6.QtCore.QDateTime, flags: PySide6.QtNetwork.QHstsPolicy.PolicyFlag, host: str, /, mode: PySide6.QtCore.QUrl.ParsingMode = ...) -> None: ...
    @typing.overload
    def __init__(self, rhs: PySide6.QtNetwork.QHstsPolicy, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QHstsPolicy, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QHstsPolicy, /) -> bool: ...
    def expiry(self, /) -> PySide6.QtCore.QDateTime: ...
    def host(self, /, options: PySide6.QtCore.QUrl.ComponentFormattingOption = ...) -> str: ...
    def includesSubDomains(self, /) -> bool: ...
    def isExpired(self, /) -> bool: ...
    def setExpiry(self, expiry: PySide6.QtCore.QDateTime, /) -> None: ...
    def setHost(self, host: str, /, mode: PySide6.QtCore.QUrl.ParsingMode = ...) -> None: ...
    def setIncludesSubDomains(self, include: bool, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QHstsPolicy, /) -> None: ...


class QHttp1Configuration(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QHttp1Configuration, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QHttp1Configuration, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QHttp1Configuration, /) -> bool: ...
    def numberOfConnectionsPerHost(self, /) -> int: ...
    def setNumberOfConnectionsPerHost(self, amount: int, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QHttp1Configuration, /) -> None: ...


class QHttp2Configuration(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QHttp2Configuration, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QHttp2Configuration, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QHttp2Configuration, /) -> bool: ...
    def huffmanCompressionEnabled(self, /) -> bool: ...
    def maxConcurrentStreams(self, /) -> int: ...
    def maxFrameSize(self, /) -> int: ...
    def serverPushEnabled(self, /) -> bool: ...
    def sessionReceiveWindowSize(self, /) -> int: ...
    def setHuffmanCompressionEnabled(self, enable: bool, /) -> None: ...
    def setMaxConcurrentStreams(self, value: int, /) -> None: ...
    def setMaxFrameSize(self, size: int, /) -> bool: ...
    def setServerPushEnabled(self, enable: bool, /) -> None: ...
    def setSessionReceiveWindowSize(self, size: int, /) -> bool: ...
    def setStreamReceiveWindowSize(self, size: int, /) -> bool: ...
    def streamReceiveWindowSize(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QHttp2Configuration, /) -> None: ...


class QHttpHeaders(Shiboken.Object):

    class WellKnownHeader(enum.Enum):

        AIM                       = ...  # 0x0
        Accept                    = ...  # 0x1
        AcceptAdditions           = ...  # 0x2
        AcceptCH                  = ...  # 0x3
        AcceptDatetime            = ...  # 0x4
        AcceptEncoding            = ...  # 0x5
        AcceptFeatures            = ...  # 0x6
        AcceptLanguage            = ...  # 0x7
        AcceptPatch               = ...  # 0x8
        AcceptPost                = ...  # 0x9
        AcceptRanges              = ...  # 0xa
        AcceptSignature           = ...  # 0xb
        AccessControlAllowCredentials = ...  # 0xc
        AccessControlAllowHeaders = ...  # 0xd
        AccessControlAllowMethods = ...  # 0xe
        AccessControlAllowOrigin  = ...  # 0xf
        AccessControlExposeHeaders = ...  # 0x10
        AccessControlMaxAge       = ...  # 0x11
        AccessControlRequestHeaders = ...  # 0x12
        AccessControlRequestMethod = ...  # 0x13
        Age                       = ...  # 0x14
        Allow                     = ...  # 0x15
        ALPN                      = ...  # 0x16
        AltSvc                    = ...  # 0x17
        AltUsed                   = ...  # 0x18
        Alternates                = ...  # 0x19
        ApplyToRedirectRef        = ...  # 0x1a
        AuthenticationControl     = ...  # 0x1b
        AuthenticationInfo        = ...  # 0x1c
        Authorization             = ...  # 0x1d
        CacheControl              = ...  # 0x1e
        CacheStatus               = ...  # 0x1f
        CalManagedID              = ...  # 0x20
        CalDAVTimezones           = ...  # 0x21
        CapsuleProtocol           = ...  # 0x22
        CDNCacheControl           = ...  # 0x23
        CDNLoop                   = ...  # 0x24
        CertNotAfter              = ...  # 0x25
        CertNotBefore             = ...  # 0x26
        ClearSiteData             = ...  # 0x27
        ClientCert                = ...  # 0x28
        ClientCertChain           = ...  # 0x29
        Close                     = ...  # 0x2a
        Connection                = ...  # 0x2b
        ContentDigest             = ...  # 0x2c
        ContentDisposition        = ...  # 0x2d
        ContentEncoding           = ...  # 0x2e
        ContentID                 = ...  # 0x2f
        ContentLanguage           = ...  # 0x30
        ContentLength             = ...  # 0x31
        ContentLocation           = ...  # 0x32
        ContentRange              = ...  # 0x33
        ContentSecurityPolicy     = ...  # 0x34
        ContentSecurityPolicyReportOnly = ...  # 0x35
        ContentType               = ...  # 0x36
        Cookie                    = ...  # 0x37
        CrossOriginEmbedderPolicy = ...  # 0x38
        CrossOriginEmbedderPolicyReportOnly = ...  # 0x39
        CrossOriginOpenerPolicy   = ...  # 0x3a
        CrossOriginOpenerPolicyReportOnly = ...  # 0x3b
        CrossOriginResourcePolicy = ...  # 0x3c
        DASL                      = ...  # 0x3d
        Date                      = ...  # 0x3e
        DAV                       = ...  # 0x3f
        DeltaBase                 = ...  # 0x40
        Depth                     = ...  # 0x41
        Destination               = ...  # 0x42
        DifferentialID            = ...  # 0x43
        DPoP                      = ...  # 0x44
        DPoPNonce                 = ...  # 0x45
        EarlyData                 = ...  # 0x46
        ETag                      = ...  # 0x47
        Expect                    = ...  # 0x48
        ExpectCT                  = ...  # 0x49
        Expires                   = ...  # 0x4a
        Forwarded                 = ...  # 0x4b
        From                      = ...  # 0x4c
        Hobareg                   = ...  # 0x4d
        Host                      = ...  # 0x4e
        If                        = ...  # 0x4f
        IfMatch                   = ...  # 0x50
        IfModifiedSince           = ...  # 0x51
        IfNoneMatch               = ...  # 0x52
        IfRange                   = ...  # 0x53
        IfScheduleTagMatch        = ...  # 0x54
        IfUnmodifiedSince         = ...  # 0x55
        IM                        = ...  # 0x56
        IncludeReferredTokenBindingID = ...  # 0x57
        KeepAlive                 = ...  # 0x58
        Label                     = ...  # 0x59
        LastEventID               = ...  # 0x5a
        LastModified              = ...  # 0x5b
        Link                      = ...  # 0x5c
        Location                  = ...  # 0x5d
        LockToken                 = ...  # 0x5e
        MaxForwards               = ...  # 0x5f
        MementoDatetime           = ...  # 0x60
        Meter                     = ...  # 0x61
        MIMEVersion               = ...  # 0x62
        Negotiate                 = ...  # 0x63
        NEL                       = ...  # 0x64
        ODataEntityId             = ...  # 0x65
        ODataIsolation            = ...  # 0x66
        ODataMaxVersion           = ...  # 0x67
        ODataVersion              = ...  # 0x68
        OptionalWWWAuthenticate   = ...  # 0x69
        OrderingType              = ...  # 0x6a
        Origin                    = ...  # 0x6b
        OriginAgentCluster        = ...  # 0x6c
        OSCORE                    = ...  # 0x6d
        OSLCCoreVersion           = ...  # 0x6e
        Overwrite                 = ...  # 0x6f
        PingFrom                  = ...  # 0x70
        PingTo                    = ...  # 0x71
        Position                  = ...  # 0x72
        Prefer                    = ...  # 0x73
        PreferenceApplied         = ...  # 0x74
        Priority                  = ...  # 0x75
        ProxyAuthenticate         = ...  # 0x76
        ProxyAuthenticationInfo   = ...  # 0x77
        ProxyAuthorization        = ...  # 0x78
        ProxyStatus               = ...  # 0x79
        PublicKeyPins             = ...  # 0x7a
        PublicKeyPinsReportOnly   = ...  # 0x7b
        Range                     = ...  # 0x7c
        RedirectRef               = ...  # 0x7d
        Referer                   = ...  # 0x7e
        Refresh                   = ...  # 0x7f
        ReplayNonce               = ...  # 0x80
        ReprDigest                = ...  # 0x81
        RetryAfter                = ...  # 0x82
        ScheduleReply             = ...  # 0x83
        ScheduleTag               = ...  # 0x84
        SecPurpose                = ...  # 0x85
        SecTokenBinding           = ...  # 0x86
        SecWebSocketAccept        = ...  # 0x87
        SecWebSocketExtensions    = ...  # 0x88
        SecWebSocketKey           = ...  # 0x89
        SecWebSocketProtocol      = ...  # 0x8a
        SecWebSocketVersion       = ...  # 0x8b
        Server                    = ...  # 0x8c
        ServerTiming              = ...  # 0x8d
        SetCookie                 = ...  # 0x8e
        Signature                 = ...  # 0x8f
        SignatureInput            = ...  # 0x90
        SLUG                      = ...  # 0x91
        SoapAction                = ...  # 0x92
        StatusURI                 = ...  # 0x93
        StrictTransportSecurity   = ...  # 0x94
        Sunset                    = ...  # 0x95
        SurrogateCapability       = ...  # 0x96
        SurrogateControl          = ...  # 0x97
        TCN                       = ...  # 0x98
        TE                        = ...  # 0x99
        Timeout                   = ...  # 0x9a
        Topic                     = ...  # 0x9b
        Traceparent               = ...  # 0x9c
        Tracestate                = ...  # 0x9d
        Trailer                   = ...  # 0x9e
        TransferEncoding          = ...  # 0x9f
        TTL                       = ...  # 0xa0
        Upgrade                   = ...  # 0xa1
        Urgency                   = ...  # 0xa2
        UserAgent                 = ...  # 0xa3
        VariantVary               = ...  # 0xa4
        Vary                      = ...  # 0xa5
        Via                       = ...  # 0xa6
        WantContentDigest         = ...  # 0xa7
        WantReprDigest            = ...  # 0xa8
        WWWAuthenticate           = ...  # 0xa9
        XContentTypeOptions       = ...  # 0xaa
        XFrameOptions             = ...  # 0xab
        AcceptCharset             = ...  # 0xac
        CPEPInfo                  = ...  # 0xad
        Pragma                    = ...  # 0xae
        ProtocolInfo              = ...  # 0xaf
        ProtocolQuery             = ...  # 0xb0


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @typing.overload
    def append(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, value: str, /) -> bool: ...
    @typing.overload
    def append(self, name: str, value: str, /) -> bool: ...
    def clear(self, /) -> None: ...
    @typing.overload
    def combinedValue(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /) -> PySide6.QtCore.QByteArray: ...
    @typing.overload
    def combinedValue(self, name: str, /) -> PySide6.QtCore.QByteArray: ...
    @typing.overload
    def contains(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /) -> bool: ...
    @typing.overload
    def contains(self, name: str, /) -> bool: ...
    @staticmethod
    def fromListOfPairs(headers: collections.abc.Sequence[typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]], /) -> PySide6.QtNetwork.QHttpHeaders: ...
    @staticmethod
    def fromMultiHash(headers: typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray], /) -> PySide6.QtNetwork.QHttpHeaders: ...
    @staticmethod
    def fromMultiMap(headers: typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray], /) -> PySide6.QtNetwork.QHttpHeaders: ...
    @typing.overload
    def insert(self, i: int, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, value: str, /) -> bool: ...
    @typing.overload
    def insert(self, i: int, name: str, value: str, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def nameAt(self, i: int, /) -> str: ...
    @typing.overload
    def removeAll(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /) -> None: ...
    @typing.overload
    def removeAll(self, name: str, /) -> None: ...
    def removeAt(self, i: int, /) -> None: ...
    @typing.overload
    def replace(self, i: int, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, newValue: str, /) -> bool: ...
    @typing.overload
    def replace(self, i: int, name: str, newValue: str, /) -> bool: ...
    @typing.overload
    def replaceOrAppend(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, newValue: str, /) -> bool: ...
    @typing.overload
    def replaceOrAppend(self, name: str, newValue: str, /) -> bool: ...
    def reserve(self, size: int, /) -> None: ...
    def size(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def toListOfPairs(self, /) -> typing.List[typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]]: ...
    def toMultiHash(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]: ...
    def toMultiMap(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]: ...
    @typing.overload
    def value(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /, defaultValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> PySide6.QtCore.QByteArray: ...
    @typing.overload
    def value(self, name: str, /, defaultValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> PySide6.QtCore.QByteArray: ...
    def valueAt(self, i: int, /) -> PySide6.QtCore.QByteArray: ...
    @typing.overload
    def values(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    @typing.overload
    def values(self, name: str, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    @staticmethod
    def wellKnownHeaderName(name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, /) -> PySide6.QtCore.QByteArray: ...


class QHttpMultiPart(PySide6.QtCore.QObject):

    class ContentType(enum.Enum):

        MixedType                 = ...  # 0x0
        RelatedType               = ...  # 0x1
        FormDataType              = ...  # 0x2
        AlternativeType           = ...  # 0x3


    @typing.overload
    def __init__(self, contentType: PySide6.QtNetwork.QHttpMultiPart.ContentType, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def append(self, httpPart: PySide6.QtNetwork.QHttpPart, /) -> None: ...
    def boundary(self, /) -> PySide6.QtCore.QByteArray: ...
    def setBoundary(self, boundary: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setContentType(self, contentType: PySide6.QtNetwork.QHttpMultiPart.ContentType, /) -> None: ...


class QHttpPart(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QHttpPart, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QHttpPart, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QHttpPart, /) -> bool: ...
    def setBody(self, body: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setBodyDevice(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def setHeader(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, value: typing.Any, /) -> None: ...
    def setRawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, headerValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QHttpPart, /) -> None: ...


class QIPv6Address(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QIPv6Address: PySide6.QtNetwork.QIPv6Address, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...


class QIntList: ...


class QLocalServer(PySide6.QtCore.QObject):

    newConnection            : typing.ClassVar[Signal] = ... # newConnection()

    class SocketOption(enum.Flag):

        NoOptions                 = ...  # 0x0
        UserAccessOption          = ...  # 0x1
        GroupAccessOption         = ...  # 0x2
        OtherAccessOption         = ...  # 0x4
        WorldAccessOption         = ...  # 0x7
        AbstractNamespaceOption   = ...  # 0x8


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, socketOptions: PySide6.QtNetwork.QLocalServer.SocketOption | None = ...) -> None: ...

    def addPendingConnection(self, socket: PySide6.QtNetwork.QLocalSocket, /) -> None: ...
    def close(self, /) -> None: ...
    def errorString(self, /) -> str: ...
    def fullServerName(self, /) -> str: ...
    def hasPendingConnections(self, /) -> bool: ...
    def incomingConnection(self, socketDescriptor: int, /) -> None: ...
    def isListening(self, /) -> bool: ...
    @typing.overload
    def listen(self, name: str, /) -> bool: ...
    @typing.overload
    def listen(self, socketDescriptor: int, /) -> bool: ...
    def listenBacklogSize(self, /) -> int: ...
    def maxPendingConnections(self, /) -> int: ...
    def nextPendingConnection(self, /) -> PySide6.QtNetwork.QLocalSocket: ...
    @staticmethod
    def removeServer(name: str, /) -> bool: ...
    def serverError(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketError: ...
    def serverName(self, /) -> str: ...
    def setListenBacklogSize(self, size: int, /) -> None: ...
    def setMaxPendingConnections(self, numConnections: int, /) -> None: ...
    def setSocketOptions(self, options: PySide6.QtNetwork.QLocalServer.SocketOption, /) -> None: ...
    def socketDescriptor(self, /) -> int: ...
    def socketOptions(self, /) -> PySide6.QtNetwork.QLocalServer.SocketOption: ...
    def waitForNewConnection(self, msec: int, /) -> typing.Tuple[bool, bool]: ...


class QLocalSocket(PySide6.QtCore.QIODevice):

    connected                : typing.ClassVar[Signal] = ... # connected()
    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QLocalSocket::LocalSocketError)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QLocalSocket::LocalSocketState)

    class LocalSocketError(enum.Enum):

        UnknownSocketError        = ...  # -1
        ConnectionRefusedError    = ...  # 0x0
        PeerClosedError           = ...  # 0x1
        ServerNotFoundError       = ...  # 0x2
        SocketAccessError         = ...  # 0x3
        SocketResourceError       = ...  # 0x4
        SocketTimeoutError        = ...  # 0x5
        DatagramTooLargeError     = ...  # 0x6
        ConnectionError           = ...  # 0x7
        UnsupportedSocketOperationError = ...  # 0xa
        OperationError            = ...  # 0x13

    class LocalSocketState(enum.Enum):

        UnconnectedState          = ...  # 0x0
        ConnectingState           = ...  # 0x2
        ConnectedState            = ...  # 0x3
        ClosingState              = ...  # 0x6

    class SocketOption(enum.Flag):

        NoOptions                 = ...  # 0x0
        AbstractNamespaceOption   = ...  # 0x1


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, socketOptions: PySide6.QtNetwork.QLocalServer.SocketOption | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def bytesAvailable(self, /) -> int: ...
    def bytesToWrite(self, /) -> int: ...
    def canReadLine(self, /) -> bool: ...
    def close(self, /) -> None: ...
    @typing.overload
    def connectToServer(self, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToServer(self, name: str, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    def disconnectFromServer(self, /) -> None: ...
    def error(self, /) -> PySide6.QtNetwork.QLocalSocket.LocalSocketError: ...
    def flush(self, /) -> bool: ...
    def fullServerName(self, /) -> str: ...
    def isSequential(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def open(self, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def readBufferSize(self, /) -> int: ...
    def readData(self, arg__2: int, /) -> object: ...
    def readLineData(self, maxSize: int, /) -> object: ...
    def serverName(self, /) -> str: ...
    def setReadBufferSize(self, size: int, /) -> None: ...
    def setServerName(self, name: str, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, /, socketState: PySide6.QtNetwork.QLocalSocket.LocalSocketState = ..., openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def setSocketOptions(self, option: PySide6.QtNetwork.QLocalSocket.SocketOption, /) -> None: ...
    def skipData(self, maxSize: int, /) -> int: ...
    def socketDescriptor(self, /) -> int: ...
    def socketOptions(self, /) -> PySide6.QtNetwork.QLocalSocket.SocketOption: ...
    def state(self, /) -> PySide6.QtNetwork.QLocalSocket.LocalSocketState: ...
    def waitForBytesWritten(self, /, msecs: int = ...) -> bool: ...
    def waitForConnected(self, /, msecs: int = ...) -> bool: ...
    def waitForDisconnected(self, /, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, /, msecs: int = ...) -> bool: ...
    def writeData(self, arg__1: bytes | bytearray | memoryview, arg__2: int, /) -> int: ...


class QNetworkAccessManager(PySide6.QtCore.QObject):

    authenticationRequired   : typing.ClassVar[Signal] = ... # authenticationRequired(QNetworkReply*,QAuthenticator*)
    encrypted                : typing.ClassVar[Signal] = ... # encrypted(QNetworkReply*)
    finished                 : typing.ClassVar[Signal] = ... # finished(QNetworkReply*)
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QNetworkReply*,QSslPreSharedKeyAuthenticator*)
    proxyAuthenticationRequired: typing.ClassVar[Signal] = ... # proxyAuthenticationRequired(QNetworkProxy,QAuthenticator*)
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QNetworkReply*,QList<QSslError>)

    class Operation(enum.Enum):

        UnknownOperation          = ...  # 0x0
        HeadOperation             = ...  # 0x1
        GetOperation              = ...  # 0x2
        PutOperation              = ...  # 0x3
        PostOperation             = ...  # 0x4
        DeleteOperation           = ...  # 0x5
        CustomOperation           = ...  # 0x6


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addStrictTransportSecurityHosts(self, knownHosts: collections.abc.Sequence[PySide6.QtNetwork.QHstsPolicy], /) -> None: ...
    def autoDeleteReplies(self, /) -> bool: ...
    def cache(self, /) -> PySide6.QtNetwork.QAbstractNetworkCache: ...
    def clearAccessCache(self, /) -> None: ...
    def clearConnectionCache(self, /) -> None: ...
    def connectToHost(self, hostName: str, /, port: int = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: str, /, port: int = ..., sslConfiguration: PySide6.QtNetwork.QSslConfiguration = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: str, port: int, sslConfiguration: PySide6.QtNetwork.QSslConfiguration, peerName: str, /) -> None: ...
    def cookieJar(self, /) -> PySide6.QtNetwork.QNetworkCookieJar: ...
    def createRequest(self, op: PySide6.QtNetwork.QNetworkAccessManager.Operation, request: PySide6.QtNetwork.QNetworkRequest, /, outgoingData: PySide6.QtCore.QIODevice | None = ...) -> PySide6.QtNetwork.QNetworkReply: ...
    def deleteResource(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def enableStrictTransportSecurityStore(self, enabled: bool, /, storeDir: str = ...) -> None: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def head(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def isStrictTransportSecurityEnabled(self, /) -> bool: ...
    def isStrictTransportSecurityStoreEnabled(self, /) -> bool: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, multiPart: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def proxy(self, /) -> PySide6.QtNetwork.QNetworkProxy: ...
    def proxyFactory(self, /) -> PySide6.QtNetwork.QNetworkProxyFactory: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, multiPart: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def redirectPolicy(self, /) -> PySide6.QtNetwork.QNetworkRequest.RedirectPolicy: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, verb: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, multiPart: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, verb: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, data: PySide6.QtCore.QIODevice | None = ...) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, verb: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def setAutoDeleteReplies(self, autoDelete: bool, /) -> None: ...
    def setCache(self, cache: PySide6.QtNetwork.QAbstractNetworkCache, /) -> None: ...
    def setCookieJar(self, cookieJar: PySide6.QtNetwork.QNetworkCookieJar, /) -> None: ...
    def setProxy(self, proxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setProxyFactory(self, factory: PySide6.QtNetwork.QNetworkProxyFactory, /) -> None: ...
    def setRedirectPolicy(self, policy: PySide6.QtNetwork.QNetworkRequest.RedirectPolicy, /) -> None: ...
    def setStrictTransportSecurityEnabled(self, enabled: bool, /) -> None: ...
    def setTransferTimeout(self, timeout: int, /) -> None: ...
    def strictTransportSecurityHosts(self, /) -> typing.List[PySide6.QtNetwork.QHstsPolicy]: ...
    def supportedSchemes(self, /) -> typing.List[str]: ...
    def supportedSchemesImplementation(self, /) -> typing.List[str]: ...
    def transferTimeout(self, /) -> int: ...
    def transferTimeoutAsDuration(self, /) -> int: ...


class QNetworkAddressEntry(Shiboken.Object):

    class DnsEligibilityStatus(enum.Enum):

        DnsEligibilityUnknown     = ...  # -1
        DnsIneligible             = ...  # 0x0
        DnsEligible               = ...  # 0x1


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkAddressEntry, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkAddressEntry, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkAddressEntry, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def broadcast(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def clearAddressLifetime(self, /) -> None: ...
    def dnsEligibility(self, /) -> PySide6.QtNetwork.QNetworkAddressEntry.DnsEligibilityStatus: ...
    def ip(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def isLifetimeKnown(self, /) -> bool: ...
    def isPermanent(self, /) -> bool: ...
    def isTemporary(self, /) -> bool: ...
    def netmask(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def preferredLifetime(self, /) -> PySide6.QtCore.QDeadlineTimer: ...
    def prefixLength(self, /) -> int: ...
    def setAddressLifetime(self, preferred: PySide6.QtCore.QDeadlineTimer | PySide6.QtCore.QDeadlineTimer.ForeverConstant | int, validity: PySide6.QtCore.QDeadlineTimer | PySide6.QtCore.QDeadlineTimer.ForeverConstant | int, /) -> None: ...
    def setBroadcast(self, newBroadcast: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def setDnsEligibility(self, status: PySide6.QtNetwork.QNetworkAddressEntry.DnsEligibilityStatus, /) -> None: ...
    def setIp(self, newIp: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def setNetmask(self, newNetmask: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> None: ...
    def setPrefixLength(self, length: int, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkAddressEntry, /) -> None: ...
    def validityLifetime(self, /) -> PySide6.QtCore.QDeadlineTimer: ...


class QNetworkCacheMetaData(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> bool: ...
    def __lshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> bool: ...
    def __rshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def attributes(self, /) -> typing.Dict[PySide6.QtNetwork.QNetworkRequest.Attribute, typing.Any]: ...
    def expirationDate(self, /) -> PySide6.QtCore.QDateTime: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    def isValid(self, /) -> bool: ...
    def lastModified(self, /) -> PySide6.QtCore.QDateTime: ...
    def rawHeaders(self, /) -> typing.List[typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]]: ...
    def saveToDisk(self, /) -> bool: ...
    def setAttributes(self, attributes: typing.Dict[PySide6.QtNetwork.QNetworkRequest.Attribute, typing.Any], /) -> None: ...
    def setExpirationDate(self, dateTime: PySide6.QtCore.QDateTime, /) -> None: ...
    def setHeaders(self, headers: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def setLastModified(self, dateTime: PySide6.QtCore.QDateTime, /) -> None: ...
    def setRawHeaders(self, headers: collections.abc.Sequence[typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]], /) -> None: ...
    def setSaveToDisk(self, allow: bool, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QNetworkCookie(Shiboken.Object):

    class RawForm(enum.Enum):

        NameAndValueOnly          = ...  # 0x0
        Full                      = ...  # 0x1

    class SameSite(enum.Enum):

        Default                   = ...  # 0x0
        None_                     = ...  # 0x1
        Lax                       = ...  # 0x2
        Strict                    = ...  # 0x3


    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkCookie, /) -> None: ...
    @typing.overload
    def __init__(self, /, name: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ..., value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def domain(self, /) -> str: ...
    def expirationDate(self, /) -> PySide6.QtCore.QDateTime: ...
    def hasSameIdentifier(self, other: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def isHttpOnly(self, /) -> bool: ...
    def isSecure(self, /) -> bool: ...
    def isSessionCookie(self, /) -> bool: ...
    def name(self, /) -> PySide6.QtCore.QByteArray: ...
    def normalize(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    @staticmethod
    def parseCookies(cookieString: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[PySide6.QtNetwork.QNetworkCookie]: ...
    def path(self, /) -> str: ...
    def sameSitePolicy(self, /) -> PySide6.QtNetwork.QNetworkCookie.SameSite: ...
    def setDomain(self, domain: str, /) -> None: ...
    def setExpirationDate(self, date: PySide6.QtCore.QDateTime, /) -> None: ...
    def setHttpOnly(self, enable: bool, /) -> None: ...
    def setName(self, cookieName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setPath(self, path: str, /) -> None: ...
    def setSameSitePolicy(self, sameSite: PySide6.QtNetwork.QNetworkCookie.SameSite, /) -> None: ...
    def setSecure(self, enable: bool, /) -> None: ...
    def setValue(self, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkCookie, /) -> None: ...
    def toRawForm(self, /, form: PySide6.QtNetwork.QNetworkCookie.RawForm = ...) -> PySide6.QtCore.QByteArray: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...


class QNetworkCookieJar(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def allCookies(self, /) -> typing.List[PySide6.QtNetwork.QNetworkCookie]: ...
    def cookiesForUrl(self, url: PySide6.QtCore.QUrl | str, /) -> typing.List[PySide6.QtNetwork.QNetworkCookie]: ...
    def deleteCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def insertCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def setAllCookies(self, cookieList: collections.abc.Sequence[PySide6.QtNetwork.QNetworkCookie], /) -> None: ...
    def setCookiesFromUrl(self, cookieList: collections.abc.Sequence[PySide6.QtNetwork.QNetworkCookie], url: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def updateCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, /) -> bool: ...
    def validateCookie(self, cookie: PySide6.QtNetwork.QNetworkCookie, url: PySide6.QtCore.QUrl | str, /) -> bool: ...


class QNetworkDatagram(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkDatagram, /) -> None: ...
    @typing.overload
    def __init__(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, destinationAddress: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress = ..., port: int | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def clear(self, /) -> None: ...
    def data(self, /) -> PySide6.QtCore.QByteArray: ...
    def destinationAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def destinationPort(self, /) -> int: ...
    def hopLimit(self, /) -> int: ...
    def interfaceIndex(self, /) -> int: ...
    def isNull(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def makeReply(self, payload: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkDatagram: ...
    def senderAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def senderPort(self, /) -> int: ...
    def setData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setDestination(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /) -> None: ...
    def setHopLimit(self, count: int, /) -> None: ...
    def setInterfaceIndex(self, index: int, /) -> None: ...
    def setSender(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ...) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkDatagram | PySide6.QtCore.QByteArray, /) -> None: ...


class QNetworkDiskCache(PySide6.QtNetwork.QAbstractNetworkCache):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def cacheDirectory(self, /) -> str: ...
    def cacheSize(self, /) -> int: ...
    def clear(self, /) -> None: ...
    def data(self, url: PySide6.QtCore.QUrl | str, /) -> PySide6.QtCore.QIODevice: ...
    def expire(self, /) -> int: ...
    def fileMetaData(self, fileName: str, /) -> PySide6.QtNetwork.QNetworkCacheMetaData: ...
    def insert(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def maximumCacheSize(self, /) -> int: ...
    def metaData(self, url: PySide6.QtCore.QUrl | str, /) -> PySide6.QtNetwork.QNetworkCacheMetaData: ...
    def prepare(self, metaData: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> PySide6.QtCore.QIODevice: ...
    def remove(self, url: PySide6.QtCore.QUrl | str, /) -> bool: ...
    def setCacheDirectory(self, cacheDir: str, /) -> None: ...
    def setMaximumCacheSize(self, size: int, /) -> None: ...
    def updateMetaData(self, metaData: PySide6.QtNetwork.QNetworkCacheMetaData, /) -> None: ...


class QNetworkInformation(PySide6.QtCore.QObject):

    isBehindCaptivePortalChanged: typing.ClassVar[Signal] = ... # isBehindCaptivePortalChanged(bool)
    isMeteredChanged         : typing.ClassVar[Signal] = ... # isMeteredChanged(bool)
    reachabilityChanged      : typing.ClassVar[Signal] = ... # reachabilityChanged(QNetworkInformation::Reachability)
    transportMediumChanged   : typing.ClassVar[Signal] = ... # transportMediumChanged(QNetworkInformation::TransportMedium)

    class Feature(enum.Flag):

        Reachability              = ...  # 0x1
        CaptivePortal             = ...  # 0x2
        TransportMedium           = ...  # 0x4
        Metered                   = ...  # 0x8

    class Reachability(enum.Enum):

        Unknown                   = ...  # 0x0
        Disconnected              = ...  # 0x1
        Local                     = ...  # 0x2
        Site                      = ...  # 0x3
        Online                    = ...  # 0x4

    class TransportMedium(enum.Enum):

        Unknown                   = ...  # 0x0
        Ethernet                  = ...  # 0x1
        Cellular                  = ...  # 0x2
        WiFi                      = ...  # 0x3
        Bluetooth                 = ...  # 0x4


    @staticmethod
    def availableBackends() -> typing.List[str]: ...
    def backendName(self, /) -> str: ...
    @staticmethod
    def instance() -> PySide6.QtNetwork.QNetworkInformation: ...
    def isBehindCaptivePortal(self, /) -> bool: ...
    def isMetered(self, /) -> bool: ...
    @typing.overload
    @staticmethod
    def load(features: PySide6.QtNetwork.QNetworkInformation.Feature, /) -> bool: ...
    @typing.overload
    @staticmethod
    def load(backend: str, /) -> bool: ...
    @staticmethod
    def loadBackendByFeatures(features: PySide6.QtNetwork.QNetworkInformation.Feature, /) -> bool: ...
    @staticmethod
    def loadBackendByName(backend: str, /) -> bool: ...
    @staticmethod
    def loadDefaultBackend() -> bool: ...
    def reachability(self, /) -> PySide6.QtNetwork.QNetworkInformation.Reachability: ...
    def supportedFeatures(self, /) -> PySide6.QtNetwork.QNetworkInformation.Feature: ...
    def supports(self, features: PySide6.QtNetwork.QNetworkInformation.Feature, /) -> bool: ...
    def transportMedium(self, /) -> PySide6.QtNetwork.QNetworkInformation.TransportMedium: ...


class QNetworkInterface(Shiboken.Object):

    class InterfaceFlag(enum.Flag):

        IsUp                      = ...  # 0x1
        IsRunning                 = ...  # 0x2
        CanBroadcast              = ...  # 0x4
        IsLoopBack                = ...  # 0x8
        IsPointToPoint            = ...  # 0x10
        CanMulticast              = ...  # 0x20

    class InterfaceType(enum.Enum):

        Unknown                   = ...  # 0x0
        Loopback                  = ...  # 0x1
        Virtual                   = ...  # 0x2
        Ethernet                  = ...  # 0x3
        Slip                      = ...  # 0x4
        CanBus                    = ...  # 0x5
        Ppp                       = ...  # 0x6
        Fddi                      = ...  # 0x7
        Ieee80211                 = ...  # 0x8
        Wifi                      = ...  # 0x8
        Phonet                    = ...  # 0x9
        Ieee802154                = ...  # 0xa
        SixLoWPAN                 = ...  # 0xb
        Ieee80216                 = ...  # 0xc
        Ieee1394                  = ...  # 0xd


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkInterface, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    def addressEntries(self, /) -> typing.List[PySide6.QtNetwork.QNetworkAddressEntry]: ...
    @staticmethod
    def allAddresses() -> typing.List[PySide6.QtNetwork.QHostAddress]: ...
    @staticmethod
    def allInterfaces() -> typing.List[PySide6.QtNetwork.QNetworkInterface]: ...
    def flags(self, /) -> PySide6.QtNetwork.QNetworkInterface.InterfaceFlag: ...
    def hardwareAddress(self, /) -> str: ...
    def humanReadableName(self, /) -> str: ...
    def index(self, /) -> int: ...
    @staticmethod
    def interfaceFromIndex(index: int, /) -> PySide6.QtNetwork.QNetworkInterface: ...
    @staticmethod
    def interfaceFromName(name: str, /) -> PySide6.QtNetwork.QNetworkInterface: ...
    @staticmethod
    def interfaceIndexFromName(name: str, /) -> int: ...
    @staticmethod
    def interfaceNameFromIndex(index: int, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def maximumTransmissionUnit(self, /) -> int: ...
    def name(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkInterface, /) -> None: ...
    def type(self, /) -> PySide6.QtNetwork.QNetworkInterface.InterfaceType: ...


class QNetworkProxy(Shiboken.Object):

    class Capability(enum.Flag):

        TunnelingCapability       = ...  # 0x1
        ListeningCapability       = ...  # 0x2
        UdpTunnelingCapability    = ...  # 0x4
        CachingCapability         = ...  # 0x8
        HostNameLookupCapability  = ...  # 0x10
        SctpTunnelingCapability   = ...  # 0x20
        SctpListeningCapability   = ...  # 0x40

    class ProxyType(enum.Enum):

        DefaultProxy              = ...  # 0x0
        Socks5Proxy               = ...  # 0x1
        NoProxy                   = ...  # 0x2
        HttpProxy                 = ...  # 0x3
        HttpCachingProxy          = ...  # 0x4
        FtpCachingProxy           = ...  # 0x5


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtNetwork.QNetworkProxy.ProxyType, /, hostName: str = ..., port: int | None = ..., user: str = ..., password: str = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkProxy, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    @staticmethod
    def applicationProxy() -> PySide6.QtNetwork.QNetworkProxy: ...
    def capabilities(self, /) -> PySide6.QtNetwork.QNetworkProxy.Capability: ...
    def hasRawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def header(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, /) -> typing.Any: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    def hostName(self, /) -> str: ...
    def isCachingProxy(self, /) -> bool: ...
    def isTransparentProxy(self, /) -> bool: ...
    def password(self, /) -> str: ...
    def port(self, /) -> int: ...
    def rawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtCore.QByteArray: ...
    def rawHeaderList(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    @staticmethod
    def setApplicationProxy(proxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setCapabilities(self, capab: PySide6.QtNetwork.QNetworkProxy.Capability, /) -> None: ...
    def setHeader(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, value: typing.Any, /) -> None: ...
    def setHeaders(self, newHeaders: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def setHostName(self, hostName: str, /) -> None: ...
    def setPassword(self, password: str, /) -> None: ...
    def setPort(self, port: int, /) -> None: ...
    def setRawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setType(self, type: PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setUser(self, userName: str, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def type(self, /) -> PySide6.QtNetwork.QNetworkProxy.ProxyType: ...
    def user(self, /) -> str: ...


class QNetworkProxyFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    @staticmethod
    def proxyForQuery(query: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int, /) -> typing.List[PySide6.QtNetwork.QNetworkProxy]: ...
    def queryProxy(self, /, query: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int = ...) -> typing.List[PySide6.QtNetwork.QNetworkProxy]: ...
    @staticmethod
    def setApplicationProxyFactory(factory: PySide6.QtNetwork.QNetworkProxyFactory, /) -> None: ...
    @staticmethod
    def setUseSystemConfiguration(enable: bool, /) -> None: ...
    @staticmethod
    def systemProxyForQuery(query: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int = ...) -> typing.List[PySide6.QtNetwork.QNetworkProxy]: ...
    @staticmethod
    def usesSystemConfiguration() -> bool: ...


class QNetworkProxyQuery(Shiboken.Object):

    class QueryType(enum.Enum):

        TcpSocket                 = ...  # 0x0
        UdpSocket                 = ...  # 0x1
        SctpSocket                = ...  # 0x2
        TcpServer                 = ...  # 0x64
        UrlRequest                = ...  # 0x65
        SctpServer                = ...  # 0x66


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkProxyQuery, /) -> None: ...
    @typing.overload
    def __init__(self, hostname: str, port: int, /, protocolTag: str = ..., queryType: PySide6.QtNetwork.QNetworkProxyQuery.QueryType = ...) -> None: ...
    @typing.overload
    def __init__(self, requestUrl: PySide6.QtCore.QUrl | str, /, queryType: PySide6.QtNetwork.QNetworkProxyQuery.QueryType = ...) -> None: ...
    @typing.overload
    def __init__(self, bindPort: int, /, protocolTag: str = ..., queryType: PySide6.QtNetwork.QNetworkProxyQuery.QueryType = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def localPort(self, /) -> int: ...
    def peerHostName(self, /) -> str: ...
    def peerPort(self, /) -> int: ...
    def protocolTag(self, /) -> str: ...
    def queryType(self, /) -> PySide6.QtNetwork.QNetworkProxyQuery.QueryType: ...
    def setLocalPort(self, port: int, /) -> None: ...
    def setPeerHostName(self, hostname: str, /) -> None: ...
    def setPeerPort(self, port: int, /) -> None: ...
    def setProtocolTag(self, protocolTag: str, /) -> None: ...
    def setQueryType(self, type: PySide6.QtNetwork.QNetworkProxyQuery.QueryType, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkProxyQuery | PySide6.QtCore.QUrl | int, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QNetworkReply(PySide6.QtCore.QIODevice):

    downloadProgress         : typing.ClassVar[Signal] = ... # downloadProgress(qlonglong,qlonglong)
    encrypted                : typing.ClassVar[Signal] = ... # encrypted()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QNetworkReply::NetworkError)
    finished                 : typing.ClassVar[Signal] = ... # finished()
    metaDataChanged          : typing.ClassVar[Signal] = ... # metaDataChanged()
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator*)
    redirectAllowed          : typing.ClassVar[Signal] = ... # redirectAllowed()
    redirected               : typing.ClassVar[Signal] = ... # redirected(QUrl)
    requestSent              : typing.ClassVar[Signal] = ... # requestSent()
    socketStartedConnecting  : typing.ClassVar[Signal] = ... # socketStartedConnecting()
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QList<QSslError>)
    uploadProgress           : typing.ClassVar[Signal] = ... # uploadProgress(qlonglong,qlonglong)

    class NetworkError(enum.Enum):

        NoError                   = ...  # 0x0
        ConnectionRefusedError    = ...  # 0x1
        RemoteHostClosedError     = ...  # 0x2
        HostNotFoundError         = ...  # 0x3
        TimeoutError              = ...  # 0x4
        OperationCanceledError    = ...  # 0x5
        SslHandshakeFailedError   = ...  # 0x6
        TemporaryNetworkFailureError = ...  # 0x7
        NetworkSessionFailedError = ...  # 0x8
        BackgroundRequestNotAllowedError = ...  # 0x9
        TooManyRedirectsError     = ...  # 0xa
        InsecureRedirectError     = ...  # 0xb
        UnknownNetworkError       = ...  # 0x63
        ProxyConnectionRefusedError = ...  # 0x65
        ProxyConnectionClosedError = ...  # 0x66
        ProxyNotFoundError        = ...  # 0x67
        ProxyTimeoutError         = ...  # 0x68
        ProxyAuthenticationRequiredError = ...  # 0x69
        UnknownProxyError         = ...  # 0xc7
        ContentAccessDenied       = ...  # 0xc9
        ContentOperationNotPermittedError = ...  # 0xca
        ContentNotFoundError      = ...  # 0xcb
        AuthenticationRequiredError = ...  # 0xcc
        ContentReSendError        = ...  # 0xcd
        ContentConflictError      = ...  # 0xce
        ContentGoneError          = ...  # 0xcf
        UnknownContentError       = ...  # 0x12b
        ProtocolUnknownError      = ...  # 0x12d
        ProtocolInvalidOperationError = ...  # 0x12e
        ProtocolFailure           = ...  # 0x18f
        InternalServerError       = ...  # 0x191
        OperationNotImplementedError = ...  # 0x192
        ServiceUnavailableError   = ...  # 0x193
        UnknownServerError        = ...  # 0x1f3


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def attribute(self, code: PySide6.QtNetwork.QNetworkRequest.Attribute, /) -> typing.Any: ...
    def close(self, /) -> None: ...
    def error(self, /) -> PySide6.QtNetwork.QNetworkReply.NetworkError: ...
    def hasRawHeader(self, headerName: str, /) -> bool: ...
    def header(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, /) -> typing.Any: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    @typing.overload
    def ignoreSslErrors(self, /) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: collections.abc.Sequence[PySide6.QtNetwork.QSslError], /) -> None: ...
    def ignoreSslErrorsImplementation(self, arg__1: collections.abc.Sequence[PySide6.QtNetwork.QSslError], /) -> None: ...
    def isFinished(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def isSequential(self, /) -> bool: ...
    def manager(self, /) -> PySide6.QtNetwork.QNetworkAccessManager: ...
    def operation(self, /) -> PySide6.QtNetwork.QNetworkAccessManager.Operation: ...
    def rawHeader(self, headerName: str, /) -> PySide6.QtCore.QByteArray: ...
    def rawHeaderList(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def rawHeaderPairs(self, /) -> typing.List[typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtCore.QByteArray]]: ...
    def readBufferSize(self, /) -> int: ...
    def request(self, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    def setAttribute(self, code: PySide6.QtNetwork.QNetworkRequest.Attribute, value: typing.Any, /) -> None: ...
    def setError(self, errorCode: PySide6.QtNetwork.QNetworkReply.NetworkError, errorString: str, /) -> None: ...
    def setFinished(self, arg__1: bool, /) -> None: ...
    def setHeader(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, value: typing.Any, /) -> None: ...
    def setHeaders(self, newHeaders: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def setOperation(self, operation: PySide6.QtNetwork.QNetworkAccessManager.Operation, /) -> None: ...
    def setRawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setReadBufferSize(self, size: int, /) -> None: ...
    def setRequest(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> None: ...
    def setSslConfiguration(self, configuration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setSslConfigurationImplementation(self, arg__1: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setWellKnownHeader(self, name: PySide6.QtNetwork.QHttpHeaders.WellKnownHeader, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def sslConfigurationImplementation(self, arg__1: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...
    def writeData(self, data: bytes | bytearray | memoryview, len: int, /) -> int: ...


class QNetworkRequest(Shiboken.Object):

    class Attribute(enum.Enum):

        HttpStatusCodeAttribute   = ...  # 0x0
        HttpReasonPhraseAttribute = ...  # 0x1
        RedirectionTargetAttribute = ...  # 0x2
        ConnectionEncryptedAttribute = ...  # 0x3
        CacheLoadControlAttribute = ...  # 0x4
        CacheSaveControlAttribute = ...  # 0x5
        SourceIsFromCacheAttribute = ...  # 0x6
        DoNotBufferUploadDataAttribute = ...  # 0x7
        HttpPipeliningAllowedAttribute = ...  # 0x8
        HttpPipeliningWasUsedAttribute = ...  # 0x9
        CustomVerbAttribute       = ...  # 0xa
        CookieLoadControlAttribute = ...  # 0xb
        AuthenticationReuseAttribute = ...  # 0xc
        CookieSaveControlAttribute = ...  # 0xd
        MaximumDownloadBufferSizeAttribute = ...  # 0xe
        DownloadBufferAttribute   = ...  # 0xf
        SynchronousRequestAttribute = ...  # 0x10
        BackgroundRequestAttribute = ...  # 0x11
        EmitAllUploadProgressSignalsAttribute = ...  # 0x12
        Http2AllowedAttribute     = ...  # 0x13
        Http2WasUsedAttribute     = ...  # 0x14
        OriginalContentLengthAttribute = ...  # 0x15
        RedirectPolicyAttribute   = ...  # 0x16
        Http2DirectAttribute      = ...  # 0x17
        ResourceTypeAttribute     = ...  # 0x18
        AutoDeleteReplyOnFinishAttribute = ...  # 0x19
        ConnectionCacheExpiryTimeoutSecondsAttribute = ...  # 0x1a
        Http2CleartextAllowedAttribute = ...  # 0x1b
        UseCredentialsAttribute   = ...  # 0x1c
        FullLocalServerNameAttribute = ...  # 0x1d
        User                      = ...  # 0x3e8
        UserMax                   = ...  # 0x7fff

    class CacheLoadControl(enum.Enum):

        AlwaysNetwork             = ...  # 0x0
        PreferNetwork             = ...  # 0x1
        PreferCache               = ...  # 0x2
        AlwaysCache               = ...  # 0x3

    class KnownHeaders(enum.Enum):

        ContentTypeHeader         = ...  # 0x0
        ContentLengthHeader       = ...  # 0x1
        LocationHeader            = ...  # 0x2
        LastModifiedHeader        = ...  # 0x3
        CookieHeader              = ...  # 0x4
        SetCookieHeader           = ...  # 0x5
        ContentDispositionHeader  = ...  # 0x6
        UserAgentHeader           = ...  # 0x7
        ServerHeader              = ...  # 0x8
        IfModifiedSinceHeader     = ...  # 0x9
        ETagHeader                = ...  # 0xa
        IfMatchHeader             = ...  # 0xb
        IfNoneMatchHeader         = ...  # 0xc
        NumKnownHeaders           = ...  # 0xd

    class LoadControl(enum.Enum):

        Automatic                 = ...  # 0x0
        Manual                    = ...  # 0x1

    class Priority(enum.Enum):

        HighPriority              = ...  # 0x1
        NormalPriority            = ...  # 0x3
        LowPriority               = ...  # 0x5

    class RedirectPolicy(enum.Enum):

        ManualRedirectPolicy      = ...  # 0x0
        NoLessSafeRedirectPolicy  = ...  # 0x1
        SameOriginRedirectPolicy  = ...  # 0x2
        UserVerifiedRedirectPolicy = ...  # 0x3

    class TransferTimeoutConstant(enum.Enum):

        DefaultTransferTimeoutConstant = ...  # 0x7530


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkRequest, /) -> None: ...
    @typing.overload
    def __init__(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QNetworkRequest, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QNetworkRequest, /) -> bool: ...
    def attribute(self, code: PySide6.QtNetwork.QNetworkRequest.Attribute, /, defaultValue: typing.Any = ...) -> typing.Any: ...
    def decompressedSafetyCheckThreshold(self, /) -> int: ...
    def hasRawHeader(self, headerName: str, /) -> bool: ...
    def header(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, /) -> typing.Any: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    def http1Configuration(self, /) -> PySide6.QtNetwork.QHttp1Configuration: ...
    def http2Configuration(self, /) -> PySide6.QtNetwork.QHttp2Configuration: ...
    def maximumRedirectsAllowed(self, /) -> int: ...
    def originatingObject(self, /) -> PySide6.QtCore.QObject: ...
    def peerVerifyName(self, /) -> str: ...
    def priority(self, /) -> PySide6.QtNetwork.QNetworkRequest.Priority: ...
    def rawHeader(self, headerName: str, /) -> PySide6.QtCore.QByteArray: ...
    def rawHeaderList(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def setAttribute(self, code: PySide6.QtNetwork.QNetworkRequest.Attribute, value: typing.Any, /) -> None: ...
    def setDecompressedSafetyCheckThreshold(self, threshold: int, /) -> None: ...
    def setHeader(self, header: PySide6.QtNetwork.QNetworkRequest.KnownHeaders, value: typing.Any, /) -> None: ...
    def setHeaders(self, newHeaders: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def setHttp1Configuration(self, configuration: PySide6.QtNetwork.QHttp1Configuration, /) -> None: ...
    def setHttp2Configuration(self, configuration: PySide6.QtNetwork.QHttp2Configuration, /) -> None: ...
    def setMaximumRedirectsAllowed(self, maximumRedirectsAllowed: int, /) -> None: ...
    def setOriginatingObject(self, object: PySide6.QtCore.QObject, /) -> None: ...
    def setPeerVerifyName(self, peerName: str, /) -> None: ...
    def setPriority(self, priority: PySide6.QtNetwork.QNetworkRequest.Priority, /) -> None: ...
    def setRawHeader(self, headerName: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setSslConfiguration(self, configuration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setTransferTimeout(self, timeout: int, /) -> None: ...
    def setUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkRequest, /) -> None: ...
    def transferTimeout(self, /) -> int: ...
    def transferTimeoutAsDuration(self, /) -> int: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QNetworkRequestFactory(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QNetworkRequestFactory, /) -> None: ...
    @typing.overload
    def __init__(self, baseUrl: PySide6.QtCore.QUrl | str, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @typing.overload
    def attribute(self, attribute: PySide6.QtNetwork.QNetworkRequest.Attribute, /) -> typing.Any: ...
    @typing.overload
    def attribute(self, attribute: PySide6.QtNetwork.QNetworkRequest.Attribute, defaultValue: typing.Any, /) -> typing.Any: ...
    def baseUrl(self, /) -> PySide6.QtCore.QUrl: ...
    def bearerToken(self, /) -> PySide6.QtCore.QByteArray: ...
    def clearAttribute(self, attribute: PySide6.QtNetwork.QNetworkRequest.Attribute, /) -> None: ...
    def clearAttributes(self, /) -> None: ...
    def clearBearerToken(self, /) -> None: ...
    def clearCommonHeaders(self, /) -> None: ...
    def clearPassword(self, /) -> None: ...
    def clearQueryParameters(self, /) -> None: ...
    def clearUserName(self, /) -> None: ...
    def commonHeaders(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    @typing.overload
    def createRequest(self, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    @typing.overload
    def createRequest(self, query: PySide6.QtCore.QUrlQuery, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    @typing.overload
    def createRequest(self, path: str, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    @typing.overload
    def createRequest(self, path: str, query: PySide6.QtCore.QUrlQuery, /) -> PySide6.QtNetwork.QNetworkRequest: ...
    def password(self, /) -> str: ...
    def priority(self, /) -> PySide6.QtNetwork.QNetworkRequest.Priority: ...
    def queryParameters(self, /) -> PySide6.QtCore.QUrlQuery: ...
    def setAttribute(self, attribute: PySide6.QtNetwork.QNetworkRequest.Attribute, value: typing.Any, /) -> None: ...
    def setBaseUrl(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setBearerToken(self, token: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setCommonHeaders(self, headers: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def setPassword(self, password: str, /) -> None: ...
    def setPriority(self, priority: PySide6.QtNetwork.QNetworkRequest.Priority, /) -> None: ...
    def setQueryParameters(self, query: PySide6.QtCore.QUrlQuery, /) -> None: ...
    def setSslConfiguration(self, configuration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setTransferTimeout(self, timeout: int, /) -> None: ...
    def setUserName(self, userName: str, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def swap(self, other: PySide6.QtNetwork.QNetworkRequestFactory, /) -> None: ...
    def transferTimeout(self, /) -> int: ...
    def userName(self, /) -> str: ...


class QOcspCertificateStatus(enum.Enum):

    Good                      = ...  # 0x0
    Revoked                   = ...  # 0x1
    Unknown                   = ...  # 0x2


class QOcspResponse(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QOcspResponse, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QOcspResponse, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QOcspResponse, /) -> bool: ...
    def certificateStatus(self, /) -> PySide6.QtNetwork.QOcspCertificateStatus: ...
    def responder(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def revocationReason(self, /) -> PySide6.QtNetwork.QOcspRevocationReason: ...
    def subject(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def swap(self, other: PySide6.QtNetwork.QOcspResponse, /) -> None: ...


class QOcspRevocationReason(enum.Enum):

    None_                     = ...  # -1
    Unspecified               = ...  # 0x0
    KeyCompromise             = ...  # 0x1
    CACompromise              = ...  # 0x2
    AffiliationChanged        = ...  # 0x3
    Superseded                = ...  # 0x4
    CessationOfOperation      = ...  # 0x5
    CertificateHold           = ...  # 0x6
    RemoveFromCRL             = ...  # 0x7


class QPasswordDigestor(Shiboken.Object):
    @staticmethod
    def deriveKeyPbkdf1(algorithm: PySide6.QtCore.QCryptographicHash.Algorithm, password: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, salt: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, iterations: int, dkLen: int, /) -> PySide6.QtCore.QByteArray: ...
    @staticmethod
    def deriveKeyPbkdf2(algorithm: PySide6.QtCore.QCryptographicHash.Algorithm, password: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, salt: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, iterations: int, dkLen: int, /) -> PySide6.QtCore.QByteArray: ...


class QRestAccessManager(PySide6.QtCore.QObject):

    def __init__(self, manager: PySide6.QtNetwork.QNetworkAccessManager, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def deleteResource(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def deleteResource(self, request: PySide6.QtNetwork.QNetworkRequest, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def get(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def head(self, request: PySide6.QtNetwork.QNetworkRequest, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def head(self, request: PySide6.QtNetwork.QNetworkRequest, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    def networkAccessManager(self, /) -> PySide6.QtNetwork.QNetworkAccessManager: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def patch(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtNetwork.QHttpMultiPart, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def post(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QIODevice, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtNetwork.QHttpMultiPart, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QJsonDocument, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: typing.Dict[str, typing.Any], context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def put(self, request: PySide6.QtNetwork.QNetworkRequest, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QIODevice, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QIODevice, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtNetwork.QHttpMultiPart, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtNetwork.QHttpMultiPart, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNetwork.QNetworkReply: ...
    @typing.overload
    def sendCustomRequest(self, request: PySide6.QtNetwork.QNetworkRequest, method: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, context: PySide6.QtCore.QObject, slot: collections.abc.Callable[..., typing.Any], /) -> PySide6.QtNetwork.QNetworkReply: ...


class QRestReply(Shiboken.Object):

    def __init__(self, reply: PySide6.QtNetwork.QNetworkReply, /) -> None: ...

    def error(self, /) -> PySide6.QtNetwork.QNetworkReply.NetworkError: ...
    def errorString(self, /) -> str: ...
    def hasError(self, /) -> bool: ...
    def httpStatus(self, /) -> int: ...
    def isHttpStatusSuccess(self, /) -> bool: ...
    def isSuccess(self, /) -> bool: ...
    def networkReply(self, /) -> PySide6.QtNetwork.QNetworkReply: ...
    def readBody(self, /) -> PySide6.QtCore.QByteArray: ...
    def readJson(self, /) -> typing.Tuple[typing.Optional[PySide6.QtCore.QJsonDocument], PySide6.QtCore.QJsonParseError]: ...
    def readText(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QRestReply, /) -> None: ...


class QSsl(Shiboken.Object):

    class AlertLevel(enum.Enum):

        Warning                   = ...  # 0x0
        Fatal                     = ...  # 0x1
        Unknown                   = ...  # 0x2

    class AlertType(enum.Enum):

        CloseNotify               = ...  # 0x0
        UnexpectedMessage         = ...  # 0xa
        BadRecordMac              = ...  # 0x14
        RecordOverflow            = ...  # 0x16
        DecompressionFailure      = ...  # 0x1e
        HandshakeFailure          = ...  # 0x28
        NoCertificate             = ...  # 0x29
        BadCertificate            = ...  # 0x2a
        UnsupportedCertificate    = ...  # 0x2b
        CertificateRevoked        = ...  # 0x2c
        CertificateExpired        = ...  # 0x2d
        CertificateUnknown        = ...  # 0x2e
        IllegalParameter          = ...  # 0x2f
        UnknownCa                 = ...  # 0x30
        AccessDenied              = ...  # 0x31
        DecodeError               = ...  # 0x32
        DecryptError              = ...  # 0x33
        ExportRestriction         = ...  # 0x3c
        ProtocolVersion           = ...  # 0x46
        InsufficientSecurity      = ...  # 0x47
        InternalError             = ...  # 0x50
        InappropriateFallback     = ...  # 0x56
        UserCancelled             = ...  # 0x5a
        NoRenegotiation           = ...  # 0x64
        MissingExtension          = ...  # 0x6d
        UnsupportedExtension      = ...  # 0x6e
        CertificateUnobtainable   = ...  # 0x6f
        UnrecognizedName          = ...  # 0x70
        BadCertificateStatusResponse = ...  # 0x71
        BadCertificateHashValue   = ...  # 0x72
        UnknownPskIdentity        = ...  # 0x73
        CertificateRequired       = ...  # 0x74
        NoApplicationProtocol     = ...  # 0x78
        UnknownAlertMessage       = ...  # 0xff

    class AlternativeNameEntryType(enum.Enum):

        EmailEntry                = ...  # 0x0
        DnsEntry                  = ...  # 0x1
        IpAddressEntry            = ...  # 0x2

    class EncodingFormat(enum.Enum):

        Pem                       = ...  # 0x0
        Der                       = ...  # 0x1

    class ImplementedClass(enum.Enum):

        Key                       = ...  # 0x0
        Certificate               = ...  # 0x1
        Socket                    = ...  # 0x2
        DiffieHellman             = ...  # 0x3
        EllipticCurve             = ...  # 0x4
        Dtls                      = ...  # 0x5
        DtlsCookie                = ...  # 0x6

    class KeyAlgorithm(enum.Enum):

        Opaque                    = ...  # 0x0
        Rsa                       = ...  # 0x1
        Dsa                       = ...  # 0x2
        Ec                        = ...  # 0x3
        Dh                        = ...  # 0x4

    class KeyType(enum.Enum):

        PrivateKey                = ...  # 0x0
        PublicKey                 = ...  # 0x1

    class SslOption(enum.Flag):

        SslOptionDisableEmptyFragments = ...  # 0x1
        SslOptionDisableSessionTickets = ...  # 0x2
        SslOptionDisableCompression = ...  # 0x4
        SslOptionDisableServerNameIndication = ...  # 0x8
        SslOptionDisableLegacyRenegotiation = ...  # 0x10
        SslOptionDisableSessionSharing = ...  # 0x20
        SslOptionDisableSessionPersistence = ...  # 0x40
        SslOptionDisableServerCipherPreference = ...  # 0x80

    class SslProtocol(enum.Enum):

        UnknownProtocol           = ...  # -1
        TlsV1_0                   = ...  # 0x0
        TlsV1_1                   = ...  # 0x1
        TlsV1_2                   = ...  # 0x2
        AnyProtocol               = ...  # 0x3
        SecureProtocols           = ...  # 0x4
        TlsV1_0OrLater            = ...  # 0x5
        TlsV1_1OrLater            = ...  # 0x6
        TlsV1_2OrLater            = ...  # 0x7
        DtlsV1_0                  = ...  # 0x8
        DtlsV1_0OrLater           = ...  # 0x9
        DtlsV1_2                  = ...  # 0xa
        DtlsV1_2OrLater           = ...  # 0xb
        TlsV1_3                   = ...  # 0xc
        TlsV1_3OrLater            = ...  # 0xd

    class SupportedFeature(enum.Enum):

        CertificateVerification   = ...  # 0x0
        ClientSideAlpn            = ...  # 0x1
        ServerSideAlpn            = ...  # 0x2
        Ocsp                      = ...  # 0x3
        Psk                       = ...  # 0x4
        SessionTicket             = ...  # 0x5
        Alerts                    = ...  # 0x6


class QSslCertificate(Shiboken.Object):

    class PatternSyntax(enum.Enum):

        RegularExpression         = ...  # 0x0
        Wildcard                  = ...  # 0x1
        FixedString               = ...  # 0x2

    class SubjectInfo(enum.Enum):

        Organization              = ...  # 0x0
        CommonName                = ...  # 0x1
        LocalityName              = ...  # 0x2
        OrganizationalUnitName    = ...  # 0x3
        CountryName               = ...  # 0x4
        StateOrProvinceName       = ...  # 0x5
        DistinguishedNameQualifier = ...  # 0x6
        SerialNumber              = ...  # 0x7
        EmailAddress              = ...  # 0x8


    @typing.overload
    def __init__(self, device: PySide6.QtCore.QIODevice, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslCertificate, /) -> None: ...
    @typing.overload
    def __init__(self, /, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ..., format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, other: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def clear(self, /) -> None: ...
    def digest(self, /, algorithm: PySide6.QtCore.QCryptographicHash.Algorithm = ...) -> PySide6.QtCore.QByteArray: ...
    def effectiveDate(self, /) -> PySide6.QtCore.QDateTime: ...
    def expiryDate(self, /) -> PySide6.QtCore.QDateTime: ...
    def extensions(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificateExtension]: ...
    @staticmethod
    def fromData(data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    @staticmethod
    def fromDevice(device: PySide6.QtCore.QIODevice, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    @staticmethod
    def fromPath(path: str, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ..., syntax: PySide6.QtNetwork.QSslCertificate.PatternSyntax = ...) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def handle(self, /) -> int: ...
    @staticmethod
    def importPkcs12(device: PySide6.QtCore.QIODevice, key: PySide6.QtNetwork.QSslKey | int, cert: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /, caCertificates: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate] | None = ..., passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> bool: ...
    def isBlacklisted(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isSelfSigned(self, /) -> bool: ...
    def issuerDisplayName(self, /) -> str: ...
    @typing.overload
    def issuerInfo(self, info: PySide6.QtNetwork.QSslCertificate.SubjectInfo, /) -> typing.List[str]: ...
    @typing.overload
    def issuerInfo(self, attribute: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[str]: ...
    def issuerInfoAttributes(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def publicKey(self, /) -> PySide6.QtNetwork.QSslKey: ...
    def serialNumber(self, /) -> PySide6.QtCore.QByteArray: ...
    def subjectAlternativeNames(self, /) -> typing.OrderedDict[PySide6.QtNetwork.QSsl.AlternativeNameEntryType, typing.List[str]]: ...
    def subjectDisplayName(self, /) -> str: ...
    @typing.overload
    def subjectInfo(self, info: PySide6.QtNetwork.QSslCertificate.SubjectInfo, /) -> typing.List[str]: ...
    @typing.overload
    def subjectInfo(self, attribute: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.List[str]: ...
    def subjectInfoAttributes(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def swap(self, other: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...
    def toDer(self, /) -> PySide6.QtCore.QByteArray: ...
    def toPem(self, /) -> PySide6.QtCore.QByteArray: ...
    def toText(self, /) -> str: ...
    @staticmethod
    def verify(certificateChain: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate], /, hostName: str = ...) -> typing.List[PySide6.QtNetwork.QSslError]: ...
    def version(self, /) -> PySide6.QtCore.QByteArray: ...


class QSslCertificateExtension(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslCertificateExtension, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def isCritical(self, /) -> bool: ...
    def isSupported(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def oid(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QSslCertificateExtension, /) -> None: ...
    def value(self, /) -> typing.Any: ...


class QSslCipher(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslCipher, /) -> None: ...
    @typing.overload
    def __init__(self, name: str, /) -> None: ...
    @typing.overload
    def __init__(self, name: str, protocol: PySide6.QtNetwork.QSsl.SslProtocol, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QSslCipher, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QSslCipher, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def authenticationMethod(self, /) -> str: ...
    def encryptionMethod(self, /) -> str: ...
    def isNull(self, /) -> bool: ...
    def keyExchangeMethod(self, /) -> str: ...
    def name(self, /) -> str: ...
    def protocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    def protocolString(self, /) -> str: ...
    def supportedBits(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QSslCipher, /) -> None: ...
    def usedBits(self, /) -> int: ...


class QSslConfiguration(Shiboken.Object):

    class NextProtocolNegotiationStatus(enum.Enum):

        NextProtocolNegotiationNone = ...  # 0x0
        NextProtocolNegotiationNegotiated = ...  # 0x1
        NextProtocolNegotiationUnsupported = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QSslConfiguration, /) -> bool: ...
    def __ne__(self, other: PySide6.QtNetwork.QSslConfiguration, /) -> bool: ...
    def addCaCertificate(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...
    @typing.overload
    def addCaCertificates(self, path: str, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ..., syntax: PySide6.QtNetwork.QSslCertificate.PatternSyntax = ...) -> bool: ...
    @typing.overload
    def addCaCertificates(self, certificates: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate], /) -> None: ...
    def allowedNextProtocols(self, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def backendConfiguration(self, /) -> typing.Dict[PySide6.QtCore.QByteArray, typing.Any]: ...
    def caCertificates(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def ciphers(self, /) -> typing.List[PySide6.QtNetwork.QSslCipher]: ...
    @staticmethod
    def defaultConfiguration() -> PySide6.QtNetwork.QSslConfiguration: ...
    @staticmethod
    def defaultDtlsConfiguration() -> PySide6.QtNetwork.QSslConfiguration: ...
    def diffieHellmanParameters(self, /) -> PySide6.QtNetwork.QSslDiffieHellmanParameters: ...
    def dtlsCookieVerificationEnabled(self, /) -> bool: ...
    def ellipticCurves(self, /) -> typing.List[PySide6.QtNetwork.QSslEllipticCurve]: ...
    def ephemeralServerKey(self, /) -> PySide6.QtNetwork.QSslKey: ...
    def handshakeMustInterruptOnError(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def localCertificate(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def localCertificateChain(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def missingCertificateIsFatal(self, /) -> bool: ...
    def nextNegotiatedProtocol(self, /) -> PySide6.QtCore.QByteArray: ...
    def nextProtocolNegotiationStatus(self, /) -> PySide6.QtNetwork.QSslConfiguration.NextProtocolNegotiationStatus: ...
    def ocspStaplingEnabled(self, /) -> bool: ...
    def peerCertificate(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def peerCertificateChain(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def peerVerifyDepth(self, /) -> int: ...
    def peerVerifyMode(self, /) -> PySide6.QtNetwork.QSslSocket.PeerVerifyMode: ...
    def preSharedKeyIdentityHint(self, /) -> PySide6.QtCore.QByteArray: ...
    def privateKey(self, /) -> PySide6.QtNetwork.QSslKey: ...
    def protocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    def sessionCipher(self, /) -> PySide6.QtNetwork.QSslCipher: ...
    def sessionProtocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    def sessionTicket(self, /) -> PySide6.QtCore.QByteArray: ...
    def sessionTicketLifeTimeHint(self, /) -> int: ...
    def setAllowedNextProtocols(self, protocols: collections.abc.Sequence[PySide6.QtCore.QByteArray], /) -> None: ...
    def setBackendConfiguration(self, /, backendConfiguration: typing.Dict[PySide6.QtCore.QByteArray, typing.Any] = ...) -> None: ...
    def setBackendConfigurationOption(self, name: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, value: typing.Any, /) -> None: ...
    def setCaCertificates(self, certificates: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate], /) -> None: ...
    @typing.overload
    def setCiphers(self, ciphers: str, /) -> None: ...
    @typing.overload
    def setCiphers(self, ciphers: collections.abc.Sequence[PySide6.QtNetwork.QSslCipher], /) -> None: ...
    @staticmethod
    def setDefaultConfiguration(configuration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    @staticmethod
    def setDefaultDtlsConfiguration(configuration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def setDiffieHellmanParameters(self, dhparams: PySide6.QtNetwork.QSslDiffieHellmanParameters, /) -> None: ...
    def setDtlsCookieVerificationEnabled(self, enable: bool, /) -> None: ...
    def setEllipticCurves(self, curves: collections.abc.Sequence[PySide6.QtNetwork.QSslEllipticCurve], /) -> None: ...
    def setHandshakeMustInterruptOnError(self, interrupt: bool, /) -> None: ...
    def setLocalCertificate(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...
    def setLocalCertificateChain(self, localChain: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate], /) -> None: ...
    def setMissingCertificateIsFatal(self, cannotRecover: bool, /) -> None: ...
    def setOcspStaplingEnabled(self, enable: bool, /) -> None: ...
    def setPeerVerifyDepth(self, depth: int, /) -> None: ...
    def setPeerVerifyMode(self, mode: PySide6.QtNetwork.QSslSocket.PeerVerifyMode, /) -> None: ...
    def setPreSharedKeyIdentityHint(self, hint: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setPrivateKey(self, key: PySide6.QtNetwork.QSslKey | int, /) -> None: ...
    def setProtocol(self, protocol: PySide6.QtNetwork.QSsl.SslProtocol, /) -> None: ...
    def setSessionTicket(self, sessionTicket: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setSslOption(self, option: PySide6.QtNetwork.QSsl.SslOption, on: bool, /) -> None: ...
    @staticmethod
    def supportedCiphers() -> typing.List[PySide6.QtNetwork.QSslCipher]: ...
    @staticmethod
    def supportedEllipticCurves() -> typing.List[PySide6.QtNetwork.QSslEllipticCurve]: ...
    def swap(self, other: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    @staticmethod
    def systemCaCertificates() -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def testSslOption(self, option: PySide6.QtNetwork.QSsl.SslOption, /) -> bool: ...


class QSslDiffieHellmanParameters(Shiboken.Object):

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InvalidInputDataError     = ...  # 0x1
        UnsafeParametersError     = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslDiffieHellmanParameters, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QSslDiffieHellmanParameters, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QSslDiffieHellmanParameters, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    @staticmethod
    def defaultParameters() -> PySide6.QtNetwork.QSslDiffieHellmanParameters: ...
    def error(self, /) -> PySide6.QtNetwork.QSslDiffieHellmanParameters.Error: ...
    def errorString(self, /) -> str: ...
    @typing.overload
    @staticmethod
    def fromEncoded(device: PySide6.QtCore.QIODevice, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> PySide6.QtNetwork.QSslDiffieHellmanParameters: ...
    @typing.overload
    @staticmethod
    def fromEncoded(encoded: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> PySide6.QtNetwork.QSslDiffieHellmanParameters: ...
    def isEmpty(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def swap(self, other: PySide6.QtNetwork.QSslDiffieHellmanParameters, /) -> None: ...


class QSslEllipticCurve(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QSslEllipticCurve: PySide6.QtNetwork.QSslEllipticCurve, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QSslEllipticCurve, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QSslEllipticCurve, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    @staticmethod
    def fromLongName(name: str, /) -> PySide6.QtNetwork.QSslEllipticCurve: ...
    @staticmethod
    def fromShortName(name: str, /) -> PySide6.QtNetwork.QSslEllipticCurve: ...
    def isTlsNamedCurve(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def longName(self, /) -> str: ...
    def shortName(self, /) -> str: ...


class QSslError(Shiboken.Object):

    class SslError(enum.Enum):

        UnspecifiedError          = ...  # -1
        NoError                   = ...  # 0x0
        UnableToGetIssuerCertificate = ...  # 0x1
        UnableToDecryptCertificateSignature = ...  # 0x2
        UnableToDecodeIssuerPublicKey = ...  # 0x3
        CertificateSignatureFailed = ...  # 0x4
        CertificateNotYetValid    = ...  # 0x5
        CertificateExpired        = ...  # 0x6
        InvalidNotBeforeField     = ...  # 0x7
        InvalidNotAfterField      = ...  # 0x8
        SelfSignedCertificate     = ...  # 0x9
        SelfSignedCertificateInChain = ...  # 0xa
        UnableToGetLocalIssuerCertificate = ...  # 0xb
        UnableToVerifyFirstCertificate = ...  # 0xc
        CertificateRevoked        = ...  # 0xd
        InvalidCaCertificate      = ...  # 0xe
        PathLengthExceeded        = ...  # 0xf
        InvalidPurpose            = ...  # 0x10
        CertificateUntrusted      = ...  # 0x11
        CertificateRejected       = ...  # 0x12
        SubjectIssuerMismatch     = ...  # 0x13
        AuthorityIssuerSerialNumberMismatch = ...  # 0x14
        NoPeerCertificate         = ...  # 0x15
        HostNameMismatch          = ...  # 0x16
        NoSslSupport              = ...  # 0x17
        CertificateBlacklisted    = ...  # 0x18
        CertificateStatusUnknown  = ...  # 0x19
        OcspNoResponseFound       = ...  # 0x1a
        OcspMalformedRequest      = ...  # 0x1b
        OcspMalformedResponse     = ...  # 0x1c
        OcspInternalError         = ...  # 0x1d
        OcspTryLater              = ...  # 0x1e
        OcspSigRequred            = ...  # 0x1f
        OcspUnauthorized          = ...  # 0x20
        OcspResponseCannotBeTrusted = ...  # 0x21
        OcspResponseCertIdUnknown = ...  # 0x22
        OcspResponseExpired       = ...  # 0x23
        OcspStatusUnknown         = ...  # 0x24


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslError, /) -> None: ...
    @typing.overload
    def __init__(self, error: PySide6.QtNetwork.QSslError.SslError, /) -> None: ...
    @typing.overload
    def __init__(self, error: PySide6.QtNetwork.QSslError.SslError, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNetwork.QSslError, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, other: PySide6.QtNetwork.QSslError, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def certificate(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def error(self, /) -> PySide6.QtNetwork.QSslError.SslError: ...
    def errorString(self, /) -> str: ...
    def swap(self, other: PySide6.QtNetwork.QSslError, /) -> None: ...


class QSslKey(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, device: PySide6.QtCore.QIODevice, algorithm: PySide6.QtNetwork.QSsl.KeyAlgorithm, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ..., type: PySide6.QtNetwork.QSsl.KeyType = ..., passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNetwork.QSslKey, /) -> None: ...
    @typing.overload
    def __init__(self, encoded: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, algorithm: PySide6.QtNetwork.QSsl.KeyAlgorithm, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ..., type: PySide6.QtNetwork.QSsl.KeyType = ..., passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    @typing.overload
    def __init__(self, handle: int, /, type: PySide6.QtNetwork.QSsl.KeyType = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, key: PySide6.QtNetwork.QSslKey | int, /) -> bool: ...
    def __ne__(self, key: PySide6.QtNetwork.QSslKey | int, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def algorithm(self, /) -> PySide6.QtNetwork.QSsl.KeyAlgorithm: ...
    def clear(self, /) -> None: ...
    def handle(self, /) -> int: ...
    def isNull(self, /) -> bool: ...
    def length(self, /) -> int: ...
    def swap(self, other: PySide6.QtNetwork.QSslKey | int, /) -> None: ...
    def toDer(self, /, passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> PySide6.QtCore.QByteArray: ...
    def toPem(self, /, passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> PySide6.QtCore.QByteArray: ...
    def type(self, /) -> PySide6.QtNetwork.QSsl.KeyType: ...


class QSslPreSharedKeyAuthenticator(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, authenticator: PySide6.QtNetwork.QSslPreSharedKeyAuthenticator, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtNetwork.QSslPreSharedKeyAuthenticator, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtNetwork.QSslPreSharedKeyAuthenticator, /) -> bool: ...
    def identity(self, /) -> PySide6.QtCore.QByteArray: ...
    def identityHint(self, /) -> PySide6.QtCore.QByteArray: ...
    def maximumIdentityLength(self, /) -> int: ...
    def maximumPreSharedKeyLength(self, /) -> int: ...
    def preSharedKey(self, /) -> PySide6.QtCore.QByteArray: ...
    def setIdentity(self, identity: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setPreSharedKey(self, preSharedKey: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def swap(self, other: PySide6.QtNetwork.QSslPreSharedKeyAuthenticator, /) -> None: ...


class QSslServer(PySide6.QtNetwork.QTcpServer):

    alertReceived            : typing.ClassVar[Signal] = ... # alertReceived(QSslSocket*,QSsl::AlertLevel,QSsl::AlertType,QString)
    alertSent                : typing.ClassVar[Signal] = ... # alertSent(QSslSocket*,QSsl::AlertLevel,QSsl::AlertType,QString)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QSslSocket*,QAbstractSocket::SocketError)
    handshakeInterruptedOnError: typing.ClassVar[Signal] = ... # handshakeInterruptedOnError(QSslSocket*,QSslError)
    peerVerifyError          : typing.ClassVar[Signal] = ... # peerVerifyError(QSslSocket*,QSslError)
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QSslSocket*,QSslPreSharedKeyAuthenticator*)
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QSslSocket*,QList<QSslError>)
    startedEncryptionHandshake: typing.ClassVar[Signal] = ... # startedEncryptionHandshake(QSslSocket*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def handshakeTimeout(self, /) -> int: ...
    def incomingConnection(self, socket: int, /) -> None: ...
    def setHandshakeTimeout(self, timeout: int, /) -> None: ...
    def setSslConfiguration(self, sslConfiguration: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...


class QSslSocket(PySide6.QtNetwork.QTcpSocket):

    alertReceived            : typing.ClassVar[Signal] = ... # alertReceived(QSsl::AlertLevel,QSsl::AlertType,QString)
    alertSent                : typing.ClassVar[Signal] = ... # alertSent(QSsl::AlertLevel,QSsl::AlertType,QString)
    encrypted                : typing.ClassVar[Signal] = ... # encrypted()
    encryptedBytesWritten    : typing.ClassVar[Signal] = ... # encryptedBytesWritten(qlonglong)
    handshakeInterruptedOnError: typing.ClassVar[Signal] = ... # handshakeInterruptedOnError(QSslError)
    modeChanged              : typing.ClassVar[Signal] = ... # modeChanged(QSslSocket::SslMode)
    newSessionTicketReceived : typing.ClassVar[Signal] = ... # newSessionTicketReceived()
    peerVerifyError          : typing.ClassVar[Signal] = ... # peerVerifyError(QSslError)
    preSharedKeyAuthenticationRequired: typing.ClassVar[Signal] = ... # preSharedKeyAuthenticationRequired(QSslPreSharedKeyAuthenticator*)
    sslErrors                : typing.ClassVar[Signal] = ... # sslErrors(QList<QSslError>)

    class PeerVerifyMode(enum.Enum):

        VerifyNone                = ...  # 0x0
        QueryPeer                 = ...  # 0x1
        VerifyPeer                = ...  # 0x2
        AutoVerifyPeer            = ...  # 0x3

    class SslMode(enum.Enum):

        UnencryptedMode           = ...  # 0x0
        SslClientMode             = ...  # 0x1
        SslServerMode             = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @staticmethod
    def activeBackend() -> str: ...
    def atEnd(self, /) -> bool: ...
    @staticmethod
    def availableBackends() -> typing.List[str]: ...
    def bytesAvailable(self, /) -> int: ...
    def bytesToWrite(self, /) -> int: ...
    def canReadLine(self, /) -> bool: ...
    def close(self, /) -> None: ...
    @typing.overload
    def connectToHost(self, hostName: str, port: int, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: PySide6.QtNetwork.QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    @typing.overload
    def connectToHost(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: str, port: int, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: PySide6.QtNetwork.QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: str, port: int, sslPeerName: str, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: PySide6.QtNetwork.QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    def continueInterruptedHandshake(self, /) -> None: ...
    def disconnectFromHost(self, /) -> None: ...
    def encryptedBytesAvailable(self, /) -> int: ...
    def encryptedBytesToWrite(self, /) -> int: ...
    @typing.overload
    def ignoreSslErrors(self, /) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: collections.abc.Sequence[PySide6.QtNetwork.QSslError], /) -> None: ...
    @staticmethod
    def implementedClasses(backendName: str = ...) -> typing.List[PySide6.QtNetwork.QSsl.ImplementedClass]: ...
    @staticmethod
    def isClassImplemented(cl: PySide6.QtNetwork.QSsl.ImplementedClass, /, backendName: str = ...) -> bool: ...
    def isEncrypted(self, /) -> bool: ...
    @staticmethod
    def isFeatureSupported(feat: PySide6.QtNetwork.QSsl.SupportedFeature, /, backendName: str = ...) -> bool: ...
    @staticmethod
    def isProtocolSupported(protocol: PySide6.QtNetwork.QSsl.SslProtocol, /, backendName: str = ...) -> bool: ...
    def localCertificate(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def localCertificateChain(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def mode(self, /) -> PySide6.QtNetwork.QSslSocket.SslMode: ...
    def ocspResponses(self, /) -> typing.List[PySide6.QtNetwork.QOcspResponse]: ...
    def peerCertificate(self, /) -> PySide6.QtNetwork.QSslCertificate: ...
    def peerCertificateChain(self, /) -> typing.List[PySide6.QtNetwork.QSslCertificate]: ...
    def peerVerifyDepth(self, /) -> int: ...
    def peerVerifyMode(self, /) -> PySide6.QtNetwork.QSslSocket.PeerVerifyMode: ...
    def peerVerifyName(self, /) -> str: ...
    def privateKey(self, /) -> PySide6.QtNetwork.QSslKey: ...
    def protocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    def readData(self, maxlen: int, /) -> object: ...
    def resume(self, /) -> None: ...
    def sessionCipher(self, /) -> PySide6.QtNetwork.QSslCipher: ...
    def sessionProtocol(self, /) -> PySide6.QtNetwork.QSsl.SslProtocol: ...
    @staticmethod
    def setActiveBackend(backendName: str, /) -> bool: ...
    @typing.overload
    def setLocalCertificate(self, fileName: str, /, format: PySide6.QtNetwork.QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def setLocalCertificate(self, certificate: PySide6.QtNetwork.QSslCertificate | PySide6.QtCore.QIODevice, /) -> None: ...
    def setLocalCertificateChain(self, localChain: collections.abc.Sequence[PySide6.QtNetwork.QSslCertificate], /) -> None: ...
    def setPeerVerifyDepth(self, depth: int, /) -> None: ...
    def setPeerVerifyMode(self, mode: PySide6.QtNetwork.QSslSocket.PeerVerifyMode, /) -> None: ...
    def setPeerVerifyName(self, hostName: str, /) -> None: ...
    @typing.overload
    def setPrivateKey(self, fileName: str, /, algorithm: PySide6.QtNetwork.QSsl.KeyAlgorithm = ..., format: PySide6.QtNetwork.QSsl.EncodingFormat = ..., passPhrase: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    @typing.overload
    def setPrivateKey(self, key: PySide6.QtNetwork.QSslKey | int, /) -> None: ...
    def setProtocol(self, protocol: PySide6.QtNetwork.QSsl.SslProtocol, /) -> None: ...
    def setReadBufferSize(self, size: int, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, /, state: PySide6.QtNetwork.QAbstractSocket.SocketState = ..., openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def setSocketOption(self, option: PySide6.QtNetwork.QAbstractSocket.SocketOption, value: typing.Any, /) -> None: ...
    def setSslConfiguration(self, config: PySide6.QtNetwork.QSslConfiguration, /) -> None: ...
    def skipData(self, maxSize: int, /) -> int: ...
    def socketOption(self, option: PySide6.QtNetwork.QAbstractSocket.SocketOption, /) -> typing.Any: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def sslHandshakeErrors(self, /) -> typing.List[PySide6.QtNetwork.QSslError]: ...
    @staticmethod
    def sslLibraryBuildVersionNumber() -> int: ...
    @staticmethod
    def sslLibraryBuildVersionString() -> str: ...
    @staticmethod
    def sslLibraryVersionNumber() -> int: ...
    @staticmethod
    def sslLibraryVersionString() -> str: ...
    def startClientEncryption(self, /) -> None: ...
    def startServerEncryption(self, /) -> None: ...
    @staticmethod
    def supportedFeatures(backendName: str = ...) -> typing.List[PySide6.QtNetwork.QSsl.SupportedFeature]: ...
    @staticmethod
    def supportedProtocols(backendName: str = ...) -> typing.List[PySide6.QtNetwork.QSsl.SslProtocol]: ...
    @staticmethod
    def supportsSsl() -> bool: ...
    def waitForBytesWritten(self, /, msecs: int = ...) -> bool: ...
    def waitForConnected(self, /, msecs: int = ...) -> bool: ...
    def waitForDisconnected(self, /, msecs: int = ...) -> bool: ...
    def waitForEncrypted(self, /, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, /, msecs: int = ...) -> bool: ...
    def writeData(self, data: bytes | bytearray | memoryview, len: int, /) -> int: ...


class QTcpServer(PySide6.QtCore.QObject):

    acceptError              : typing.ClassVar[Signal] = ... # acceptError(QAbstractSocket::SocketError)
    newConnection            : typing.ClassVar[Signal] = ... # newConnection()
    pendingConnectionAvailable: typing.ClassVar[Signal] = ... # pendingConnectionAvailable()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addPendingConnection(self, socket: PySide6.QtNetwork.QTcpSocket, /) -> None: ...
    def close(self, /) -> None: ...
    def errorString(self, /) -> str: ...
    def hasPendingConnections(self, /) -> bool: ...
    def incomingConnection(self, handle: int, /) -> None: ...
    def isListening(self, /) -> bool: ...
    def listen(self, /, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress = ..., port: int | None = ...) -> bool: ...
    def listenBacklogSize(self, /) -> int: ...
    def maxPendingConnections(self, /) -> int: ...
    def nextPendingConnection(self, /) -> PySide6.QtNetwork.QTcpSocket: ...
    def pauseAccepting(self, /) -> None: ...
    def proxy(self, /) -> PySide6.QtNetwork.QNetworkProxy: ...
    def resumeAccepting(self, /) -> None: ...
    def serverAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def serverError(self, /) -> PySide6.QtNetwork.QAbstractSocket.SocketError: ...
    def serverPort(self, /) -> int: ...
    def setListenBacklogSize(self, size: int, /) -> None: ...
    def setMaxPendingConnections(self, numConnections: int, /) -> None: ...
    def setProxy(self, networkProxy: PySide6.QtNetwork.QNetworkProxy | PySide6.QtNetwork.QNetworkProxy.ProxyType, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, /) -> bool: ...
    def socketDescriptor(self, /) -> int: ...
    def waitForNewConnection(self, msec: int, /) -> typing.Tuple[bool, bool]: ...


class QTcpSocket(PySide6.QtNetwork.QAbstractSocket):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def bind(self, addr: PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    @typing.overload
    def bind(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    @typing.overload
    def bind(self, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...


class QUdpSocket(PySide6.QtNetwork.QAbstractSocket):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def bind(self, addr: PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    @typing.overload
    def bind(self, address: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    @typing.overload
    def bind(self, /, port: int | None = ..., mode: PySide6.QtNetwork.QAbstractSocket.BindFlag = ...) -> bool: ...
    def hasPendingDatagrams(self, /) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, iface: PySide6.QtNetwork.QNetworkInterface, /) -> bool: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, /) -> bool: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, iface: PySide6.QtNetwork.QNetworkInterface, /) -> bool: ...
    def multicastInterface(self, /) -> PySide6.QtNetwork.QNetworkInterface: ...
    def pendingDatagramSize(self, /) -> int: ...
    def readDatagram(self, maxlen: int, /) -> typing.Tuple[PySide6.QtCore.QByteArray, PySide6.QtNetwork.QHostAddress, int]: ...
    def receiveDatagram(self, /, maxSize: int = ...) -> PySide6.QtNetwork.QNetworkDatagram: ...
    def setMulticastInterface(self, iface: PySide6.QtNetwork.QNetworkInterface, /) -> None: ...
    @typing.overload
    def writeDatagram(self, datagram: PySide6.QtNetwork.QNetworkDatagram | PySide6.QtCore.QByteArray, /) -> int: ...
    @typing.overload
    def writeDatagram(self, datagram: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, host: PySide6.QtNetwork.QHostAddress | PySide6.QtNetwork.QHostAddress.SpecialAddress, port: int, /) -> int: ...


# eof
