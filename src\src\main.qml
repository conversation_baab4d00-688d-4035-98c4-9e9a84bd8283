import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15

// Import the screens directory
import "screens"
import "components/dialogs"
import "constants/WindowsConstants.js" as WindowsConstants
import "constants/TextConstants.js" as TextConstants
import "utils/Utils.js" as Utils

ApplicationWindow {
    id: mainWindow
    width: WindowsConstants.mainWindowSize.width
    height: WindowsConstants.mainWindowSize.height
    minimumWidth: WindowsConstants.mainWindowSize.width
    minimumHeight: WindowsConstants.mainWindowSize.height
    visible: true
    title: qsTr("FactoryEye v" + appVersion)
    color: "#2c2c2c"

    property var cameraWindows: []
    property bool isCameraWindowsEmpty: cameraWindows.length === 0

    function openCameraWindow(cameraspaceInfo) {
        Utils.createCameraWindow(mainWindow, cameraspaceInfo, cameraWindows, workspaceDatabase, menuBar.selectedWorkspaceId, drawingWindow);
    }

    // Add this function to find camera windows by serial number
    function findCameraWindowsBySerialNum(serialNumber) {
        var serialWindows = [];
        for (var i = 0; i < cameraWindows.length; i++) {
            if (cameraWindows[i].serialNumber === serialNumber) {
                serialWindows.push(cameraWindows[i]);
            }
        }
        return serialWindows;
    }

    onClosing: function(close) {
        // Close all camera windows first
        for (var i = cameraWindows.length - 1; i >= 0; i--) {
            cameraWindows[i].closeWindow();
        }
        
        // Save any final application state if needed
        workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, "camera_list", cameraListWindow.visible);
        workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, "camera_setting", brightnessWindow.visible);
        workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, "camera_ipl", iplWindow.visible);
        workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, "camera_drawing", drawingWindow.visible);
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 1

        WorkspaceDialog {
            id: workspaceDialog
        }

        // Menu Bar
        Menubar {
            id: menuBar
            Layout.fillWidth: true

            onWorkspaceSelected: function (workspace_info) {
                brightnessWindow.visible = workspace_info["camera_setting"];
                iplWindow.visible = workspace_info["camera_ipl"];
                drawingWindow.visible = workspace_info["camera_drawing"];
                cameraListWindow.visible = workspace_info["camera_list"];

                    // Iterate over cameraWindows and close those not in validSerialNumbers
                    for (var i = cameraWindows.length - 1; i >= 0; i--) {
                        cameraWindows[i].closeWindow();
                    }

                    // Open new camera windows as needed
                    for (var i = 0; i < workspace_info["cameraspace"].length; i++) {
                        openCameraWindow(workspace_info["cameraspace"][i]);
                }

                isCameraWindowsEmpty = cameraWindows.length === 0;
            }

            onCameraListClicked: {
                cameraListWindow.visible = !cameraListWindow.visible;
                workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_list, cameraListWindow.visible);
            }
            onAllClosed: function (isChangedWorkspace) {
                try {
                    cameraListWindow.visible = false;
                    iplWindow.visible = false;
                    brightnessWindow.visible = false;
                    drawingWindow.visible = false;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_list, false);
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_setting, false);
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_ipl, false);
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_drawing, false);

                    while (cameraWindows.length > 0) {
                        if (!isChangedWorkspace) {
                            workspaceDatabase.remove_cameraspace(cameraWindows[0].cameraspaceId); // Remove from database
                        }

                        cameraWindows[0].closeWindow();
                    }

                    cameraWindows = [];
                    isCameraWindowsEmpty = cameraWindows.length === 0;
                } catch (error) {
                    console.error("Error closing camera window:", error);
                }
            }
            onAddWorkspaceClicked: function (workspaceName) {
                workspaceName = workspaceName === TextConstants.noWorkspace ? "" : workspaceName;
                workspaceDialog.workspaceName = workspaceName;
                workspaceDialog.visible = true;
            }
        }

        //Main Content
        RowLayout {
            spacing: 1
            Layout.fillWidth: true
            Layout.fillHeight: true

            // Side Bar
            Sidebar {
                id: sidebar
                Layout.fillHeight: true

                onIPLClicked: {
                    iplWindow.visible = !iplWindow.visible;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_ipl, iplWindow.visible);
                }

                onBrightnessClicked: {
                    brightnessWindow.visible = !brightnessWindow.visible;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_setting, brightnessWindow.visible);
                }
            }

            // Camera List Window
            CameraListWindow {
                id: cameraListWindow
                Layout.fillHeight: true
                visible: false

                onCameraListClosed: {
                    cameraListWindow.visible = false;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_list, false);
                }

                onCameraPlayRequested: function (modelName, serialNumber) {
                    var workspaceId = menuBar.selectedWorkspaceId;
                    
                    // Calculate center position relative to main window
                    var mainWindowCenter = Qt.point(
                        mainWindow.x + (mainWindow.width - WindowsConstants.cameraWindowSize.width) / 2,
                        mainWindow.y + (mainWindow.height - WindowsConstants.cameraWindowSize.height) / 2
                    );
                    
                    // Add offset based on number of existing windows to prevent perfect overlap
                    var offset = cameraWindows.length * 30;  // 30 pixels offset for each existing window
                    var x = Math.max(0, mainWindowCenter.x + offset);
                    var y = Math.max(0, mainWindowCenter.y + offset);
                    
                    var cameraspaceInfo = workspaceDatabase.add_cameraspace(
                        modelName, 
                        serialNumber, 
                        workspaceId,
                        x,
                        y,
                        WindowsConstants.cameraWindowSize.width, 
                        WindowsConstants.cameraWindowSize.height
                    );
                    
                    if (cameraspaceInfo) {
                        openCameraWindow(cameraspaceInfo);
                    }
                    isCameraWindowsEmpty = cameraWindows.length === 0;
                }

                onCameraStopRequested: function (serialNumber) {
                    // Find and close the CameraWorkspaceWindow with the given serial number
                    for (var i = 0; i < cameraWindows.length; i++) {
                        if (cameraWindows[i].serialNumber === serialNumber) {
                            workspaceDatabase.remove_cameraspace(cameraWindows[i].cameraspaceId); // Use cameraspaceId instead
                            cameraWindows[i].closeWindow();
                            break;
                        }
                    }
                    isCameraWindowsEmpty = cameraWindows.length === 0;
                }
            }

            // IPL Window
            IPLWindow {
                id: iplWindow
                Layout.fillHeight: true
                visible: false

                onIPLClosed: {
                    iplWindow.visible = false;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_ipl, false);
                }
            }

            // Brightness Window
            BrightnessWindow {
                id: brightnessWindow
                Layout.fillHeight: true
                visible: false
                isCameraWindowsEmpty: mainWindow.isCameraWindowsEmpty

                onBrightnessClosed: {
                    brightnessWindow.visible = false;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_setting, false);
                }
            }

            // Drawing Window
            DrawingWindow {
                id: drawingWindow
                Layout.fillHeight: true
                visible: false

                onDrawingClosed: {
                    drawingWindow.visible = false;
                    workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, TextConstants.tbFeildName.camera_drawing, false);
                }
            }
        }
    }

    // Background text
    Text {
        text: "FACTORYEYE"
        font.pointSize: 30
        font.bold: true
        color: "white"
        opacity: 0.5
        anchors.centerIn: parent
        z: -1
    }
}
