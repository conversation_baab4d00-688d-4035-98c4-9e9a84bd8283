import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Window

Window {
    id: root
    title: qsTr("About FactoryEye")
    width: 340
    height: 170
    minimumWidth: 340
    maximumWidth: 340
    minimumHeight: 170
    maximumHeight: 170
    flags: Qt.Dialog
    modality: Qt.ApplicationModal
    color: "#2c2c2c"

    // Center the window when it opens
    function centerWindow() {
        let screenWidth = Screen.width
        let screenHeight = Screen.height
        x = (screenWidth - width) / 2
        y = (screenHeight - height) / 2
    }

    Component.onCompleted: {
        centerWindow()
        okButton.forceActiveFocus() // Set focus directly to the OK button
    }

    Column {
        anchors.fill: parent
        // Content area
        Rectangle {
            width: parent.width
            height: parent.height
            color: "#3c3c3c"

            ColumnLayout {
                anchors {
                    fill: parent
                    margins: 20
                }
                spacing: 12

                Label {
                    text: qsTr("FactoryEye v" + appVersion)
                    font.bold: true
                    font.pixelSize: 14
                    color: "white"
                    Layout.fillWidth: true
                }

                Label {
                    text: qsTr("Industrial camera management and analysis tool for IDS camera.")
                    wrapMode: Text.WordWrap
                    color: "white"
                    Layout.fillWidth: true
                }

                Label {
                    text: qsTr("Copyright © 2025")
                    color: "white"
                    Layout.fillWidth: true
                }

                Item { Layout.fillHeight: true } // Spacer

                Button {
                    id: okButton
                    text: qsTr("OK")
                    Layout.alignment: Qt.AlignRight
                    implicitWidth: 80
                    focus: true
                    onClicked: root.close()

                    Keys.onPressed: function(event) {
                        if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                            root.close()
                            event.accepted = true
                        }
                    }
                }
            }
        }
    }
}

