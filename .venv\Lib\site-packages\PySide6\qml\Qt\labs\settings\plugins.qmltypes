import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmlsettings_p.h"
        name: "QQmlSettingsLabs"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt.labs.settings/Settings 1.0",
            "Qt.labs.settings/Settings 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "category"
            type: "QString"
            read: "category"
            write: "setCategory"
            index: 0
            isFinal: true
        }
        Property {
            name: "fileName"
            type: "QString"
            read: "fileName"
            write: "setFileName"
            index: 1
            isFinal: true
        }
        Method { name: "_q_propertyChanged" }
        Method {
            name: "value"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "key"; type: "QString" }
            Parameter { name: "defaultValue"; type: "QVariant" }
        }
        Method {
            name: "value"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "key"; type: "QString" }
        }
        Method {
            name: "setValue"
            Parameter { name: "key"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method { name: "sync" }
    }
}
