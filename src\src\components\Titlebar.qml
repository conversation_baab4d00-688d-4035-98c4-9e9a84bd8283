import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../constants/WindowsConstants.js" as WindowsConstants

Rectangle {
    width: parent.width
    height: WindowsConstants.topBarHeight
    color: "#888888"

    signal subWindowClosed

    property string titleIcon: "qrc:/resources/icons/logo.png"
    property string title: "FactoryEye"

    RowLayout {
        width: parent.width
        ToolButton {
            icon.source: titleIcon
            enabled: false
        }
        Text {
            text: title
            color: "black"
        }

        // Rectangle to push the close button to the right
        Item {
            Layout.fillWidth: true
        }

        ToolButton {
            icon.source: "qrc:/resources/icons/close.png" // Path to your close icon
            onClicked: {
                subWindowClosed();
            }
        }
    }
}
