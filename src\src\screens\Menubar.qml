import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../constants/WindowsConstants.js" as WindowsConstants
import "../constants/TextConstants.js" as TextConstants
import "../constants/NumberConstants.js" as NumberConstants
import "../components/dialogs"
import "../components/separators"
import "../components"

Rectangle {
    Layout.fillWidth: true
    height: 35 // Adjust the height as needed
    color: "lightgray"

    property string selectedWorkspaceName: TextConstants.noWorkspace
    property int selectedWorkspaceId: NumberConstants.noWorkspaceId
    property bool cameraListVisible: false

    signal cameraListClicked
    signal allClosed(bool isChangedWorkspace)
    signal addWorkspaceClicked(string workspaceName)
    signal workspaceSelected(variant workspaceInfo)

    AboutDialog {
        id: aboutDialog
    }

    Connections {
        target: cameraListWindow
        function onVisibleChanged() {
            cameraListVisible = cameraListWindow.visible
        }
    }

    Connections {
        target: workspaceDatabase
        function onWorkspaceAdded(workspaceName) {
            workspaceComboBox.model = workspaceDatabase.workspaceNames;
            workspaceComboBox.currentIndex = workspaceComboBox.model.indexOf(workspaceName);
            workspaceComboBox.activated(workspaceComboBox.model.indexOf(workspaceName));
        }
    }

    Connections {
        target: cameraListModel
        
        // Handle camera list changes (new connections/disconnections)
        function onCameraListChanged() {
            quickCameraButton.enabled = cameraListModel.has_available_camera()
        }
        
        // Handle camera state changes (start/stop)
        function onCameraStateChanged() {
            quickCameraButton.enabled = cameraListModel.has_available_camera()
        }
        
        // Optional: Handle specific camera connection events
        function onCameraConnected(serialNumber) {
            console.log("New camera connected:", serialNumber)
            // Add any specific UI updates for new camera connection
        }
        
        // Optional: Handle specific camera disconnection events
        function onCameraDisconnected(serialNumber) {
            console.log("Camera disconnected:", serialNumber)
            // Add any specific UI updates for camera disconnection
        }
    }

    RowLayout {
        spacing: 10
        Layout.fillHeight: true

        Rectangle {
            width: 1
            height: parent.parent.height
            color: "transparent"
        }

        ToolButton {
            id: quickCameraButton
            icon.source: "qrc:/resources/icons/camera.png"
            enabled: cameraListModel.has_available_camera()
            onClicked: {
                // Get the first available (not started) camera from the list
                var availableCamera = cameraListModel.get_first_available_camera();
                if (availableCamera) {
                    var mainWindowCenter = Qt.point(
                        mainWindow.x + (mainWindow.width - WindowsConstants.cameraWindowSize.width) / 2,
                        mainWindow.y + (mainWindow.height - WindowsConstants.cameraWindowSize.height) / 2
                    );
                    
                    var offset = mainWindow.cameraWindows.length * 30;
                    var x = Math.max(0, mainWindowCenter.x + offset);
                    var y = Math.max(0, mainWindowCenter.y + offset);
                    
                    var cameraspaceInfo = workspaceDatabase.add_cameraspace(
                        availableCamera.modelName,
                        availableCamera.serialNumber,
                        selectedWorkspaceId,
                        x,
                        y,
                        WindowsConstants.cameraWindowSize.width,
                        WindowsConstants.cameraWindowSize.height
                    );
                    
                    if (cameraspaceInfo) {
                        openCameraWindow(cameraspaceInfo);
                    }
                }
                else {
                    quickCameraButton.enabled = false;
                }
            }
        }
        CheckableButton {
            checked: cameraListVisible
            icon.source: "qrc:/resources/icons/list.png" // Path to your list icon
            onClicked: {
                cameraListClicked();
            }
        }
        ToolButton {
            icon.source: "qrc:/resources/icons/close.png" // Path to your close icon
            onClicked: {
                allClosed(false);
            }
        }

        VerticalSeparator {
            height: menuBar.height * 0.8
        }

        ComboBox {
            id: workspaceComboBox
            model: workspaceDatabase.workspaceNames
            onActivated: {
                selectedWorkspaceName = currentText;
                if (selectedWorkspaceName === TextConstants.noWorkspace) {
                    selectedWorkspaceId = NumberConstants.noWorkspaceId;
                    allClosed(true);
                } else {
                    var workspaceInfo = workspaceDatabase.get_workspace(selectedWorkspaceName);
                    selectedWorkspaceId = workspaceInfo["id"];
                    menuBar.workspaceSelected(workspaceInfo);
                }
            }
        }
        ToolButton {
            icon.source: "qrc:/resources/icons/workspace_add.png" // Path to your add workspace icon
            onClicked: {
                addWorkspaceClicked(selectedWorkspaceName);
            }
        }
        ToolButton {
            icon.source: "qrc:/resources/icons/remove.png" // Path to your remove workspace icon
            onClicked: {
                if (selectedWorkspaceName !== TextConstants.noWorkspace) {
                    workspaceDatabase.remove_workspace(selectedWorkspaceId);
                }
            }
        }

        VerticalSeparator {
            height: menuBar.height * 0.8
        }

        ToolButton {
            icon.source: "qrc:/resources/icons/info.png"
            onClicked: {
                var aboutDialog = Qt.createComponent("../components/dialogs/AboutDialog.qml").createObject(mainWindow)
                aboutDialog.show()
            }
        }
    }
}
