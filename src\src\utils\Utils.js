// utils.js

function createCameraWindow(mainWindow, cameraspaceInfo, cameraWindows, workspaceDatabase, selectedWorkspaceId, drawingWindow) {
    for (var i = 0; i < cameraWindows.length; i++) {
        // If already created
        if (cameraWindows[i].modelName === cameraspaceInfo.camera_name && 
            cameraWindows[i].serialNumber === cameraspaceInfo.camera_serial) {
            return;
        }
    }

    var component = Qt.createComponent("../screens/CameraWorkspaceWindow.qml");

    if (component.status === Component.Ready) {
        var cameraWindow = component.createObject(mainWindow, {
            workspaceId: selectedWorkspaceId,
            cameraspaceId: cameraspaceInfo.id,
            modelName: cameraspaceInfo.camera_name,
            serialNumber: cameraspaceInfo.camera_serial,
            drawingWindow: drawingWindow,
            workspaceDatabase: workspaceDatabase,
            x: cameraspaceInfo.window_x,
            y: cameraspaceInfo.window_y,
            width: cameraspaceInfo.window_w,
            height: cameraspaceInfo.window_h
        });

        cameraWindow.cameraWorkSpaceClosed.connect(function () {
            try {
                var index = cameraWindows.indexOf(cameraWindow);
                if (index !== -1) {
                    cameraWindows.splice(index, 1);
                    cameraWindow.destroy();
                }

                if (cameraWindows.length === 0) {
                    mainWindow.isCameraWindowsEmpty = true;
                }

            } catch (error) {
                console.error("Error closing camera window:", error);
            }
        });

        if (cameraWindow !== null) {
            cameraWindows.push(cameraWindow);
        } else {
            console.error("Failed to create CameraWorkspaceWindow");
        }
    } else {
        console.error("Failed to load CameraWorkspaceWindow component");
    }
}
