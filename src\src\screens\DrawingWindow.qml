import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../constants/WindowsConstants.js" as WindowsConstants

Rectangle {
    id: drawingWindow
    width: WindowsConstants.subWindowWidth
    Layout.fillHeight: true
    color: "lightgray"

    // Property to expose DrawingWindow to other components
    property alias redSlider: redSlider
    property alias greenSlider: greenSlider
    property alias blueSlider: blueSlider
    property alias alphaSlider: alphaSlider
    property alias lineWidth: lineWidth
    property alias lineStyle: lineStyle

    // Signals for value changes
    signal drawingSettingValueChanged
    signal drawingClosed

    Component.onCompleted: {
        var linestyle = workspaceDatabase.get_linestyle();
        if (linestyle) {
            redSlider.sliderValue = linestyle["red"] ; // Assuming extendRate is 255
            greenSlider.sliderValue = linestyle["green"];
            blueSlider.sliderValue = linestyle["blue"];
            alphaSlider.sliderValue = linestyle["alpha"];
            lineWidth.value = linestyle["line_width"];
            lineStyle.currentIndex = linestyle["line_style"];
        }
    }

    Column {
        anchors.fill: parent
        
        Titlebar {
            title: "Drawing options"
            titleIcon: "qrc:/resources/icons/drawing_setting.png"

            onSubWindowClosed: {
                drawingClosed();
            }
        }

        // Rectangle to set background color and padding for ListView
        Rectangle {
            width: parent.width
            height: parent.height - WindowsConstants.topBarHeight
            color: "lightgray"

            Column {
                spacing: 10
                padding: 15
                Layout.fillWidth: true

                // Color Preview
                Rectangle {
                    id: colorPreview
                    Layout.fillWidth: true
                    width: WindowsConstants.subWindowWidth -30
                    height: 20
                    color: Qt.rgba(redSlider.sliderValue, greenSlider.sliderValue, blueSlider.sliderValue, alphaSlider.sliderValue)
                    border.color: "black"
                }

                // Red Slider
                TitleSliderText {
                    id: redSlider
                    title: "Red"
                    sliderValue: 1.0
                    sliderFrom: 0.0
                    sliderTo: 1.0
                    extendRate: 255
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    onSliderValueChanged: {
                        workspaceDatabase.set_linestyle("red", sliderValue)
                        drawingSettingValueChanged()
                    }
                }

                // Green Slider
                TitleSliderText {
                    id: greenSlider
                    title: "Green"
                    sliderValue: 1.0
                    sliderFrom: 0.0
                    sliderTo: 1.0
                    extendRate: 255
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    onSliderValueChanged: {
                        workspaceDatabase.set_linestyle("green", sliderValue)
                        drawingSettingValueChanged()
                    }
                }
                
                // Blue Slider
                TitleSliderText {
                    id: blueSlider
                    title: "Blue"
                    sliderValue: 1.0
                    sliderFrom: 0.0
                    sliderTo: 1.0
                    extendRate: 255
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    onSliderValueChanged: {
                        workspaceDatabase.set_linestyle("blue", sliderValue)
                        drawingSettingValueChanged()
                    }
                }
                
                // Blue Slider
                TitleSliderText {
                    id: alphaSlider
                    title: "Alpha(%)"
                    sliderValue: 1.0
                    sliderFrom: 0.0
                    sliderTo: 1.0
                    extendRate: 100
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    onSliderValueChanged: {
                        workspaceDatabase.set_linestyle("alpha", sliderValue)
                        drawingSettingValueChanged()
                    }
                }

                // Line Width
                RowLayout {
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    Layout.alignment: Qt.AlignHCenter
                    Text {
                        text: "Line width"
                    }
                    SpinBox {
                        id: lineWidth
                        value: 1
                        from: 1
                        to: 10
                        onValueChanged: {
                            workspaceDatabase.set_linestyle("line_width", lineWidth.value)
                            drawingSettingValueChanged()
                        }
                    }
                    
                }

                // Line Style
                RowLayout {
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    Layout.topMargin: 5
                    Layout.bottomMargin: 5
                    Layout.alignment: Qt.AlignHCenter
                    Text {
                        text: "Line style"
                    }
                    ComboBox {
                        id: lineStyle
                        model: ["Solid", "Dashed", "Dotted"]
                        onCurrentTextChanged: {
                            workspaceDatabase.set_linestyle("line_style", lineStyle.currentIndex)
                            drawingSettingValueChanged()
                        }
                    }
                }
            }
        }
    }
}
