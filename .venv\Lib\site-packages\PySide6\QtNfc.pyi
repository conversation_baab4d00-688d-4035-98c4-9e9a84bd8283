# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtNfc, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtNfc`

import PySide6.QtNfc
import PySide6.QtCore

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QNdefFilter(Shiboken.Object):

    class Record(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Record: PySide6.QtNfc.QNdefFilter.Record, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefFilter, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    @typing.overload
    def appendRecord(self, record: PySide6.QtNfc.QNdefFilter.Record, /) -> bool: ...
    @typing.overload
    def appendRecord(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, min: int = ..., max: int = ...) -> bool: ...
    def clear(self, /) -> None: ...
    def match(self, message: PySide6.QtNfc.QNdefMessage | collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> bool: ...
    def orderMatch(self, /) -> bool: ...
    def recordAt(self, i: int, /) -> PySide6.QtNfc.QNdefFilter.Record: ...
    def recordCount(self, /) -> int: ...
    def setOrderMatch(self, on: bool, /) -> None: ...


class QNdefMessage(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, message: PySide6.QtNfc.QNdefMessage, /) -> None: ...
    @typing.overload
    def __init__(self, record: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    @typing.overload
    def __init__(self, records: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> None: ...

    def __add__(self, l: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNfc.QNdefMessage | collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> bool: ...
    def __iadd__(self, l: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def __lshift__(self, l: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    @typing.overload
    def append(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    @typing.overload
    def append(self, l: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> None: ...
    def at(self, i: int, /) -> PySide6.QtNfc.QNdefRecord: ...
    def back(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def capacity(self, /) -> int: ...
    def clear(self, /) -> None: ...
    def constData(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def constFirst(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def constLast(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def count(self, /) -> int: ...
    def data(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def empty(self, /) -> bool: ...
    @typing.overload
    def first(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    @typing.overload
    def first(self, n: int, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    @staticmethod
    def fromByteArray(message: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNfc.QNdefMessage: ...
    @staticmethod
    def fromList(list: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    @staticmethod
    def fromVector(vector: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def front(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    def insert(self, arg__1: int, arg__2: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def isEmpty(self, /) -> bool: ...
    def isSharedWith(self, other: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> bool: ...
    @typing.overload
    def last(self, /) -> PySide6.QtNfc.QNdefRecord: ...
    @typing.overload
    def last(self, n: int, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def length(self, /) -> int: ...
    @staticmethod
    def maxSize() -> int: ...
    def max_size(self, /) -> int: ...
    def mid(self, pos: int, /, len: int = ...) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def move(self, from_: int, to: int, /) -> None: ...
    def pop_back(self, /) -> None: ...
    def pop_front(self, /) -> None: ...
    def prepend(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def push_back(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def push_front(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def remove(self, i: int, /, n: int = ...) -> None: ...
    def removeAll(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def removeAt(self, i: int, /) -> None: ...
    def removeFirst(self, /) -> None: ...
    def removeLast(self, /) -> None: ...
    def removeOne(self, arg__1: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    def reserve(self, size: int, /) -> None: ...
    def resize(self, size: int, /) -> None: ...
    def resizeForOverwrite(self, size: int, /) -> None: ...
    def shrink_to_fit(self, /) -> None: ...
    def size(self, /) -> int: ...
    @typing.overload
    def sliced(self, pos: int, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    @typing.overload
    def sliced(self, pos: int, n: int, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def squeeze(self, /) -> None: ...
    def swap(self, other: collections.abc.Sequence[PySide6.QtNfc.QNdefRecord], /) -> None: ...
    def swapItemsAt(self, i: int, j: int, /) -> None: ...
    def takeAt(self, i: int, /) -> PySide6.QtNfc.QNdefRecord: ...
    def toByteArray(self, /) -> PySide6.QtCore.QByteArray: ...
    def toList(self, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def toVector(self, /) -> typing.List[PySide6.QtNfc.QNdefRecord]: ...
    def value(self, i: int, /) -> PySide6.QtNfc.QNdefRecord: ...


class QNdefNfcIconRecord(PySide6.QtNfc.QNdefRecord):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QNdefNfcIconRecord: PySide6.QtNfc.QNdefNfcIconRecord, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def data(self, /) -> PySide6.QtCore.QByteArray: ...
    def setData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...


class QNdefNfcSmartPosterRecord(PySide6.QtNfc.QNdefRecord):

    class Action(enum.Enum):

        UnspecifiedAction         = ...  # -1
        DoAction                  = ...  # 0x0
        SaveAction                = ...  # 0x1
        EditAction                = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefNfcSmartPosterRecord, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def action(self, /) -> PySide6.QtNfc.QNdefNfcSmartPosterRecord.Action: ...
    @typing.overload
    def addIcon(self, icon: PySide6.QtNfc.QNdefNfcIconRecord | PySide6.QtNfc.QNdefRecord, /) -> None: ...
    @typing.overload
    def addIcon(self, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def addTitle(self, text: str, locale: str, encoding: PySide6.QtNfc.QNdefNfcTextRecord.Encoding, /) -> bool: ...
    @typing.overload
    def addTitle(self, text: PySide6.QtNfc.QNdefNfcTextRecord | PySide6.QtNfc.QNdefRecord, /) -> bool: ...
    def hasAction(self, /) -> bool: ...
    def hasIcon(self, /, mimetype: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> bool: ...
    def hasSize(self, /) -> bool: ...
    def hasTitle(self, /, locale: str = ...) -> bool: ...
    def hasTypeInfo(self, /) -> bool: ...
    def icon(self, /, mimetype: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> PySide6.QtCore.QByteArray: ...
    def iconCount(self, /) -> int: ...
    def iconRecord(self, index: int, /) -> PySide6.QtNfc.QNdefNfcIconRecord: ...
    def iconRecords(self, /) -> typing.List[PySide6.QtNfc.QNdefNfcIconRecord]: ...
    @typing.overload
    def removeIcon(self, icon: PySide6.QtNfc.QNdefNfcIconRecord | PySide6.QtNfc.QNdefRecord, /) -> bool: ...
    @typing.overload
    def removeIcon(self, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    @typing.overload
    def removeTitle(self, locale: str, /) -> bool: ...
    @typing.overload
    def removeTitle(self, text: PySide6.QtNfc.QNdefNfcTextRecord | PySide6.QtNfc.QNdefRecord, /) -> bool: ...
    def setAction(self, act: PySide6.QtNfc.QNdefNfcSmartPosterRecord.Action, /) -> None: ...
    def setIcons(self, icons: collections.abc.Sequence[PySide6.QtNfc.QNdefNfcIconRecord], /) -> None: ...
    def setPayload(self, payload: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setSize(self, size: int, /) -> None: ...
    def setTitles(self, titles: collections.abc.Sequence[PySide6.QtNfc.QNdefNfcTextRecord], /) -> None: ...
    def setTypeInfo(self, type: str, /) -> None: ...
    @typing.overload
    def setUri(self, url: PySide6.QtNfc.QNdefNfcUriRecord | PySide6.QtNfc.QNdefRecord, /) -> None: ...
    @typing.overload
    def setUri(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def size(self, /) -> int: ...
    def title(self, /, locale: str = ...) -> str: ...
    def titleCount(self, /) -> int: ...
    def titleRecord(self, index: int, /) -> PySide6.QtNfc.QNdefNfcTextRecord: ...
    def titleRecords(self, /) -> typing.List[PySide6.QtNfc.QNdefNfcTextRecord]: ...
    def typeInfo(self, /) -> str: ...
    def uri(self, /) -> PySide6.QtCore.QUrl: ...
    def uriRecord(self, /) -> PySide6.QtNfc.QNdefNfcUriRecord: ...


class QNdefNfcTextRecord(PySide6.QtNfc.QNdefRecord):

    class Encoding(enum.Enum):

        Utf8                      = ...  # 0x0
        Utf16                     = ...  # 0x1


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QNdefNfcTextRecord: PySide6.QtNfc.QNdefNfcTextRecord, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def encoding(self, /) -> PySide6.QtNfc.QNdefNfcTextRecord.Encoding: ...
    def locale(self, /) -> str: ...
    def setEncoding(self, encoding: PySide6.QtNfc.QNdefNfcTextRecord.Encoding, /) -> None: ...
    def setLocale(self, locale: str, /) -> None: ...
    def setText(self, text: str, /) -> None: ...
    def text(self, /) -> str: ...


class QNdefNfcUriRecord(PySide6.QtNfc.QNdefRecord):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QNdefNfcUriRecord: PySide6.QtNfc.QNdefNfcUriRecord, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def setUri(self, uri: PySide6.QtCore.QUrl | str, /) -> None: ...
    def uri(self, /) -> PySide6.QtCore.QUrl: ...


class QNdefRecord(Shiboken.Object):

    class TypeNameFormat(enum.Enum):

        Empty                     = ...  # 0x0
        NfcRtd                    = ...  # 0x1
        Mime                      = ...  # 0x2
        Uri                       = ...  # 0x3
        ExternalRtd               = ...  # 0x4
        Unknown                   = ...  # 0x5


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtNfc.QNdefRecord, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def __init__(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtNfc.QNdefRecord, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, other: PySide6.QtNfc.QNdefRecord, /) -> bool: ...
    def clear(self, /) -> None: ...
    def id(self, /) -> PySide6.QtCore.QByteArray: ...
    def isEmpty(self, /) -> bool: ...
    def payload(self, /) -> PySide6.QtCore.QByteArray: ...
    def setId(self, id: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setPayload(self, payload: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setType(self, type: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setTypeNameFormat(self, typeNameFormat: PySide6.QtNfc.QNdefRecord.TypeNameFormat, /) -> None: ...
    def type(self, /) -> PySide6.QtCore.QByteArray: ...
    def typeNameFormat(self, /) -> PySide6.QtNfc.QNdefRecord.TypeNameFormat: ...


class QNearFieldManager(PySide6.QtCore.QObject):

    adapterStateChanged      : typing.ClassVar[Signal] = ... # adapterStateChanged(QNearFieldManager::AdapterState)
    targetDetected           : typing.ClassVar[Signal] = ... # targetDetected(QNearFieldTarget*)
    targetDetectionStopped   : typing.ClassVar[Signal] = ... # targetDetectionStopped()
    targetLost               : typing.ClassVar[Signal] = ... # targetLost(QNearFieldTarget*)

    class AdapterState(enum.Enum):

        Offline                   = ...  # 0x1
        TurningOn                 = ...  # 0x2
        Online                    = ...  # 0x3
        TurningOff                = ...  # 0x4


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def isEnabled(self, /) -> bool: ...
    def isSupported(self, /, accessMethod: PySide6.QtNfc.QNearFieldTarget.AccessMethod = ...) -> bool: ...
    def setUserInformation(self, message: str, /) -> None: ...
    def startTargetDetection(self, accessMethod: PySide6.QtNfc.QNearFieldTarget.AccessMethod, /) -> bool: ...
    def stopTargetDetection(self, /, errorMessage: str = ...) -> None: ...


class QNearFieldTarget(PySide6.QtCore.QObject):

    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    error                    : typing.ClassVar[Signal] = ... # error(QNearFieldTarget::Error,QNearFieldTarget::RequestId)
    ndefMessageRead          : typing.ClassVar[Signal] = ... # ndefMessageRead(QNdefMessage)
    requestCompleted         : typing.ClassVar[Signal] = ... # requestCompleted(QNearFieldTarget::RequestId)

    class AccessMethod(enum.Flag):

        UnknownAccess             = ...  # 0x0
        NdefAccess                = ...  # 0x1
        TagTypeSpecificAccess     = ...  # 0x2
        AnyAccess                 = ...  # 0xff

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        UnknownError              = ...  # 0x1
        UnsupportedError          = ...  # 0x2
        TargetOutOfRangeError     = ...  # 0x3
        NoResponseError           = ...  # 0x4
        ChecksumMismatchError     = ...  # 0x5
        InvalidParametersError    = ...  # 0x6
        ConnectionError           = ...  # 0x7
        NdefReadError             = ...  # 0x8
        NdefWriteError            = ...  # 0x9
        CommandError              = ...  # 0xa
        TimeoutError              = ...  # 0xb
        UnsupportedTargetError    = ...  # 0xc

    class RequestId(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, other: PySide6.QtNfc.QNearFieldTarget.RequestId, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def __eq__(self, other: PySide6.QtNfc.QNearFieldTarget.RequestId, /) -> bool: ...
        def __lt__(self, other: PySide6.QtNfc.QNearFieldTarget.RequestId, /) -> bool: ...
        def __ne__(self, other: PySide6.QtNfc.QNearFieldTarget.RequestId, /) -> bool: ...
        def isValid(self, /) -> bool: ...
        def refCount(self, /) -> int: ...

    class Type(enum.Enum):

        ProprietaryTag            = ...  # 0x0
        NfcTagType1               = ...  # 0x1
        NfcTagType2               = ...  # 0x2
        NfcTagType3               = ...  # 0x3
        NfcTagType4               = ...  # 0x4
        NfcTagType4A              = ...  # 0x5
        NfcTagType4B              = ...  # 0x6
        MifareTag                 = ...  # 0x7


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def accessMethods(self, /) -> PySide6.QtNfc.QNearFieldTarget.AccessMethod: ...
    def disconnect(self, /) -> bool: ...
    def hasNdefMessage(self, /) -> bool: ...
    def maxCommandLength(self, /) -> int: ...
    def readNdefMessages(self, /) -> PySide6.QtNfc.QNearFieldTarget.RequestId: ...
    def requestResponse(self, id: PySide6.QtNfc.QNearFieldTarget.RequestId, /) -> typing.Any: ...
    def sendCommand(self, command: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtNfc.QNearFieldTarget.RequestId: ...
    def type(self, /) -> PySide6.QtNfc.QNearFieldTarget.Type: ...
    def uid(self, /) -> PySide6.QtCore.QByteArray: ...
    def waitForRequestCompleted(self, id: PySide6.QtNfc.QNearFieldTarget.RequestId, /, msecs: int = ...) -> bool: ...
    def writeNdefMessages(self, messages: collections.abc.Sequence[PySide6.QtNfc.QNdefMessage], /) -> PySide6.QtNfc.QNearFieldTarget.RequestId: ...


# eof
