[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "ColorDialog"}, {"name": "QML.Extended", "value": "QColorDialogOptions"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformColorDialog", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "currentColor", "notify": "currentColorChanged", "read": "currentColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setCurrentColor"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "scriptable": true, "stored": true, "type": "QColorDialogOptions::ColorDialogOptions", "user": false, "write": "setOptions"}], "qualifiedClassName": "QQuickLabsPlatformColorDialog", "signals": [{"access": "public", "index": 0, "name": "colorChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "currentColorChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "optionsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickLabsPlatformDialog"}]}], "inputFile": "qquicklabsplatformcolordialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Dialog"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Dialog is an abstract base class"}, {"name": "DefaultProperty", "value": "data"}], "className": "QQuickLabsPlatformDialog", "enums": [{"isClass": false, "isFlag": false, "name": "StandardCode", "values": ["Rejected", "Accepted"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "parentWindow", "notify": "parentWindowChanged", "read": "parentWindow", "required": false, "scriptable": true, "stored": true, "type": "QWindow*", "user": false, "write": "setParentWindow"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "flags", "notify": "flagsChanged", "read": "flags", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowFlags", "user": false, "write": "setFlags"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "modality", "notify": "modalityChanged", "read": "modality", "required": false, "scriptable": true, "stored": true, "type": "Qt::WindowModality", "user": false, "write": "setModality"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "result", "notify": "resultC<PERSON>ed", "read": "result", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setResult"}], "qualifiedClassName": "QQuickLabsPlatformDialog", "signals": [{"access": "public", "index": 0, "name": "accepted", "returnType": "void"}, {"access": "public", "index": 1, "name": "rejected", "returnType": "void"}, {"access": "public", "index": 2, "name": "parentWindowChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "flagsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "modalityChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "resultC<PERSON>ed", "returnType": "void"}], "slots": [{"access": "public", "index": 8, "name": "open", "returnType": "void"}, {"access": "public", "index": 9, "name": "close", "returnType": "void"}, {"access": "public", "index": 10, "name": "accept", "returnType": "void"}, {"access": "public", "index": 11, "name": "reject", "returnType": "void"}, {"access": "public", "arguments": [{"name": "result", "type": "int"}], "index": 12, "name": "done", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicklabsplatformdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FileDialog"}, {"name": "QML.Extended", "value": "QFileDialogOptions"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformFileDialog", "enums": [{"isClass": false, "isFlag": false, "name": "FileMode", "values": ["OpenFile", "OpenFiles", "SaveFile"]}], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "fileMode", "notify": "fileModeChanged", "read": "fileMode", "required": false, "scriptable": true, "stored": true, "type": "FileMode", "user": false, "write": "setFileMode"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "file", "notify": "fileChanged", "read": "file", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setFile"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "files", "notify": "filesChanged", "read": "files", "required": false, "scriptable": true, "stored": true, "type": "QList<QUrl>", "user": false, "write": "setFiles"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "currentFile", "notify": "currentFileChanged", "read": "currentFile", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setCurrentFile"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "currentFiles", "notify": "currentFilesChanged", "read": "currentFiles", "required": false, "scriptable": true, "stored": true, "type": "QList<QUrl>", "user": false, "write": "setCurrentFiles"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "folder", "notify": "folderChanged", "read": "folder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setFolder"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QFileDialogOptions::FileDialogOptions", "user": false, "write": "setOptions"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "nameFilters", "notify": "nameFiltersChanged", "read": "nameFilters", "required": false, "reset": "resetNameFilters", "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setNameFilters"}, {"constant": true, "designable": true, "final": true, "index": 8, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformFileNameFilter*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "defaultSuffix", "notify": "defaultSuffixChanged", "read": "defaultSuffix", "required": false, "reset": "resetDefaultSuffix", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDefaultSuffix"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "acceptLabel", "notify": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "read": "acceptLabel", "required": false, "reset": "resetAcceptLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAcceptLabel"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "reset": "resetRejectLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRejectLabel"}], "qualifiedClassName": "QQuickLabsPlatformFileDialog", "signals": [{"access": "public", "index": 0, "name": "fileModeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "fileChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "filesChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "currentFileChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "currentFilesChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "folderChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "optionsChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "nameFiltersChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "defaultSuffixChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 10, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickLabsPlatformDialog"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQuickLabsPlatformFileNameFilter", "lineNumber": 127, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "index", "notify": "indexChanged", "read": "index", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setIndex"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "extensions", "notify": "extensionsChanged", "read": "extensions", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "QQuickLabsPlatformFileNameFilter", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 0, "name": "indexChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 1, "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extensions", "type": "QStringList"}], "index": 2, "name": "extensionsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquicklabsplatformfiledialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FolderDialog"}, {"name": "QML.Extended", "value": "QFileDialogOptions"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformFolderDialog", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "folder", "notify": "folderChanged", "read": "folder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setFolder"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "currentFolder", "notify": "currentFolderChanged", "read": "currentFolder", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setCurrentFolder"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "reset": "resetOptions", "scriptable": true, "stored": true, "type": "QFileDialogOptions::FileDialogOptions", "user": false, "write": "setOptions"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "acceptLabel", "notify": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "read": "acceptLabel", "required": false, "reset": "resetAcceptLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAcceptLabel"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "reset": "resetRejectLabel", "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRejectLabel"}], "qualifiedClassName": "QQuickLabsPlatformFolderDialog", "signals": [{"access": "public", "index": 0, "name": "folderChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "currentFolderChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "optionsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "accept<PERSON>abel<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickLabsPlatformDialog"}]}], "inputFile": "qquicklabsplatformfolderdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FontDialog"}, {"name": "QML.Extended", "value": "QFontDialogOptions"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformFontDialog", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "currentFont", "notify": "currentFontChanged", "read": "currentFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setCurrentFont"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "options", "notify": "optionsChanged", "read": "options", "required": false, "scriptable": true, "stored": true, "type": "QFontDialogOptions::FontDialogOptions", "user": false, "write": "setOptions"}], "qualifiedClassName": "QQuickLabsPlatformFontDialog", "signals": [{"access": "public", "index": 0, "name": "fontChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "currentFontChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "optionsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickLabsPlatformDialog"}]}], "inputFile": "qquicklabsplatformfontdialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQuickLabsPlatformIcon", "gadget": true, "lineNumber": 28, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "source", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "mask", "read": "isMask", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMask"}], "qualifiedClassName": "QQuickLabsPlatformIcon"}], "inputFile": "qquicklabsplatformicon_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON>"}, {"name": "QML.Extended", "value": "QPlatformMenu"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}, {"name": "DefaultProperty", "value": "data"}], "className": "QQuickLabsPlatformMenu", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 42, "methods": [{"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 16, "name": "addItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 17, "name": "insertItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 18, "name": "removeItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 19, "name": "addMenu", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 20, "name": "insertMenu", "returnType": "void"}, {"access": "public", "arguments": [{"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 21, "name": "removeMenu", "returnType": "void"}, {"access": "public", "index": 22, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "items", "notify": "itemsChanged", "read": "items", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickLabsPlatformMenuItem>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "menuBar", "notify": "menuBarChanged", "read": "menuBar", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenuBar*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "parentMenu", "notify": "parentMenuChanged", "read": "parentMenu", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenu*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "systemTrayIcon", "notify": "systemTrayIconChanged", "read": "systemTrayIcon", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformSystemTrayIcon*", "user": false}, {"constant": true, "designable": true, "final": true, "index": 5, "name": "menuItem", "read": "menuItem", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenuItem*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "enabled", "notify": "enabledChanged", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "minimumWidth", "notify": "minimumWidthChanged", "read": "minimumWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinimumWidth"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QPlatformMenu::MenuType", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformIcon", "user": false, "write": "setIcon"}], "qualifiedClassName": "QQuickLabsPlatformMenu", "signals": [{"access": "public", "index": 0, "name": "aboutToShow", "returnType": "void"}, {"access": "public", "index": 1, "name": "aboutToHide", "returnType": "void"}, {"access": "public", "index": 2, "name": "itemsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "menuBarChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "parentMenuChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "systemTrayIconChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "minimumWidthChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "fontChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "typeChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "iconChanged", "returnType": "void", "revision": 257}], "slots": [{"access": "public", "arguments": [{"name": "args", "type": "QQmlV4FunctionPtr"}], "index": 13, "name": "open", "returnType": "void"}, {"access": "public", "index": 14, "name": "close", "returnType": "void"}, {"access": "private", "index": 15, "name": "updateIcon", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicklabsplatformmenu_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "DefaultProperty", "value": "data"}], "className": "QQuickLabsPlatformMenuBar", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 30, "methods": [{"access": "public", "arguments": [{"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 2, "name": "addMenu", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 3, "name": "insertMenu", "returnType": "void"}, {"access": "public", "arguments": [{"name": "menu", "type": "QQuickLabsPlatformMenu*"}], "index": 4, "name": "removeMenu", "returnType": "void"}, {"access": "public", "index": 5, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "menus", "notify": "menusChanged", "read": "menus", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickLabsPlatformMenu>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "window", "notify": "windowChanged", "read": "window", "required": false, "scriptable": true, "stored": true, "type": "QWindow*", "user": false, "write": "setWindow"}], "qualifiedClassName": "QQuickLabsPlatformMenuBar", "signals": [{"access": "public", "index": 0, "name": "menusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "windowChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicklabsplatformmenubar_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MenuItem"}, {"name": "QML.Extended", "value": "QPlatformMenuItem"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformMenuItem", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 34, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "menu", "notify": "menuChanged", "read": "menu", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenu*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "subMenu", "notify": "subMenuChanged", "read": "subMenu", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenu*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "group", "notify": "groupChanged", "read": "group", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenuItemGroup*", "user": false, "write": "setGroup"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "enabled", "notify": "enabledChanged", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "separator", "notify": "separator<PERSON>hanged", "read": "isSeparator", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSeparator"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "checkable", "notify": "checkableChanged", "read": "isCheckable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCheckable"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "checked", "notify": "checkedChanged", "read": "isChecked", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setChecked"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "role", "notify": "<PERSON><PERSON><PERSON><PERSON>", "read": "role", "required": false, "scriptable": true, "stored": true, "type": "QPlatformMenuItem::MenuRole", "user": false, "write": "setRole"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "shortcut", "notify": "shortcutChanged", "read": "shortcut", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setShortcut"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformIcon", "user": false, "write": "setIcon"}], "qualifiedClassName": "QQuickLabsPlatformMenuItem", "signals": [{"access": "public", "index": 0, "name": "triggered", "returnType": "void"}, {"access": "public", "index": 1, "name": "hovered", "returnType": "void"}, {"access": "public", "index": 2, "name": "menuChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "subMenuChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "groupChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "separator<PERSON>hanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "checkableChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "checkedChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 11, "name": "textChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "shortcutChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "fontChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "iconChanged", "returnType": "void", "revision": 257}], "slots": [{"access": "public", "index": 15, "name": "toggle", "returnType": "void"}, {"access": "private", "index": 16, "name": "activate", "returnType": "void"}, {"access": "private", "index": 17, "name": "updateIcon", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicklabsplatformmenuitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MenuItemGroup"}], "className": "QQuickLabsPlatformMenuItemGroup", "lineNumber": 28, "methods": [{"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 7, "name": "addItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 8, "name": "removeItem", "returnType": "void"}, {"access": "public", "index": 9, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "enabled", "notify": "enabledChanged", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "exclusive", "notify": "exclusiveChanged", "read": "isExclusive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setExclusive"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "checkedItem", "notify": "checkedItemChanged", "read": "checkedItem", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenuItem*", "user": false, "write": "setCheckedItem"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "items", "notify": "itemsChanged", "read": "items", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickLabsPlatformMenuItem>", "user": false}], "qualifiedClassName": "QQuickLabsPlatformMenuItemGroup", "signals": [{"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 0, "name": "triggered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickLabsPlatformMenuItem*"}], "index": 1, "name": "hovered", "returnType": "void"}, {"access": "public", "index": 2, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "exclusiveChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "checkedItemChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "itemsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquicklabsplatformmenuitemgroup_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MenuSeparator"}], "className": "QQuickLabsPlatformMenuSeparator", "lineNumber": 22, "object": true, "qualifiedClassName": "QQuickLabsPlatformMenuSeparator", "superClasses": [{"access": "public", "name": "QQuickLabsPlatformMenuItem"}]}], "inputFile": "qquicklabsplatformmenuseparator_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MessageDialog"}, {"name": "QML.Extended", "value": "QPlatformDialogHelper"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformMessageDialog", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "informativeText", "notify": "informativeTextChanged", "read": "informativeText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setInformativeText"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "detailedText", "notify": "detailedTextChanged", "read": "detailedText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDetailedText"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "buttons", "notify": "buttonsChanged", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "QPlatformDialogHelper::StandardButtons", "user": false, "write": "setButtons"}], "qualifiedClassName": "QQuickLabsPlatformMessageDialog", "signals": [{"access": "public", "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "informativeTextChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "detailedTextChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "buttonsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "button", "type": "QPlatformDialogHelper::StandardButton"}], "index": 4, "name": "clicked", "returnType": "void"}, {"access": "public", "index": 5, "name": "okClicked", "returnType": "void"}, {"access": "public", "index": 6, "name": "saveClicked", "returnType": "void"}, {"access": "public", "index": 7, "name": "saveAllClicked", "returnType": "void"}, {"access": "public", "index": 8, "name": "openClicked", "returnType": "void"}, {"access": "public", "index": 9, "name": "yesClicked", "returnType": "void"}, {"access": "public", "index": 10, "name": "yesToAllClicked", "returnType": "void"}, {"access": "public", "index": 11, "name": "noClicked", "returnType": "void"}, {"access": "public", "index": 12, "name": "noToAllClicked", "returnType": "void"}, {"access": "public", "index": 13, "name": "abortClicked", "returnType": "void"}, {"access": "public", "index": 14, "name": "retryClicked", "returnType": "void"}, {"access": "public", "index": 15, "name": "ignoreClicked", "returnType": "void"}, {"access": "public", "index": 16, "name": "closeClicked", "returnType": "void"}, {"access": "public", "index": 17, "name": "cancelClicked", "returnType": "void"}, {"access": "public", "index": 18, "name": "discardClicked", "returnType": "void"}, {"access": "public", "index": 19, "name": "helpClicked", "returnType": "void"}, {"access": "public", "index": 20, "name": "applyClicked", "returnType": "void"}, {"access": "public", "index": 21, "name": "resetClicked", "returnType": "void"}, {"access": "public", "index": 22, "name": "restoreDefaultsClicked", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "button", "type": "QPlatformDialogHelper::StandardButton"}], "index": 23, "name": "handleClick", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickLabsPlatformDialog"}]}], "inputFile": "qquicklabsplatformmessagedialog_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Singleton", "value": "true"}, {"name": "QML.Element", "value": "StandardPaths"}, {"name": "QML.Extended", "value": "QS<PERSON>dard<PERSON><PERSON><PERSON>"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformStandardPaths", "lineNumber": 31, "methods": [{"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 0, "name": "displayName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "executableName", "type": "QString"}, {"name": "paths", "type": "QStringList"}], "index": 1, "name": "findExecutable", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "executableName", "type": "QString"}], "index": 2, "isCloned": true, "name": "findExecutable", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}, {"name": "options", "type": "QStandardPaths::LocateOptions"}], "index": 3, "name": "locate", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}], "index": 4, "isCloned": true, "name": "locate", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}, {"name": "options", "type": "QStandardPaths::LocateOptions"}], "index": 5, "name": "locateAll", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}], "index": 6, "isCloned": true, "name": "locateAll", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "testMode", "type": "bool"}], "index": 7, "name": "setTestModeEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 8, "name": "standardLocations", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 9, "name": "writableLocation", "returnType": "QUrl"}], "object": true, "qualifiedClassName": "QQuickLabsPlatformStandardPaths", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquicklabsplatformstandardpaths_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SystemTrayIcon"}, {"name": "QML.Extended", "value": "QPlatformSystemTrayIcon"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQuickLabsPlatformSystemTrayIcon", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 33, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "available", "read": "isAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "supportsMessages", "read": "supportsMessages", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "tooltip", "notify": "tooltipChanged", "read": "tooltip", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTooltip"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "menu", "notify": "menuChanged", "read": "menu", "required": false, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformMenu*", "user": false, "write": "setMenu"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "geometry", "notify": "geometryChanged", "read": "geometry", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickLabsPlatformIcon", "user": false, "write": "setIcon"}], "qualifiedClassName": "QQuickLabsPlatformSystemTrayIcon", "signals": [{"access": "public", "arguments": [{"name": "reason", "type": "QPlatformSystemTrayIcon::ActivationReason"}], "index": 0, "name": "activated", "returnType": "void"}, {"access": "public", "index": 1, "name": "messageClicked", "returnType": "void"}, {"access": "public", "index": 2, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "tooltipChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "menuChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "geometryChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 6, "name": "iconChanged", "returnType": "void", "revision": 257}], "slots": [{"access": "public", "index": 7, "name": "show", "returnType": "void"}, {"access": "public", "index": 8, "name": "hide", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}, {"name": "message", "type": "QString"}, {"name": "iconType", "type": "QPlatformSystemTrayIcon::MessageIcon"}, {"name": "msecs", "type": "int"}], "index": 9, "name": "showMessage", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}, {"name": "message", "type": "QString"}, {"name": "iconType", "type": "QPlatformSystemTrayIcon::MessageIcon"}], "index": 10, "isCloned": true, "name": "showMessage", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}, {"name": "message", "type": "QString"}], "index": 11, "isCloned": true, "name": "showMessage", "returnType": "void"}, {"access": "private", "index": 12, "name": "updateIcon", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquicklabsplatformsystemtrayicon_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformColorDialog", "lineNumber": 25, "object": true, "qualifiedClassName": "QWidgetPlatformColorDialog", "superClasses": [{"access": "public", "name": "QPlatformColorDialogHelper"}]}], "inputFile": "qwidgetplatformcolordialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformFileDialog", "lineNumber": 25, "object": true, "qualifiedClassName": "QWidgetPlatformFileDialog", "superClasses": [{"access": "public", "name": "QPlatformFileDialogHelper"}]}], "inputFile": "qwidgetplatformfiledialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformFontDialog", "lineNumber": 25, "object": true, "qualifiedClassName": "QWidgetPlatformFontDialog", "superClasses": [{"access": "public", "name": "QPlatformFontDialogHelper"}]}], "inputFile": "qwidgetplatformfontdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformMenu", "lineNumber": 26, "object": true, "qualifiedClassName": "QWidgetPlatformMenu", "superClasses": [{"access": "public", "name": "QPlatformMenu"}]}], "inputFile": "qwidgetplatformmenu_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformMenuItem", "lineNumber": 25, "object": true, "qualifiedClassName": "QWidgetPlatformMenuItem", "superClasses": [{"access": "public", "name": "QPlatformMenuItem"}]}], "inputFile": "qwidgetplatformmenuitem_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformMessageDialog", "lineNumber": 25, "object": true, "qualifiedClassName": "QWidgetPlatformMessageDialog", "superClasses": [{"access": "public", "name": "QPlatformMessageDialogHelper"}]}], "inputFile": "qwidgetplatformmessagedialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QWidgetPlatformSystemTrayIcon", "lineNumber": 27, "object": true, "qualifiedClassName": "QWidgetPlatformSystemTrayIcon", "superClasses": [{"access": "public", "name": "QPlatformSystemTrayIcon"}]}], "inputFile": "qwidgetplatformsystemtrayicon_p.h", "outputRevision": 69}]