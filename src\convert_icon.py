from PIL import Image, ImageEnhance, ImageFilter
import os
from pathlib import Path

def convert_to_ico():
    project_root = Path(__file__).parent
    png_path = project_root / 'resources' / 'icons' / 'logo.png'
    ico_path = project_root / 'resources' / 'icons' / 'logo.ico'
    
    if not png_path.exists():
        print(f"Error: {png_path} not found")
        return
        
    # Open the PNG image
    img = Image.open(png_path)
    
    # Create multiple sizes with emphasis on larger sizes
    sizes = [
        (512, 512),  # Primary size for high DPI
        (256, 256),  # Secondary size
        (128, 128),  # Medium size
        (64, 64),    # Small size
        (48, 48),    # Smaller size
        (32, 32),    # Tiny size
        (16, 16)     # Smallest size
    ]
    
    # Create a list to store different size images
    img_list = []
    
    # Ensure source image is large enough
    if img.size[0] < 512 or img.size[1] < 512:
        # Upscale image if it's too small
        img = img.resize((512, 512), Image.Resampling.LANCZOS)
    
    # Convert to RGBA if not already
    if img.mode != 'RGBA':
        img = img.convert('RGBA')
    
    for size in sizes:
        # Create a clean copy for this size
        work_img = img.copy()
        
        # Enhanced processing for each size
        if size[0] < img.size[0]:
            # Stronger pre-processing for smaller sizes
            work_img = work_img.filter(ImageFilter.UnsharpMask(radius=2, percent=150, threshold=3))
        
        # Use high-quality resizing
        resized_img = work_img.resize(size, Image.Resampling.LANCZOS)
        
        # Enhanced image processing
        enhancer = ImageEnhance.Sharpness(resized_img)
        resized_img = enhancer.enhance(1.5)  # Increased sharpness
        
        enhancer = ImageEnhance.Contrast(resized_img)
        resized_img = enhancer.enhance(1.2)  # Increased contrast
        
        enhancer = ImageEnhance.Color(resized_img)
        resized_img = enhancer.enhance(1.2)  # Increased color vibrance
        
        img_list.append(resized_img)
    
    # Save as ICO with largest size first and explicit size declaration
    img_list[0].save(
        ico_path,
        format='ICO',
        sizes=[(512, 512), (256, 256), (128, 128), (64, 64), (48, 48), (32, 32), (16, 16)],
        append_images=img_list[1:],
        optimize=False
    )
    
    print(f"Converted {png_path} to {ico_path}")
    print(f"Icon sizes included: {sizes}")
    print("Icon conversion complete with maximum quality settings")

if __name__ == "__main__":
    try:
        convert_to_ico()
    except ImportError:
        print("Error: Required PIL modules not found. Please run: pip install Pillow")
    except Exception as e:
        print(f"Error during conversion: {e}")
