import QtQuick 2.15
import QtQuick.Controls 2.15

Menu {
    id: rootMenu

    // Properties to be passed from parent
    property var cameraView
    property var drawingCanvas
    property var cameraArea
    property var cameraWindow

    // Function to save image to specified file path
    function saveImageToFile(filePath) {
        try {
            // Create a combined image of the camera view and canvas drawings
            var grabResult = cameraArea.grabToImage(function (result) {
                    // Save the image using the selected path
                    var saved = result.saveToFile(filePath);
                    if (saved) {
                        console.log("Image saved successfully to: " + filePath);
                    } else {
                        console.error("Failed to save image to: " + filePath);
                    }
                });
            if (!grabResult) {
                console.error("Failed to grab the camera view");
            }
        } catch (error) {
            console.error("Error saving image:", error);
        }
    }

    // Function to save to a default location (Pictures folder with timestamp)
    function saveImageToDefaultLocation() {
        var timestamp = new Date().toISOString().replace(/[:.]/g, "-");
        var defaultFileName = "camera_image_" + serialNumber + "_" + timestamp + ".jpg";
        var defaultPath = workspaceDatabase.get_pictures_folder() + "/" + defaultFileName;
        saveImageToFile(defaultPath);
    }

    // Menu items
    MenuItem {
        text: "Zoom In"
        onTriggered: {
            var newScale = cameraView.scale + cameraArea.zoomStep;
            newScale = Math.min(newScale, cameraArea.maxZoom);

            // Apply zoom to both camera view and drawing canvas
            cameraView.scale = newScale;
            drawingCanvas.scale = newScale;
            cameraArea.zoomFactor = newScale;
        }
    }

    MenuItem {
        text: "Zoom Out"
        onTriggered: {
            var newScale = cameraView.scale - cameraArea.zoomStep;
            newScale = Math.max(newScale, cameraArea.minZoom);

            // Apply zoom to both camera view and drawing canvas
            cameraView.scale = newScale;
            drawingCanvas.scale = newScale;
            cameraArea.zoomFactor = newScale;
        }
    }

    MenuItem {
        text: "Save"
        onTriggered: {
            try {
                var component = Qt.createComponent("./dialogs/SaveFileDialog.qml");
                if (component.status === Component.Ready) {
                    var saveDialog = component.createObject(cameraWindow, {
                        nameFilters: ["JPEG Images (*.jpg)"],
                        defaultSuffix: "jpg",
                        title: "Save Camera Image"
                    });
                    saveDialog.accepted.connect(function () {
                        var filePath = saveDialog.file.toString();
                        // Remove the "file:///" prefix if it exists
                        filePath = filePath.replace(/^(file:\/{3})/, "");
                        rootMenu.saveImageToFile(filePath);
                    });
                    saveDialog.open();
                } else if (component.status === Component.Error) {
                    console.error("Error creating dialog:", component.errorString());
                    rootMenu.saveImageToDefaultLocation();
                }
            } catch (error) {
                console.error("Exception when creating dialog:", error);
                rootMenu.saveImageToDefaultLocation();
            }
        }
    }
}