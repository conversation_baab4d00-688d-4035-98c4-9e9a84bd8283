import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    implicitHeight: 60
    color: "lightgray"

    property string title: "Title"
    property real sliderFrom: 0.0
    property real sliderTo: 100.0
    property real sliderValue: 50.0
    property real sliderStepSize: 1.0
    property real sliderRate: 100.0
    property bool enabled: true

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 5

        Text {
            Layout.alignment: Qt.AlignHCenter
            text: title
            color: "black"
        }

        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true

            Slider {
                id: digitalSlider
                Layout.fillWidth: true
                Layout.preferredWidth: 1.5
                from: sliderFrom * sliderRate
                to: sliderTo * sliderRate
                stepSize: sliderStepSize
                value: sliderValue * sliderRate
                enabled: root.enabled
                onMoved: {
                    digitalSpinBox.value = digitalSlider.value;
                    sliderValue = digitalSlider.value / sliderRate;
                }
            }

            SpinBox {
                id: digitalSpinBox
                Layout.fillWidth: true
                Layout.preferredWidth: 1
                from: sliderFrom * sliderRate
                to: sliderTo * sliderRate
                stepSize: sliderStepSize
                value: sliderValue * sliderRate
                enabled: root.enabled
                editable: true
                valueFromText: function(text) {
                    return parseFloat(text) * sliderRate;
                }
                textFromValue: function(value) {
                    if ((value / 100) % 1 === 0) {
                        return (value / sliderRate).toFixed(0);
                    }
                    if ((value / 10) % 1 === 0) {
                        return (value / sliderRate).toFixed(1);
                    }
                    return (value / sliderRate).toFixed(2);
                }
                onValueModified: {
                    digitalSlider.value = value;
                    sliderValue = value / sliderRate;
                }
            }
        }
    }
}
