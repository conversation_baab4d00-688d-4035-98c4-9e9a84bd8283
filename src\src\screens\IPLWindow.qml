import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../constants/WindowsConstants.js" as WindowsConstants
import "../constants/NumberConstants.js" as NumberConstants

Rectangle {
    id: iplWindow
    width: WindowsConstants.subWindowWidth
    Layout.fillHeight: true
    color: "lightgray"

    signal iPLClosed
    property string activeSerialNumber: ""

    // Add property to track rotation
    property int currentRotation: 0
    property int cameraspaceId: -1  // This should be set when creating the IPLWindow

    Component.onCompleted: {
        // Load saved IPL settings when window is created
        var iplSettings = workspaceDatabase.get_camera_ipl_settings(activeSerialNumber, cameraspaceId);
        if (iplSettings) {
            flipHorizontalBtn.checked = iplSettings.flip_h === 1;
            flipVerticalBtn.checked = iplSettings.flip_v === 1;
            currentRotation = iplSettings.rotation;
        }
    }

    // Add function to rotate shapes
    function rotateCanvasShapes(iCameraspaceId, canvas, angle) {
        var angleRad = angle * Math.PI / 180;
        var cosAngle = Math.cos(angleRad);
        var sinAngle = Math.sin(angleRad);

        for (var i = 0; i < canvas.paths.length; i++) {
            var path = canvas.paths[i];

            var centerX = path.w / 2;
            var centerY = path.h / 2;
            var newCenterX = path.h / 2;
            var newCenterY = path.w / 2;

            switch (path.type) {
                case NumberConstants.tbDrawingType.pen:
                    // Rotate each point in the pen path
                    for (var j = 0; j < path.path.length; j++) {
                        var x = path.path[j].x - centerX;
                        var y = path.path[j].y - centerY;
                        path.path[j].x = newCenterX + (x * cosAngle - y * sinAngle);
                        path.path[j].y = newCenterY + (x * sinAngle + y * cosAngle);
                    }
                    break;

                case NumberConstants.tbDrawingType.line:
                case NumberConstants.tbDrawingType.distance:
                case NumberConstants.tbDrawingType.rectangle:
                case NumberConstants.tbDrawingType.circle:
                    // Rotate start and end points
                    var x1 = path.startX - centerX;
                    var y1 = path.startY - centerY;
                    var x2 = path.endX - centerX;
                    var y2 = path.endY - centerY;

                    path.startX = newCenterX + (x1 * cosAngle - y1 * sinAngle);
                    path.startY = newCenterY + (x1 * sinAngle + y1 * cosAngle);
                    path.endX = newCenterX + (x2 * cosAngle - y2 * sinAngle);
                    path.endY = newCenterY + (x2 * sinAngle + y2 * cosAngle);
                    break;

                case NumberConstants.tbDrawingType.text:
                    // Rotate text position
                    var x = path.x - centerX;
                    var y = path.y - centerY;
                    path.x = newCenterX + (x * cosAngle - y * sinAngle);
                    path.y = newCenterY + (x * sinAngle + y * cosAngle);
                    break;
            }

            var temp = path.w
            path.w = path.h;
            path.h = temp;

            var pathCopy = JSON.parse(JSON.stringify(path));
            workspaceDatabase.update_drawing(iCameraspaceId, i+1, JSON.stringify(pathCopy));
        }

        // Force canvas to update
        canvas.paths = [...canvas.paths];
        canvas.requestPaint();
    }

    Column {
        anchors.fill: parent

        Titlebar {
            title: "Host features"
            titleIcon: "qrc:/resources/icons/IPL.png"
            onSubWindowClosed: iPLClosed()
        }

        Rectangle {
            width: parent.width
            height: parent.height - WindowsConstants.topBarHeight
            color: "lightgray"

            Rectangle {
                width: parent.width
                height: 60
                color: "transparent"

                Column {
                    anchors.centerIn: parent
                    spacing: 5

                    Text {
                        text: "Image transformations"
                        color: "black"
                        horizontalAlignment: Text.AlignHCenter
                        width: parent.width
                    }

                    Row {
                        anchors.horizontalCenter: parent.horizontalCenter
                        spacing: 10

                        ToolButton {
                            id: flipHorizontalBtn
                            icon.source: "qrc:/resources/icons/left_right.png"
                            enabled: activeSerialNumber !== ""
                            checkable: true
                            onClicked: {
                                cameraListModel.flip_horizontal(activeSerialNumber);

                                var serialWindows = mainWindow.findCameraWindowsBySerialNum(activeSerialNumber);
                                for (var l = 0; l < serialWindows.length; l++) {
                                    workspaceDatabase.set_camera_ipl_status(serialWindows[l].cameraspaceId, activeSerialNumber, "flip_h", checked ? 1 : 0);

                                    var canvas = serialWindows[l].drawingCanvas;
                                    for (var i = 0; i < canvas.paths.length; i++) {
                                        var path = canvas.paths[i];
                                        switch (path.type) {
                                            case NumberConstants.tbDrawingType.pen:
                                                for (var j = 0; j < path.path.length; j++) {
                                                    path.path[j].x = path.w - path.path[j].x;
                                                }
                                                break;
                                            case NumberConstants.tbDrawingType.text:
                                                path.x = path.w - path.x;
                                                break;
                                            case NumberConstants.tbDrawingType.line:
                                            case NumberConstants.tbDrawingType.distance:
                                            case NumberConstants.tbDrawingType.rectangle:
                                            case NumberConstants.tbDrawingType.circle:
                                                path.startX = path.w - path.startX;
                                                path.endX = path.w - path.endX;
                                                break;
                                        }

                                        var pathCopy = JSON.parse(JSON.stringify(path));
                                        workspaceDatabase.update_drawing(serialWindows[l].cameraspaceId, i+1, JSON.stringify(pathCopy));
                                    }
                                    canvas.paths = [...canvas.paths];
                                    canvas.requestPaint();
                                }
                            }
                        }

                        ToolButton {
                            id: flipVerticalBtn
                            icon.source: "qrc:/resources/icons/up_down.png"
                            enabled: activeSerialNumber !== ""
                            checkable: true
                            onClicked: {
                                cameraListModel.flip_vertical(activeSerialNumber);

                                var serialWindows = mainWindow.findCameraWindowsBySerialNum(activeSerialNumber);
                                for (var l = 0; l < serialWindows.length; l++) {
                                    workspaceDatabase.set_camera_ipl_status(serialWindows[l].cameraspaceId, activeSerialNumber, "flip_v", checked ? 1 : 0);
                                    
                                    var canvas = serialWindows[l].drawingCanvas;
                                    for (var i = 0; i < canvas.paths.length; i++) {
                                        var path = canvas.paths[i];
                                        switch (path.type) {
                                            case NumberConstants.tbDrawingType.pen:
                                                for (var j = 0; j < path.path.length; j++) {
                                                    path.path[j].y = path.h - path.path[j].y;
                                                }
                                                break;
                                            case NumberConstants.tbDrawingType.text:
                                                path.y = path.h - path.y;
                                                break;
                                            case NumberConstants.tbDrawingType.line:
                                            case NumberConstants.tbDrawingType.distance:
                                                path.startY = path.h - path.startY;
                                                path.endY = path.h - path.endY;
                                                break;
                                            case NumberConstants.tbDrawingType.rectangle:
                                            case NumberConstants.tbDrawingType.circle:
                                                path.startY = path.h - path.startY;
                                                path.endY = path.h - path.endY;
                                                break;
                                        }

                                        var pathCopy = JSON.parse(JSON.stringify(path));
                                        workspaceDatabase.update_drawing(serialWindows[l].cameraspaceId, i+1, JSON.stringify(pathCopy));
                                    }
                                    canvas.paths = [...canvas.paths];
                                    canvas.requestPaint();
                                }
                            }
                        }

                        ToolButton {
                            id: rotateLeftBtn
                            icon.source: "qrc:/resources/icons/rotate_left.png"
                            enabled: activeSerialNumber !== ""
                            onClicked: {
                                var cameraWindow = mainWindow.findCameraWindowsBySerialNum(activeSerialNumber);
                                cameraListModel.rotate_left(activeSerialNumber);
                                currentRotation = (currentRotation + 90) % 360; // Same as camera device rotate_left

                                var serialWindows = mainWindow.findCameraWindowsBySerialNum(activeSerialNumber);
                                for (var l = 0; l < serialWindows.length; l++) {
                                    workspaceDatabase.set_camera_ipl_status(serialWindows[l].cameraspaceId, activeSerialNumber, "rotation", currentRotation);
                                    
                                    var canvas = serialWindows[l].drawingCanvas;
                                    // Update rotation value (-90 degrees)
                                    rotateCanvasShapes(serialWindows[l].cameraspaceId, canvas, -90);
                                }
                            }
                        }

                        ToolButton {
                            id: rotateRightBtn
                            icon.source: "qrc:/resources/icons/rotate_right.png"
                            enabled: activeSerialNumber !== ""
                            onClicked: {
                                cameraListModel.rotate_right(activeSerialNumber);
                                currentRotation = (currentRotation + 270) % 360;  // Same as camera device rotate_right

                                var serialWindows = mainWindow.findCameraWindowsBySerialNum(activeSerialNumber);
                                for (var l = 0; l < serialWindows.length; l++) {
                                    workspaceDatabase.set_camera_ipl_status(serialWindows[l].cameraspaceId, activeSerialNumber, "rotation", currentRotation);
                                    
                                    // Update rotation value (90 degrees)
                                    var canvas = serialWindows[l].drawingCanvas;
                                    rotateCanvasShapes(serialWindows[l].cameraspaceId, canvas, 90);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
