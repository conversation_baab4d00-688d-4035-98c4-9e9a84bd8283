import QtQuick 2.15
import QtQuick.Controls 2.15
import Qt.labs.platform 1.1

FileDialog {
    id: saveFileDialog
    title: "Save File"
    fileMode: FileDialog.SaveFile
    nameFilters: ["All Files (*)"]
    folder: StandardPaths.writableLocation(StandardPaths.DocumentsLocation)
    
    // Forward the accepted/rejected signals
    signal dialogAccepted(string filePath)
    signal dialogRejected()
    
    onAccepted: {
        dialogAccepted(file.toString());
    }
    
    onRejected: {
        dialogRejected();
    }
} 