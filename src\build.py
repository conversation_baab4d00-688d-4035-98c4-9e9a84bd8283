import os
import sys
import shutil
from pathlib import Path
import time
import psutil  # You may need to install this: pip install psutil

PROJECT_ROOT = Path(__file__).parent.absolute()

def kill_processes_using_file(file_path):
    """Kill processes that are using the specified file"""
    for proc in psutil.process_iter(['pid', 'name', 'open_files']):
        try:
            for file in proc.open_files():
                if str(file_path).lower() in str(file.path).lower():
                    print(f"Terminating process {proc.name()} (PID: {proc.pid})")
                    proc.terminate()
                    proc.wait(timeout=3)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            continue

def clean_with_retry(max_attempts=3, delay=2):
    """Clean build and dist directories with retry mechanism"""
    paths_to_clean = ['build', 'dist', '__pycache__']
    
    for path in paths_to_clean:
        if os.path.exists(path):
            for attempt in range(max_attempts):
                try:
                    print(f"Cleaning {path} (attempt {attempt + 1}/{max_attempts})")
                    if os.path.isfile(path):
                        os.remove(path)
                    else:
                        # Try to identify and kill processes using problematic files
                        problematic_files = [
                            'qwindows.dll',
                            'factoryeye.exe',
                            'factoryeye_test.exe'
                        ]
                        for root, _, files in os.walk(path):
                            for file in files:
                                if file.lower() in problematic_files:
                                    full_path = Path(root) / file
                                    kill_processes_using_file(full_path)
                        
                        # Wait a bit for processes to terminate
                        time.sleep(1)
                        
                        # Now try to remove the directory
                        shutil.rmtree(path, ignore_errors=True)
                    break
                except Exception as e:
                    if attempt == max_attempts - 1:
                        print(f"Failed to clean {path} after {max_attempts} attempts: {e}")
                        raise
                    print(f"Retrying in {delay} seconds...")
                    time.sleep(delay)

def build():
    """Build the application"""
    try:
        # Change to project root directory
        os.chdir(PROJECT_ROOT)
        
        # Clean previous builds with retry mechanism
        clean_with_retry()
        
        # Compile resources
        print("Compiling resources...")
        os.system("pyside6-rcc src/resources.qrc -o src/resources_rc.py")
        
        # Build executable using PyInstaller
        print("Building executable...")
        spec_file = "factoryeye.spec"
        
        # Use subprocess to run PyInstaller with proper Python environment
        cmd = f'"{sys.executable}" -m PyInstaller --clean {spec_file}'
        result = os.system(cmd)
        
        if result == 0:
            print("\nBuild completed successfully!")
            # Print the location of the executable
            exe_path = PROJECT_ROOT / "dist" / "FactoryEye"
            print(f"\nExecutable created at: {exe_path}")
            print("\nTry running the executable from the dist/FactoryEye directory")
        else:
            print("\nBuild failed!")
            sys.exit(1)

    except Exception as e:
        print(f"Error during build: {e}")
        sys.exit(1)

if __name__ == "__main__":
    build()

