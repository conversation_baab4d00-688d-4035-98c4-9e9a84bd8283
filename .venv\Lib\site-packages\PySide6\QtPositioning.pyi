# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtPositioning, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtPositioning`

import PySide6.QtPositioning
import PySide6.QtCore

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QGeoAddress(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoAddress, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoAddress, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoAddress, /) -> bool: ...
    def city(self, /) -> str: ...
    def clear(self, /) -> None: ...
    def country(self, /) -> str: ...
    def countryCode(self, /) -> str: ...
    def county(self, /) -> str: ...
    def district(self, /) -> str: ...
    def isEmpty(self, /) -> bool: ...
    def isTextGenerated(self, /) -> bool: ...
    def postalCode(self, /) -> str: ...
    def setCity(self, city: str, /) -> None: ...
    def setCountry(self, country: str, /) -> None: ...
    def setCountryCode(self, countryCode: str, /) -> None: ...
    def setCounty(self, county: str, /) -> None: ...
    def setDistrict(self, district: str, /) -> None: ...
    def setPostalCode(self, postalCode: str, /) -> None: ...
    def setState(self, state: str, /) -> None: ...
    def setStreet(self, street: str, /) -> None: ...
    def setStreetNumber(self, streetNumber: str, /) -> None: ...
    def setText(self, text: str, /) -> None: ...
    def state(self, /) -> str: ...
    def street(self, /) -> str: ...
    def streetNumber(self, /) -> str: ...
    def swap(self, other: PySide6.QtPositioning.QGeoAddress, /) -> None: ...
    def text(self, /) -> str: ...


class QGeoAreaMonitorInfo(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoAreaMonitorInfo, /) -> None: ...
    @typing.overload
    def __init__(self, /, name: str = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, ds: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, ds: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def area(self, /) -> PySide6.QtPositioning.QGeoShape: ...
    def expiration(self, /) -> PySide6.QtCore.QDateTime: ...
    def identifier(self, /) -> str: ...
    def isPersistent(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def notificationParameters(self, /) -> typing.Dict[str, typing.Any]: ...
    def setArea(self, newShape: PySide6.QtPositioning.QGeoShape, /) -> None: ...
    def setExpiration(self, expiry: PySide6.QtCore.QDateTime, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setNotificationParameters(self, parameters: typing.Dict[str, typing.Any], /) -> None: ...
    def setPersistent(self, isPersistent: bool, /) -> None: ...
    def swap(self, other: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, /) -> None: ...


class QGeoAreaMonitorSource(PySide6.QtCore.QObject):

    areaEntered              : typing.ClassVar[Signal] = ... # areaEntered(QGeoAreaMonitorInfo,QGeoPositionInfo)
    areaExited               : typing.ClassVar[Signal] = ... # areaExited(QGeoAreaMonitorInfo,QGeoPositionInfo)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoAreaMonitorSource::Error)
    monitorExpired           : typing.ClassVar[Signal] = ... # monitorExpired(QGeoAreaMonitorInfo)

    class AreaMonitorFeature(enum.Flag):

        AnyAreaMonitorFeature     = ...  # -1
        PersistentAreaMonitorFeature = ...  # 0x1

    class Error(enum.Enum):

        AccessError               = ...  # 0x0
        InsufficientPositionInfo  = ...  # 0x1
        UnknownSourceError        = ...  # 0x2
        NoError                   = ...  # 0x3


    def __init__(self, parent: PySide6.QtCore.QObject, /) -> None: ...

    @typing.overload
    def activeMonitors(self, /) -> typing.List[PySide6.QtPositioning.QGeoAreaMonitorInfo]: ...
    @typing.overload
    def activeMonitors(self, lookupArea: PySide6.QtPositioning.QGeoShape, /) -> typing.List[PySide6.QtPositioning.QGeoAreaMonitorInfo]: ...
    @staticmethod
    def availableSources() -> typing.List[str]: ...
    def backendProperty(self, name: str, /) -> typing.Any: ...
    @staticmethod
    def createDefaultSource(parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoAreaMonitorSource: ...
    @staticmethod
    def createSource(sourceName: str, parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoAreaMonitorSource: ...
    def error(self, /) -> PySide6.QtPositioning.QGeoAreaMonitorSource.Error: ...
    def positionInfoSource(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    def requestUpdate(self, monitor: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, signal: bytes | bytearray | memoryview, /) -> bool: ...
    def setBackendProperty(self, name: str, value: typing.Any, /) -> bool: ...
    def setPositionInfoSource(self, source: PySide6.QtPositioning.QGeoPositionInfoSource, /) -> None: ...
    def sourceName(self, /) -> str: ...
    def startMonitoring(self, monitor: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, /) -> bool: ...
    def stopMonitoring(self, monitor: PySide6.QtPositioning.QGeoAreaMonitorInfo | str, /) -> bool: ...
    def supportedAreaMonitorFeatures(self, /) -> PySide6.QtPositioning.QGeoAreaMonitorSource.AreaMonitorFeature: ...


class QGeoCircle(PySide6.QtPositioning.QGeoShape):

    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoCircle, /, *, center: PySide6.QtPositioning.QGeoCoordinate | None = ..., radius: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, center: PySide6.QtPositioning.QGeoCoordinate, /, radius: float = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoShape, /, *, center: PySide6.QtPositioning.QGeoCoordinate | None = ..., radius: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, center: PySide6.QtPositioning.QGeoCoordinate | None = ..., radius: float | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def center(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def extendCircle(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def radius(self, /) -> float: ...
    def setCenter(self, center: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setRadius(self, radius: float, /) -> None: ...
    def toString(self, /) -> str: ...
    def translate(self, degreesLatitude: float, degreesLongitude: float, /) -> None: ...
    def translated(self, degreesLatitude: float, degreesLongitude: float, /) -> PySide6.QtPositioning.QGeoCircle: ...


class QGeoCoordinate(Shiboken.Object):

    class CoordinateFormat(enum.Enum):

        Degrees                   = ...  # 0x0
        DegreesWithHemisphere     = ...  # 0x1
        DegreesMinutes            = ...  # 0x2
        DegreesMinutesWithHemisphere = ...  # 0x3
        DegreesMinutesSeconds     = ...  # 0x4
        DegreesMinutesSecondsWithHemisphere = ...  # 0x5

    class CoordinateType(enum.Enum):

        InvalidCoordinate         = ...  # 0x0
        Coordinate2D              = ...  # 0x1
        Coordinate3D              = ...  # 0x2


    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoCoordinate, /, *, latitude: float | None = ..., longitude: float | None = ..., altitude: float | None = ..., isValid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, latitude: float, longitude: float, altitude: float, /, *, isValid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, latitude: float, longitude: float, /, *, altitude: float | None = ..., isValid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, latitude: float | None = ..., longitude: float | None = ..., altitude: float | None = ..., isValid: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def altitude(self, /) -> float: ...
    def atDistanceAndAzimuth(self, distance: float, azimuth: float, /, distanceUp: float = ...) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def azimuthTo(self, other: PySide6.QtPositioning.QGeoCoordinate, /) -> float: ...
    def distanceTo(self, other: PySide6.QtPositioning.QGeoCoordinate, /) -> float: ...
    def isValid(self, /) -> bool: ...
    def latitude(self, /) -> float: ...
    def longitude(self, /) -> float: ...
    def setAltitude(self, altitude: float, /) -> None: ...
    def setLatitude(self, latitude: float, /) -> None: ...
    def setLongitude(self, longitude: float, /) -> None: ...
    def swap(self, other: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def toString(self, /, format: PySide6.QtPositioning.QGeoCoordinate.CoordinateFormat = ...) -> str: ...
    def type(self, /) -> PySide6.QtPositioning.QGeoCoordinate.CoordinateType: ...


class QGeoLocation(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoLocation, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoLocation, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoLocation, /) -> bool: ...
    def address(self, /) -> PySide6.QtPositioning.QGeoAddress: ...
    def boundingShape(self, /) -> PySide6.QtPositioning.QGeoShape: ...
    def coordinate(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def extendedAttributes(self, /) -> typing.Dict[str, typing.Any]: ...
    def isEmpty(self, /) -> bool: ...
    def setAddress(self, address: PySide6.QtPositioning.QGeoAddress, /) -> None: ...
    def setBoundingShape(self, shape: PySide6.QtPositioning.QGeoShape, /) -> None: ...
    def setCoordinate(self, position: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setExtendedAttributes(self, data: typing.Dict[str, typing.Any], /) -> None: ...
    def swap(self, other: PySide6.QtPositioning.QGeoLocation, /) -> None: ...


class QGeoPath(PySide6.QtPositioning.QGeoShape):

    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoPath, /, *, path: collections.abc.Sequence[typing.Any] | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoShape, /, *, path: collections.abc.Sequence[typing.Any] | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /, width: float = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, path: collections.abc.Sequence[typing.Any] | None = ..., width: float | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def addCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def clearPath(self, /) -> None: ...
    def containsCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    def coordinateAt(self, index: int, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def insertCoordinate(self, index: int, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def length(self, /, indexFrom: int | None = ..., indexTo: int = ...) -> float: ...
    def path(self, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...
    @typing.overload
    def removeCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    @typing.overload
    def removeCoordinate(self, index: int, /) -> None: ...
    def replaceCoordinate(self, index: int, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setPath(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def setVariantPath(self, path: collections.abc.Sequence[typing.Any], /) -> None: ...
    def setWidth(self, width: float, /) -> None: ...
    def size(self, /) -> int: ...
    def toString(self, /) -> str: ...
    def translate(self, degreesLatitude: float, degreesLongitude: float, /) -> None: ...
    def translated(self, degreesLatitude: float, degreesLongitude: float, /) -> PySide6.QtPositioning.QGeoPath: ...
    def variantPath(self, /) -> typing.List[typing.Any]: ...
    def width(self, /) -> float: ...


class QGeoPolygon(PySide6.QtPositioning.QGeoShape):

    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoPolygon, /, *, perimeter: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoShape, /, *, perimeter: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /, *, perimeter: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, perimeter: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def addCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    @typing.overload
    def addHole(self, holePath: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    @typing.overload
    def addHole(self, holePath: typing.Any, /) -> None: ...
    def containsCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    def coordinateAt(self, index: int, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def hole(self, index: int, /) -> typing.List[typing.Any]: ...
    def holePath(self, index: int, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...
    def holesCount(self, /) -> int: ...
    def insertCoordinate(self, index: int, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def length(self, /, indexFrom: int | None = ..., indexTo: int = ...) -> float: ...
    def perimeter(self, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...
    @typing.overload
    def removeCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    @typing.overload
    def removeCoordinate(self, index: int, /) -> None: ...
    def removeHole(self, index: int, /) -> None: ...
    def replaceCoordinate(self, index: int, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setPerimeter(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def size(self, /) -> int: ...
    def toString(self, /) -> str: ...
    def translate(self, degreesLatitude: float, degreesLongitude: float, /) -> None: ...
    def translated(self, degreesLatitude: float, degreesLongitude: float, /) -> PySide6.QtPositioning.QGeoPolygon: ...


class QGeoPositionInfo(Shiboken.Object):

    class Attribute(enum.Enum):

        Direction                 = ...  # 0x0
        GroundSpeed               = ...  # 0x1
        VerticalSpeed             = ...  # 0x2
        MagneticVariation         = ...  # 0x3
        HorizontalAccuracy        = ...  # 0x4
        VerticalAccuracy          = ...  # 0x5
        DirectionAccuracy         = ...  # 0x6


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, updateTime: PySide6.QtCore.QDateTime, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoPositionInfo, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoPositionInfo, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoPositionInfo, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def attribute(self, attribute: PySide6.QtPositioning.QGeoPositionInfo.Attribute, /) -> float: ...
    def coordinate(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def hasAttribute(self, attribute: PySide6.QtPositioning.QGeoPositionInfo.Attribute, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def removeAttribute(self, attribute: PySide6.QtPositioning.QGeoPositionInfo.Attribute, /) -> None: ...
    def setAttribute(self, attribute: PySide6.QtPositioning.QGeoPositionInfo.Attribute, value: float, /) -> None: ...
    def setCoordinate(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setTimestamp(self, timestamp: PySide6.QtCore.QDateTime, /) -> None: ...
    def swap(self, other: PySide6.QtPositioning.QGeoPositionInfo, /) -> None: ...
    def timestamp(self, /) -> PySide6.QtCore.QDateTime: ...


class QGeoPositionInfoSource(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoPositionInfoSource::Error)
    positionUpdated          : typing.ClassVar[Signal] = ... # positionUpdated(QGeoPositionInfo)
    supportedPositioningMethodsChanged: typing.ClassVar[Signal] = ... # supportedPositioningMethodsChanged()

    class Error(enum.Enum):

        AccessError               = ...  # 0x0
        ClosedError               = ...  # 0x1
        UnknownSourceError        = ...  # 0x2
        NoError                   = ...  # 0x3
        UpdateTimeoutError        = ...  # 0x4

    class PositioningMethod(enum.Flag):

        NonSatellitePositioningMethods = ...  # -256
        AllPositioningMethods     = ...  # -1
        NoPositioningMethods      = ...  # 0x0
        SatellitePositioningMethods = ...  # 0xff


    def __init__(self, parent: PySide6.QtCore.QObject, /, *, updateInterval: int | None = ..., minimumUpdateInterval: int | None = ..., sourceName: str | None = ..., preferredPositioningMethods: PySide6.QtPositioning.QGeoPositionInfoSource.PositioningMethod | None = ...) -> None: ...

    @staticmethod
    def availableSources() -> typing.List[str]: ...
    def backendProperty(self, name: str, /) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def createDefaultSource(parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    @typing.overload
    @staticmethod
    def createDefaultSource(parameters: typing.Dict[str, typing.Any], parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    @typing.overload
    @staticmethod
    def createSource(sourceName: str, parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    @typing.overload
    @staticmethod
    def createSource(sourceName: str, parameters: typing.Dict[str, typing.Any], parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    def error(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource.Error: ...
    def lastKnownPosition(self, /, fromSatellitePositioningMethodsOnly: bool = ...) -> PySide6.QtPositioning.QGeoPositionInfo: ...
    def minimumUpdateInterval(self, /) -> int: ...
    def preferredPositioningMethods(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource.PositioningMethod: ...
    def requestUpdate(self, /, timeout: int | None = ...) -> None: ...
    def setBackendProperty(self, name: str, value: typing.Any, /) -> bool: ...
    def setPreferredPositioningMethods(self, methods: PySide6.QtPositioning.QGeoPositionInfoSource.PositioningMethod, /) -> None: ...
    def setUpdateInterval(self, msec: int, /) -> None: ...
    def sourceName(self, /) -> str: ...
    def startUpdates(self, /) -> None: ...
    def stopUpdates(self, /) -> None: ...
    def supportedPositioningMethods(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource.PositioningMethod: ...
    def updateInterval(self, /) -> int: ...


class QGeoPositionInfoSourceFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def areaMonitor(self, parent: PySide6.QtCore.QObject, parameters: typing.Dict[str, typing.Any], /) -> PySide6.QtPositioning.QGeoAreaMonitorSource: ...
    def positionInfoSource(self, parent: PySide6.QtCore.QObject, parameters: typing.Dict[str, typing.Any], /) -> PySide6.QtPositioning.QGeoPositionInfoSource: ...
    def satelliteInfoSource(self, parent: PySide6.QtCore.QObject, parameters: typing.Dict[str, typing.Any], /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource: ...


class QGeoRectangle(PySide6.QtPositioning.QGeoShape):

    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoRectangle, /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., bottomRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., topLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, topLeft: PySide6.QtPositioning.QGeoCoordinate, bottomRight: PySide6.QtPositioning.QGeoCoordinate, /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, center: PySide6.QtPositioning.QGeoCoordinate, degreesWidth: float, degreesHeight: float, /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., bottomRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., topLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoShape, /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., bottomRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., topLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, coordinates: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., bottomRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., topLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, bottomLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., bottomRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., topLeft: PySide6.QtPositioning.QGeoCoordinate | None = ..., topRight: PySide6.QtPositioning.QGeoCoordinate | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ..., height: float | None = ..., width: float | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __ior__(self, rectangle: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __or__(self, rectangle: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def bottomLeft(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def bottomRight(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def center(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    @typing.overload
    def contains(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    @typing.overload
    def contains(self, rectangle: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> bool: ...
    def extendRectangle(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def height(self, /) -> float: ...
    def intersects(self, rectangle: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> bool: ...
    def setBottomLeft(self, bottomLeft: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setBottomRight(self, bottomRight: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setCenter(self, center: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setHeight(self, degreesHeight: float, /) -> None: ...
    def setTopLeft(self, topLeft: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setTopRight(self, topRight: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setWidth(self, degreesWidth: float, /) -> None: ...
    def toString(self, /) -> str: ...
    def topLeft(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def topRight(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def translate(self, degreesLatitude: float, degreesLongitude: float, /) -> None: ...
    def translated(self, degreesLatitude: float, degreesLongitude: float, /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def united(self, rectangle: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def width(self, /) -> float: ...


class QGeoSatelliteInfo(Shiboken.Object):

    class Attribute(enum.Enum):

        Elevation                 = ...  # 0x0
        Azimuth                   = ...  # 0x1

    class SatelliteSystem(enum.Enum):

        Undefined                 = ...  # 0x0
        GPS                       = ...  # 0x1
        GLONASS                   = ...  # 0x2
        GALILEO                   = ...  # 0x3
        BEIDOU                    = ...  # 0x4
        QZSS                      = ...  # 0x5
        Multiple                  = ...  # 0xff
        CustomType                = ...  # 0x100


    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoSatelliteInfo, /, *, satelliteSystem: PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem | None = ..., satelliteIdentifier: int | None = ..., signalStrength: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, satelliteSystem: PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem | None = ..., satelliteIdentifier: int | None = ..., signalStrength: float | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoSatelliteInfo, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoSatelliteInfo, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def attribute(self, attribute: PySide6.QtPositioning.QGeoSatelliteInfo.Attribute, /) -> float: ...
    def hasAttribute(self, attribute: PySide6.QtPositioning.QGeoSatelliteInfo.Attribute, /) -> bool: ...
    def removeAttribute(self, attribute: PySide6.QtPositioning.QGeoSatelliteInfo.Attribute, /) -> None: ...
    def satelliteIdentifier(self, /) -> int: ...
    def satelliteSystem(self, /) -> PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem: ...
    def setAttribute(self, attribute: PySide6.QtPositioning.QGeoSatelliteInfo.Attribute, value: float, /) -> None: ...
    def setSatelliteIdentifier(self, satId: int, /) -> None: ...
    def setSatelliteSystem(self, system: PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem, /) -> None: ...
    def setSignalStrength(self, signalStrength: int, /) -> None: ...
    def signalStrength(self, /) -> int: ...
    def swap(self, other: PySide6.QtPositioning.QGeoSatelliteInfo, /) -> None: ...


class QGeoSatelliteInfoSource(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoSatelliteInfoSource::Error)
    satellitesInUseUpdated   : typing.ClassVar[Signal] = ... # satellitesInUseUpdated(QList<QGeoSatelliteInfo>)
    satellitesInViewUpdated  : typing.ClassVar[Signal] = ... # satellitesInViewUpdated(QList<QGeoSatelliteInfo>)

    class Error(enum.Enum):

        UnknownSourceError        = ...  # -1
        AccessError               = ...  # 0x0
        ClosedError               = ...  # 0x1
        NoError                   = ...  # 0x2
        UpdateTimeoutError        = ...  # 0x3


    def __init__(self, parent: PySide6.QtCore.QObject, /, *, updateInterval: int | None = ..., minimumUpdateInterval: int | None = ...) -> None: ...

    @staticmethod
    def availableSources() -> typing.List[str]: ...
    def backendProperty(self, name: str, /) -> typing.Any: ...
    @typing.overload
    @staticmethod
    def createDefaultSource(parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource: ...
    @typing.overload
    @staticmethod
    def createDefaultSource(parameters: typing.Dict[str, typing.Any], parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource: ...
    @typing.overload
    @staticmethod
    def createSource(sourceName: str, parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource: ...
    @typing.overload
    @staticmethod
    def createSource(sourceName: str, parameters: typing.Dict[str, typing.Any], parent: PySide6.QtCore.QObject, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource: ...
    def error(self, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource.Error: ...
    def minimumUpdateInterval(self, /) -> int: ...
    def requestUpdate(self, /, timeout: int | None = ...) -> None: ...
    def setBackendProperty(self, name: str, value: typing.Any, /) -> bool: ...
    def setUpdateInterval(self, msec: int, /) -> None: ...
    def sourceName(self, /) -> str: ...
    def startUpdates(self, /) -> None: ...
    def stopUpdates(self, /) -> None: ...
    def updateInterval(self, /) -> int: ...


class QGeoShape(Shiboken.Object):

    class ShapeType(enum.Enum):

        UnknownType               = ...  # 0x0
        RectangleType             = ...  # 0x1
        CircleType                = ...  # 0x2
        PathType                  = ...  # 0x3
        PolygonType               = ...  # 0x4


    @typing.overload
    def __init__(self, other: PySide6.QtPositioning.QGeoShape, /, *, type: PySide6.QtPositioning.QGeoShape.ShapeType | None = ..., isValid: bool | None = ..., isEmpty: bool | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, type: PySide6.QtPositioning.QGeoShape.ShapeType | None = ..., isValid: bool | None = ..., isEmpty: bool | None = ..., center: PySide6.QtPositioning.QGeoCoordinate | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPositioning.QGeoShape, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, rhs: PySide6.QtPositioning.QGeoShape, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def boundingGeoRectangle(self, /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def center(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def contains(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def toString(self, /) -> str: ...
    def type(self, /) -> PySide6.QtPositioning.QGeoShape.ShapeType: ...


class QIntList: ...


class QNmeaPositionInfoSource(PySide6.QtPositioning.QGeoPositionInfoSource):

    class UpdateMode(enum.Enum):

        RealTimeMode              = ...  # 0x1
        SimulationMode            = ...  # 0x2


    def __init__(self, updateMode: PySide6.QtPositioning.QNmeaPositionInfoSource.UpdateMode, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def device(self, /) -> PySide6.QtCore.QIODevice: ...
    def error(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource.Error: ...
    def lastKnownPosition(self, /, fromSatellitePositioningMethodsOnly: bool = ...) -> PySide6.QtPositioning.QGeoPositionInfo: ...
    def minimumUpdateInterval(self, /) -> int: ...
    @typing.overload
    def parsePosInfoFromNmeaData(self, data: bytes | bytearray | memoryview, size: int, posInfo: PySide6.QtPositioning.QGeoPositionInfo, /) -> typing.Tuple[bool, bool]: ...
    @typing.overload
    def parsePosInfoFromNmeaData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, posInfo: PySide6.QtPositioning.QGeoPositionInfo, /) -> typing.Tuple[bool, bool]: ...
    def requestUpdate(self, /, timeout: int | None = ...) -> None: ...
    def setDevice(self, source: PySide6.QtCore.QIODevice, /) -> None: ...
    def setError(self, positionError: PySide6.QtPositioning.QGeoPositionInfoSource.Error, /) -> None: ...
    def setUpdateInterval(self, msec: int, /) -> None: ...
    def setUserEquivalentRangeError(self, uere: float, /) -> None: ...
    def startUpdates(self, /) -> None: ...
    def stopUpdates(self, /) -> None: ...
    def supportedPositioningMethods(self, /) -> PySide6.QtPositioning.QGeoPositionInfoSource.PositioningMethod: ...
    def updateMode(self, /) -> PySide6.QtPositioning.QNmeaPositionInfoSource.UpdateMode: ...
    def userEquivalentRangeError(self, /) -> float: ...


class QNmeaSatelliteInfoSource(PySide6.QtPositioning.QGeoSatelliteInfoSource):

    class SatelliteInfoParseStatus(enum.Enum):

        NotParsed                 = ...  # 0x0
        PartiallyParsed           = ...  # 0x1
        FullyParsed               = ...  # 0x2

    class UpdateMode(enum.Enum):

        RealTimeMode              = ...  # 0x1
        SimulationMode            = ...  # 0x2


    def __init__(self, mode: PySide6.QtPositioning.QNmeaSatelliteInfoSource.UpdateMode, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def backendProperty(self, name: str, /) -> typing.Any: ...
    def device(self, /) -> PySide6.QtCore.QIODevice: ...
    def error(self, /) -> PySide6.QtPositioning.QGeoSatelliteInfoSource.Error: ...
    def minimumUpdateInterval(self, /) -> int: ...
    @typing.overload
    def parseSatelliteInfoFromNmea(self, data: bytes | bytearray | memoryview, size: int, infos: collections.abc.Sequence[PySide6.QtPositioning.QGeoSatelliteInfo], system: PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem, /) -> PySide6.QtPositioning.QNmeaSatelliteInfoSource.SatelliteInfoParseStatus: ...
    @typing.overload
    def parseSatelliteInfoFromNmea(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, infos: collections.abc.Sequence[PySide6.QtPositioning.QGeoSatelliteInfo], system: PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem, /) -> PySide6.QtPositioning.QNmeaSatelliteInfoSource.SatelliteInfoParseStatus: ...
    @typing.overload
    def parseSatellitesInUseFromNmea(self, data: bytes | bytearray | memoryview, size: int, pnrsInUse: collections.abc.Sequence[int], /) -> PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem: ...
    @typing.overload
    def parseSatellitesInUseFromNmea(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, pnrsInUse: collections.abc.Sequence[int], /) -> PySide6.QtPositioning.QGeoSatelliteInfo.SatelliteSystem: ...
    def requestUpdate(self, /, timeout: int | None = ...) -> None: ...
    def setBackendProperty(self, name: str, value: typing.Any, /) -> bool: ...
    def setDevice(self, source: PySide6.QtCore.QIODevice, /) -> None: ...
    def setError(self, satelliteError: PySide6.QtPositioning.QGeoSatelliteInfoSource.Error, /) -> None: ...
    def setUpdateInterval(self, msec: int, /) -> None: ...
    def startUpdates(self, /) -> None: ...
    def stopUpdates(self, /) -> None: ...
    def updateMode(self, /) -> PySide6.QtPositioning.QNmeaSatelliteInfoSource.UpdateMode: ...


# eof
