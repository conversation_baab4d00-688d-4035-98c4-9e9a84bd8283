from setuptools import setup, find_packages
import os
from pathlib import Path

# Get the project root directory
PROJECT_ROOT = Path(__file__).parent.absolute()

# Define version (since version.py might not exist yet)
VERSION = "1.0.0"

# Read README.md if it exists, otherwise use a simple description
try:
    with open(PROJECT_ROOT / "README.md", "r", encoding="utf-8") as f:
        long_description = f.read()
except FileNotFoundError:
    long_description = "FactoryEye - Industrial camera management and analysis tool"

setup(
    name="factoryeye",
    version=VERSION,
    packages=find_packages(),
    install_requires=[
        "PySide6",
        "ids_peak",
    ],
    entry_points={
        'console_scripts': [
            'factoryeye=src.main:main',
        ],
    },
    author="Your Name",
    author_email="<EMAIL>",
    description="Industrial camera management and analysis tool",
    long_description=long_description,
    long_description_content_type="text/markdown",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
    include_package_data=True,
    package_data={
        "factoryeye": [
            "*.qml",
            "screens/*.qml",
            "components/*.qml",
            "constants/*.js",
            "resources/icons/*.png",
            "resources/*.png",
        ],
    },
)

