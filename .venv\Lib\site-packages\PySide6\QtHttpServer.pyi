# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtHttpServer, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtHttpServer`

import PySide6.QtHttpServer
import PySide6.QtCore
import PySide6.QtNetwork

import os
import enum
import typing
import collections.abc
from shiboken6 import Shiboken


class QAbstractHttpServer(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def bind(self, server: PySide6.QtNetwork.QLocalServer, /) -> bool: ...
    @typing.overload
    def bind(self, server: PySide6.QtNetwork.QTcpServer, /) -> bool: ...
    def http2Configuration(self, /) -> PySide6.QtNetwork.QHttp2Configuration: ...
    def localServers(self, /) -> typing.List[PySide6.QtNetwork.QLocalServer]: ...
    def serverPorts(self, /) -> typing.List[int]: ...
    def servers(self, /) -> typing.List[PySide6.QtNetwork.QTcpServer]: ...
    def setHttp2Configuration(self, configuration: PySide6.QtNetwork.QHttp2Configuration, /) -> None: ...


class QFutureHttpServerResponse(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QFutureHttpServerResponse: PySide6.QtHttpServer.QFutureHttpServerResponse, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def cancel(self, /) -> None: ...
    def isCanceled(self, /) -> bool: ...
    def isFinished(self, /) -> bool: ...
    def isPaused(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def isStarted(self, /) -> bool: ...
    def isSuspended(self, /) -> bool: ...
    def isSuspending(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def pause(self, /) -> None: ...
    def progressMaximum(self, /) -> int: ...
    def progressMinimum(self, /) -> int: ...
    def progressText(self, /) -> str: ...
    def progressValue(self, /) -> int: ...
    def resultCount(self, /) -> int: ...
    def resume(self, /) -> None: ...
    def setPaused(self, paused: bool, /) -> None: ...
    def setSuspended(self, suspend: bool, /) -> None: ...
    def suspend(self, /) -> None: ...
    def togglePaused(self, /) -> None: ...
    def toggleSuspended(self, /) -> None: ...
    def waitForFinished(self, /) -> None: ...


class QHttpServer(PySide6.QtHttpServer.QAbstractHttpServer):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addAfterRequestHandler(self, context: PySide6.QtCore.QObject, callback: collections.abc.Callable[..., typing.Any], /) -> None: ...
    def clearMissingHandler(self, /) -> None: ...
    def handleRequest(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder, /) -> bool: ...
    def missingHandler(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder, /) -> None: ...
    def route(self, rule: str, callback: collections.abc.Callable[..., typing.Any], /) -> bool: ...
    def router(self, /) -> PySide6.QtHttpServer.QHttpServerRouter: ...


class QHttpServerRequest(Shiboken.Object):

    class Method(enum.Flag):

        Unknown                   = ...  # 0x0
        Get                       = ...  # 0x1
        Put                       = ...  # 0x2
        Delete                    = ...  # 0x4
        Post                      = ...  # 0x8
        Head                      = ...  # 0x10
        Options                   = ...  # 0x20
        Patch                     = ...  # 0x40
        Connect                   = ...  # 0x80
        Trace                     = ...  # 0x100
        AnyKnown                  = ...  # 0x1ff


    def body(self, /) -> PySide6.QtCore.QByteArray: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    def localAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def localPort(self, /) -> int: ...
    def method(self, /) -> PySide6.QtHttpServer.QHttpServerRequest.Method: ...
    def query(self, /) -> PySide6.QtCore.QUrlQuery: ...
    def remoteAddress(self, /) -> PySide6.QtNetwork.QHostAddress: ...
    def remotePort(self, /) -> int: ...
    def sslConfiguration(self, /) -> PySide6.QtNetwork.QSslConfiguration: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...
    def value(self, key: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtCore.QByteArray: ...


class QHttpServerResponder(Shiboken.Object):

    class StatusCode(enum.Enum):

        Continue                  = ...  # 0x64
        SwitchingProtocols        = ...  # 0x65
        Processing                = ...  # 0x66
        Ok                        = ...  # 0xc8
        Created                   = ...  # 0xc9
        Accepted                  = ...  # 0xca
        NonAuthoritativeInformation = ...  # 0xcb
        NoContent                 = ...  # 0xcc
        ResetContent              = ...  # 0xcd
        PartialContent            = ...  # 0xce
        MultiStatus               = ...  # 0xcf
        AlreadyReported           = ...  # 0xd0
        IMUsed                    = ...  # 0xe2
        MultipleChoices           = ...  # 0x12c
        MovedPermanently          = ...  # 0x12d
        Found                     = ...  # 0x12e
        SeeOther                  = ...  # 0x12f
        NotModified               = ...  # 0x130
        UseProxy                  = ...  # 0x131
        TemporaryRedirect         = ...  # 0x133
        PermanentRedirect         = ...  # 0x134
        BadRequest                = ...  # 0x190
        Unauthorized              = ...  # 0x191
        PaymentRequired           = ...  # 0x192
        Forbidden                 = ...  # 0x193
        NotFound                  = ...  # 0x194
        MethodNotAllowed          = ...  # 0x195
        NotAcceptable             = ...  # 0x196
        ProxyAuthenticationRequired = ...  # 0x197
        RequestTimeout            = ...  # 0x198
        Conflict                  = ...  # 0x199
        Gone                      = ...  # 0x19a
        LengthRequired            = ...  # 0x19b
        PreconditionFailed        = ...  # 0x19c
        PayloadTooLarge           = ...  # 0x19d
        UriTooLong                = ...  # 0x19e
        UnsupportedMediaType      = ...  # 0x19f
        RequestRangeNotSatisfiable = ...  # 0x1a0
        ExpectationFailed         = ...  # 0x1a1
        ImATeapot                 = ...  # 0x1a2
        MisdirectedRequest        = ...  # 0x1a5
        UnprocessableEntity       = ...  # 0x1a6
        Locked                    = ...  # 0x1a7
        FailedDependency          = ...  # 0x1a8
        UpgradeRequired           = ...  # 0x1aa
        PreconditionRequired      = ...  # 0x1ac
        TooManyRequests           = ...  # 0x1ad
        RequestHeaderFieldsTooLarge = ...  # 0x1af
        UnavailableForLegalReasons = ...  # 0x1c3
        InternalServerError       = ...  # 0x1f4
        NotImplemented            = ...  # 0x1f5
        BadGateway                = ...  # 0x1f6
        ServiceUnavailable        = ...  # 0x1f7
        GatewayTimeout            = ...  # 0x1f8
        HttpVersionNotSupported   = ...  # 0x1f9
        VariantAlsoNegotiates     = ...  # 0x1fa
        InsufficientStorage       = ...  # 0x1fb
        LoopDetected              = ...  # 0x1fc
        NotExtended               = ...  # 0x1fe
        NetworkAuthenticationRequired = ...  # 0x1ff
        NetworkConnectTimeoutError = ...  # 0x257


    def sendResponse(self, response: PySide6.QtHttpServer.QHttpServerResponse, /) -> None: ...
    def swap(self, other: PySide6.QtHttpServer.QHttpServerResponder, /) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QIODevice, headers: PySide6.QtNetwork.QHttpHeaders, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QIODevice, mimeType: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, headers: PySide6.QtNetwork.QHttpHeaders, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, document: PySide6.QtCore.QJsonDocument, headers: PySide6.QtNetwork.QHttpHeaders, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, document: PySide6.QtCore.QJsonDocument, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, headers: PySide6.QtNetwork.QHttpHeaders, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def write(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, mimeType: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def writeBeginChunked(self, headers: PySide6.QtNetwork.QHttpHeaders, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def writeBeginChunked(self, headers: PySide6.QtNetwork.QHttpHeaders, trailerNames: collections.abc.Sequence[PySide6.QtNetwork.QHttpHeaders.WellKnownHeader], /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def writeBeginChunked(self, mimeType: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    def writeChunk(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def writeEndChunked(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def writeEndChunked(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, trailers: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...


class QHttpServerResponse(Shiboken.Object):

    @typing.overload
    def __init__(self, data: PySide6.QtCore.QJsonArray, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def __init__(self, statusCode: PySide6.QtHttpServer.QHttpServerResponder.StatusCode, /) -> None: ...
    @typing.overload
    def __init__(self, data: str, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Dict[str, PySide6.QtCore.QJsonValue], /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def __init__(self, data: bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def __init__(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...
    @typing.overload
    def __init__(self, mimeType: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, status: PySide6.QtHttpServer.QHttpServerResponder.StatusCode = ...) -> None: ...

    def data(self, /) -> PySide6.QtCore.QByteArray: ...
    @staticmethod
    def fromFile(fileName: str, /) -> PySide6.QtHttpServer.QHttpServerResponse: ...
    def headers(self, /) -> PySide6.QtNetwork.QHttpHeaders: ...
    def mimeType(self, /) -> PySide6.QtCore.QByteArray: ...
    def setHeaders(self, newHeaders: PySide6.QtNetwork.QHttpHeaders, /) -> None: ...
    def statusCode(self, /) -> PySide6.QtHttpServer.QHttpServerResponder.StatusCode: ...
    def swap(self, other: PySide6.QtHttpServer.QHttpServerResponse, /) -> None: ...


class QHttpServerRouter(Shiboken.Object):

    def __init__(self, server: PySide6.QtHttpServer.QAbstractHttpServer, /) -> None: ...

    def addConverter(self, metaType: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, regexp: str, /) -> None: ...
    def clearConverters(self, /) -> None: ...
    def converters(self, /) -> typing.Dict[PySide6.QtCore.QMetaType, str]: ...
    def handleRequest(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder, /) -> bool: ...
    def removeConverter(self, metaType: PySide6.QtCore.QMetaType | PySide6.QtCore.QMetaType.Type, /) -> None: ...


class QHttpServerRouterRule(Shiboken.Object):
    def contextObject(self, /) -> PySide6.QtCore.QObject: ...
    def exec(self, request: PySide6.QtHttpServer.QHttpServerRequest, responder: PySide6.QtHttpServer.QHttpServerResponder, /) -> bool: ...
    def hasValidMethods(self, /) -> bool: ...
    def matches(self, request: PySide6.QtHttpServer.QHttpServerRequest, match: PySide6.QtCore.QRegularExpressionMatch, /) -> bool: ...


class QHttpServerWebSocketUpgradeResponse(Shiboken.Object):

    class ResponseType(enum.Enum):

        Accept                    = ...  # 0x0
        Deny                      = ...  # 0x1
        PassToNext                = ...  # 0x2


    def __init__(self, other: PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse, /) -> None: ...

    @staticmethod
    def accept() -> PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse: ...
    @typing.overload
    @staticmethod
    def deny() -> PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse: ...
    @typing.overload
    @staticmethod
    def deny(status: int, message: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse: ...
    def denyMessage(self, /) -> PySide6.QtCore.QByteArray: ...
    def denyStatus(self, /) -> int: ...
    @staticmethod
    def passToNext() -> PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse: ...
    def swap(self, other: PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse, /) -> None: ...
    def type(self, /) -> PySide6.QtHttpServer.QHttpServerWebSocketUpgradeResponse.ResponseType: ...


class QIntList: ...


# eof
