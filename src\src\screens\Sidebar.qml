import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"

Rectangle {
    width: 50
    Layout.fillHeight: true
    color: "lightgray"
    property bool brightnessVisible: false
    property bool iplVisible: false

    signal iPLClicked
    signal brightnessClicked
    
    Connections {
        target: brightnessWindow
        function onVisibleChanged() {
            brightnessVisible = brightnessWindow.visible
        }
    }

    Connections {
        target: iplWindow
        function onVisibleChanged() {
            iplVisible = iplWindow.visible
        }
    }

    Column {
        spacing: 10
        anchors.horizontalCenter: parent.horizontalCenter

        Rectangle {
            width: 1
            height: 1
            color: 'transparent'
        }

        CheckableButton {
            checked: brightnessVisible
            icon.source: "qrc:/resources/icons/brightness.png"
            onClicked: {
                brightnessClicked()
            }
        }
        CheckableButton {
            checked: iplVisible
            icon.source: "qrc:/resources/icons/IPL.png"
            onClicked: {
                iPLClicked()
            }
        }
    }
}
