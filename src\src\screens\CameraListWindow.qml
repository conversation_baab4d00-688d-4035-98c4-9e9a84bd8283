import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../components"
import "../constants/WindowsConstants.js" as WindowsConstants

Rectangle {
    width: WindowsConstants.subWindowWidth
    Layout.fillHeight: true
    color: "lightgray"

    signal cameraListClosed
    signal cameraPlayRequested(string modelName, string serialNumber)
    signal cameraStopRequested(string serialNumber)

    Column {
        anchors.fill: parent

        Titlebar {
            title: "Camera Manager"
            titleIcon: "qrc:/resources/icons/list.png"

            onSubWindowClosed: {
                cameraListClosed();
            }
        }

        Rectangle {
            width: parent.width
            height: WindowsConstants.topBarHeight
            color: "darkgray"

            RowLayout {
                height: parent.height
                spacing: 5
                anchors.horizontalCenter: parent.horizontalCenter

                ToolButton {
                    id: cameraPlayBtn
                    icon.source: "qrc:/resources/icons/camera.png" // Path to your camera icon
                    enabled: cameraListView.currentIndex >= 0 && !cameraListModel.is_camera_started(myListModel.get(cameraListView.currentIndex).serialNumber)
                    onClicked: {
                        if (cameraListView.currentIndex >= 0) {
                            var modelName = myListModel.get(cameraListView.currentIndex).modelName;
                            var serialNumber = myListModel.get(cameraListView.currentIndex).serialNumber;
                            cameraPlayRequested(modelName, serialNumber); // Emit the signal
                        }
                    }
                }
                ToolButton {
                    id: cameraStopBtn
                    icon.source: "qrc:/resources/icons/camera_stop.png" // Path to your camera_stop icon
                    enabled: cameraListView.currentIndex >= 0 && cameraListModel.is_camera_started(myListModel.get(cameraListView.currentIndex).serialNumber)
                    onClicked: {
                        if (cameraListView.currentIndex >= 0) {
                            var serialNumber = myListModel.get(cameraListView.currentIndex).serialNumber;
                            cameraStopRequested(serialNumber);
                        }
                    }
                }
                ToolButton {
                    icon.source: "qrc:/resources/icons/refresh.png"
                    onClicked: {
                        cameraListModel.update_camera_list();
                    }
                }
            }
        }

        // Rectangle to set background color and padding for ListView
        Rectangle {
            width: parent.width
            height: parent.height - 60
            color: "lightgray"

            ListView {
                id: cameraListView
                width: parent.width
                height: parent.height
                model: myListModel

                delegate: MouseArea {
                    // Wrap the entire delegate with MouseArea
                    width: cameraListView.width
                    height: 40
                    
                    // Add double click handling
                    onDoubleClicked: {
                        // Check if camera is not already started
                        if (!cameraListModel.is_camera_started(model.serialNumber)) {
                            cameraPlayRequested(model.modelName, model.serialNumber);
                        }
                    }
                    
                    onClicked: {
                        cameraListView.currentIndex = index;
                        cameraPlayBtn.enabled = !cameraListModel.is_camera_started(myListModel.get(index).serialNumber);
                        cameraStopBtn.enabled = cameraListModel.is_camera_started(myListModel.get(index).serialNumber);

                        console.log("Item clicked: " + index + " " + model.modelName + ", " + model.serialNumber);
                    }

                    Rectangle {
                        width: parent.width
                        height: parent.height
                        color: cameraListView.currentIndex === index ? "#2c2c2c" : "lightgray" // Set selected color

                        RowLayout {
                            spacing: 5
                            Layout.fillHeight: true
                            Layout.fillWidth: true

                            Rectangle {
                                width: 1
                                height: parent.parent.height
                                color: "transparent"
                            }

                            ToolButton {
                                icon.source: cameraListView.currentIndex === index ? "qrc:/resources/icons/usb_w.png" : "qrc:/resources/icons/usb_b.png"
                                enabled: false
                            }
                            Text {
                                text: model.modelName
                                Layout.preferredWidth: parent.parent.width * 0.45
                                elide: Text.ElideRight
                                clip: true
                                color: cameraListView.currentIndex === index ? "white" : "black"
                            }
                            Text {
                                text: model.serialNumber
                                Layout.preferredWidth: parent.parent.width * 0.45
                                elide: Text.ElideRight
                                clip: true
                                color: cameraListView.currentIndex === index ? "white" : "black"
                            }
                        }
                    }
                }
            }
        }
    }

    // ListModel {
    //     id: myListModel
    // }

    // Component.onCompleted: {
    //     updateListModel();
    // }

    // function updateListModel() {
    //     myListModel.clear();
    //     var cameraList = cameraListModel.getCameraList();
    //     for (var i = 0; i < cameraList.length; i++) {
    //         myListModel.append(cameraList[i]);
    //     }
    // }

    Connections {
        target: cameraListModel
        function onCameraStateChanged() {
            cameraPlayBtn.enabled = !cameraListModel.is_camera_started(myListModel.get(cameraListView.currentIndex).serialNumber);
            cameraStopBtn.enabled = cameraListModel.is_camera_started(myListModel.get(cameraListView.currentIndex).serialNumber);
        }
    }
   
    ListModel {
        id: myListModel
        ListElement {
            modelName: "U3-388xCP-M"
            serialNumber: "4104488017"
        }
        ListElement {
            modelName: "U3-36LxXC-C"
            serialNumber: "4104284450"
        }
        ListElement {
            modelName: "U3-35FxSD-C"
            serialNumber: "4104235632"
        }
    }
}
