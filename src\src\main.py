import sys
import os
from pathlib import Path
from PySide6.QtGui import <PERSON><PERSON>uiApplication, QIcon
from PySide6.QtQml import QQmlApplicationEngine
from version import VERSION

import resources_rc  # Import the compiled resource module
from camera_manager import CameraListModel
from database_manager import WorkspaceDatabase

if __name__ == "__main__":
    app = QGuiApplication(sys.argv)
    app.setApplicationName("FactoryEye")
    app.setApplicationVersion(VERSION)
    
    # Fix the icon loading with explicit size setting
    if getattr(sys, 'frozen', False):
        # If running as bundled exe
        icon_path = str(Path(sys._MEIPASS) / "resources" / "icons" / "logo.ico")
    else:
        # If running from Python interpreter
        icon_path = str(Path(__file__).resolve().parent.parent / "resources" / "icons" / "logo.ico")
    
    # Create icon and set multiple sizes
    icon = QIcon(icon_path)
    app.setWindowIcon(icon)
    
    # Force Windows to reload icon cache (only needed during development)
    if os.name == 'nt':  # Windows
        import ctypes
        ctypes.windll.shell32.SHChangeNotify(0x08000000, 0x0000, None, None)

    engine = QQmlApplicationEngine()
    # Expose version to QML
    engine.rootContext().setContextProperty("appVersion", VERSION)
    
    # Get the absolute path to the QML file
    if getattr(sys, 'frozen', False):
        # If the application is run as a bundle
        application_path = sys._MEIPASS
        qml_file = str(Path(application_path) / "main.qml")
    else:
        # If the application is run from a Python interpreter
        qml_file = str(Path(__file__).resolve().parent / "main.qml")

    # Set the global resource path
    if getattr(sys, 'frozen', False):
        resource_path = Path(application_path).parent / "resources"
    else:
        resource_path = Path(__file__).resolve().parent.parent / "resources"
    
    engine.rootContext().setContextProperty("resourcePath", str(resource_path.as_posix()))

    # Create the camera list model
    camera_list_model = CameraListModel(engine)
    
    # Expose the camera list model to QML
    engine.rootContext().setContextProperty("cameraListModel", camera_list_model)

    # Create the workspace database with appropriate path
    if getattr(sys, 'frozen', False):
        db_name = os.path.join(os.getenv('APPDATA'), 'FactoryEye', 'data', 'factoryeye.db')
    else:
        db_name = "factoryeye.db"
        
    workspace_database = WorkspaceDatabase(db_name)
    # Expose the workspace database to QML
    engine.rootContext().setContextProperty("workspaceDatabase", workspace_database)

    engine.load(qml_file)
    if not engine.rootObjects():
        sys.exit(-1)
    sys.exit(app.exec())
