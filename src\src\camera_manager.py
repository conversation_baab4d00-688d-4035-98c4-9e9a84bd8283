from PySide6.QtCore import QAbstractListModel, Qt, Slot, Signal, QObject, QThread, QTimer
from PySide6.QtGui import QImage
from PySide6.QtQuick import QQuickImageProvider
from ids_peak import ids_peak
from ids_peak_ipl import ids_peak_ipl
from ids_peak import ids_peak_ipl_extension
import os
import time
from os.path import exists
from dataclasses import dataclass
from database_manager import WorkspaceDatabase
import logging
import datetime
import sys

# Configure logging
try:
    # Use AppData for logs in production, local directory in development
    if getattr(sys, 'frozen', False):
        # If running as bundled exe
        log_directory = os.path.join(os.getenv('APPDATA'), 'FactoryEye', 'logs')
    else:
        # If running from Python interpreter
        log_directory = "logs"

    if not os.path.exists(log_directory):
        os.makedirs(log_directory, exist_ok=True)

    current_date = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = os.path.join(log_directory, f"camera_manager_{current_date}.log")

    # Configure the logger
    logging.basicConfig(
        filename=log_file,
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Create a logger
    logger = logging.getLogger("CameraManager")

    # Create console handler to also show logs in console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

except Exception as e:
    # Fallback to console-only logging if file logging fails
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("CameraManager")
    logger.error(f"Failed to setup file logging: {e}")

TARGET_PIXEL_FORMAT = ids_peak_ipl.PixelFormatName_BGRa8

class LiveImageProvider(QQuickImageProvider):
    def __init__(self):
        super().__init__(QQuickImageProvider.Image)
        self._current_image = QImage()

    def requestImage(self, id_str, requested_size, size):
        # id_str: The ID that was requested (string after "image://live/")
        # requested_size: The size that was requested by the QML engine
        # size: The size to be filled with the actual image size
        if not self._current_image.isNull():
            size.setWidth(self._current_image.width())
            size.setHeight(self._current_image.height())
            return self._current_image.copy()
        return QImage()

    def updateImage(self, image):
        self._current_image = image.copy()

@dataclass
class RecordingStatistics:
    frames_encoded: int
    frames_stream_dropped: int
    frames_video_dropped: int
    frames_lost_stream: int
    duration: int

    def fps(self):
        return self.frames_encoded / self.duration

class CameraAcquisitionThread(QThread):
    frameReady = Signal(QImage)
    warning = Signal(str)
    recordingFinished = Signal(RecordingStatistics)

    def __init__(self, camera_controller):
        super().__init__()
        self.camera_controller = camera_controller
        self.is_recording = False
        self.killed = False

    def run(self):
        while not self.killed:
            try:
                if self.is_recording:
                    self.camera_controller.record(10)  # Record for 10 seconds
                    self.is_recording = False
                else:
                    converted_ipl_image = self.camera_controller.get_data_stream_image()
                    try:
                        # Convert image to a NumPy array
                        np_image = converted_ipl_image.get_numpy()  # Returns a NumPy array

                        # Extract width and height
                        width = converted_ipl_image.Width()
                        height = converted_ipl_image.Height()

                        # Determine bytes per line (stride)
                        bytes_per_line = np_image.strides[0]  # First stride value in NumPy array

                        # Convert NumPy array to raw bytes
                        image_data = np_image.tobytes()

                        # Create QImage (assuming RGB format)
                        qimage = QImage(image_data, width, height, bytes_per_line, QImage.Format_RGB32)

                        # Convert BGR to RGB if needed
                        # qimage = qimage.rgbSwapped()
                        
                        # Update the image provider and emit signal
                        self.camera_controller.handle_frame(qimage)
                        
                    except Exception as e:
                        logger.error("CameraAcquisitionThread:image: image error", str(e))

            except Exception as e:
                self.warning.emit(str(e))
                self.is_recording = False
                self.recordingFinished.emit(RecordingStatistics(0, 0, 0, 0, 0))

    def stop(self):
        self.killed = True
        self.wait()

class CameraController(QObject):
    cameraFrameReady = Signal()
    warning = Signal(str)
    recordingFinished = Signal(RecordingStatistics)
    frame_rate_updated = Signal(dict)

    def __init__(self, serial_number):
        super().__init__()
        self.device = None
        self.dataStream = None
        self.nodemap = None
        self._acquisition_running = False
        self._image_converter = None
        self.target_fps = 20000
        self.max_fps = 0
        self.target_gain = 1
        self.max_gain = 1
        self.acquisition_thread = None
        self.serial_number = serial_number
        self.image_provider = LiveImageProvider()
        
        # Add image transformation properties with default values
        self.flip_horizontal = False
        self.flip_vertical = False
        self.rotation = 0  # 0, 90, 180, 270

        # Print IDS Peak IPL information
        # self.print_ids_peak_ipl_info()

    def get_image_provider(self):
        return self.image_provider

    def handle_frame(self, frame):
        self.image_provider.updateImage(frame)
        self.cameraFrameReady.emit()

    def start_camera(self, serial_number, cameraSettings):
        try:
            # Initialize the library if not already initialized
            ids_peak.Library.Initialize()
            
            # Get device manager
            device_manager = ids_peak.DeviceManager.Instance()
            device_manager.Update()
            
            # Find device with matching serial number
            for device in device_manager.Devices():
                if device.SerialNumber() == serial_number:
                    logger.info(f"Starting camera: {serial_number}")
                    self.device = device.OpenDevice(ids_peak.DeviceAccessType_Control)
                    break
            
            if self.device:
                # Get nodemap for camera settings
                self.nodemap = self.device.RemoteDevice().NodeMaps()[0]
                
                # Print available parameters for debugging
                logger.info("Available camera parameters:")
                # self.print_available_parameters()
                
                # Load default settings
                self.nodemap.FindNode("UserSetSelector").SetCurrentEntry("Default")
                self.nodemap.FindNode("UserSetLoad").Execute()
                self.nodemap.FindNode("UserSetLoad").WaitUntilDone()

                # Get max gain
                self.max_gain = self.nodemap.FindNode("Gain").Maximum()

                # Only set gain if it exists in cameraSettings
                if "analog_gain" in cameraSettings:
                    self.set_remote_device_value("Gain", cameraSettings["analog_gain"])
                
                # Only set exposure time if it exists in cameraSettings
                if "exposure_time" in cameraSettings:
                    self.update_exposure_time(cameraSettings["exposure_time"])
                
                # Setup device and datastream
                self._setup_device_and_datastream()
                
                # Setup image converter
                self._image_converter = ids_peak_ipl.ImageConverter()
                
                # Start acquisition
                if self.start_acquisition(cameraSettings):
                    # Create and start acquisition thread
                    self.acquisition_thread = CameraAcquisitionThread(self)
                    self.acquisition_thread.frameReady.connect(self.cameraFrameReady)
                    self.acquisition_thread.warning.connect(self.warning)
                    self.acquisition_thread.recordingFinished.connect(self.recordingFinished)
                    self.acquisition_thread.start()
                
        except Exception as e:
            logger.error(f"Error starting camera: {e}")

    def _setup_device_and_datastream(self):
        self.dataStream = self.device.DataStreams()[0].OpenDataStream()
        
        # Allocate image buffer for image acquisition
        payload_size = self.nodemap.FindNode("PayloadSize").Value()
        # Use more buffers
        max_buffer = self.dataStream.NumBuffersAnnouncedMinRequired() * 5
        for _ in range(max_buffer):
            buffer = self.dataStream.AllocAndAnnounceBuffer(payload_size)
            self.dataStream.QueueBuffer(buffer)
        logger.info("Allocated buffers, finished opening device")

    def _find_and_set_remote_device_enumeration(self, name: str, value: str):
        all_entries = self.nodemap.FindNode(name).Entries()
        available_entries = []
        for entry in all_entries:
            if (entry.AccessStatus() != ids_peak.NodeAccessStatus_NotAvailable
                    and entry.AccessStatus() != ids_peak.NodeAccessStatus_NotImplemented):
                available_entries.append(entry.SymbolicValue())
        if value in available_entries:
            self.nodemap.FindNode(name).SetCurrentEntry(value)

    def print_available_parameters(self):
        """Print all available parameters from the camera nodemap"""
        if self.nodemap:
            try:
                for node in self.nodemap.Nodes():
                    try:
                        node_type = node.__class__.__name__  # Get the class name instead of using NodeType()
                        node_name = node.Name()
                        logger.debug(f"Parameter: {node_name}, Type: {node_type}")
                        
                        # For special nodes, try to print additional information
                        if hasattr(node, "Value") and callable(node.Value):
                            try:
                                value = node.Value()
                                logger.debug(f"  - Value: {value}")
                            except:
                                pass
                                
                        if hasattr(node, "Minimum") and callable(node.Minimum):
                            try:
                                min_val = node.Minimum()
                                logger.debug(f"  - Minimum: {min_val}")
                            except:
                                pass
                                
                        if hasattr(node, "Maximum") and callable(node.Maximum):
                            try:
                                max_val = node.Maximum()
                                logger.debug(f"  - Maximum: {max_val}")
                            except:
                                pass
                    except Exception as node_error:
                        logger.error(f"Error getting info for node: {node_error}")
            except Exception as e:
                logger.error(f"Error printing parameters: {e}")
        else:
            logger.warning("Nodemap not available. Camera not initialized.")

    def update_frame_rate(self, frame_rate):
        # Update the frame rate using the set_remote_device_value method
        logger.info(f"Updating frame rate to {frame_rate}")
        self.set_remote_device_value("AcquisitionFrameRate", frame_rate)

    def update_gain(self, gain):
        # Update the gain using the set_remote_device_value method
        logger.info(f"Updating gain to {gain}")
        self.set_remote_device_value("Gain", gain)
        self.target_gain = gain  # Update the target_gain property

    def update_exposure_time(self, exposure_time):
        # Convert exposure time from milliseconds to microseconds if needed
        # Most cameras use exposure time in microseconds, so multiply by 1000
        exposure_time_us = exposure_time * 1000
        
        logger.info(f"Updating exposure time to {exposure_time} ms ({exposure_time_us} µs)")
        
        try:
            # Try to find the exposure time parameter
            exposure_node = None
            
            # Common parameter names for exposure time
            exposure_param_names = ["ExposureTime", "ExposureTimeAbs", "ExposureValue"]
            
            for name in exposure_param_names:
                if self.has_node(name):
                    exposure_node = self.nodemap.FindNode(name)
                    logger.info(f"Found exposure parameter: {name}")
                    break
            
            if exposure_node:
                # Check if we need to convert units based on the camera's expected range
                if hasattr(exposure_node, "Minimum") and callable(exposure_node.Minimum) and \
                   hasattr(exposure_node, "Maximum") and callable(exposure_node.Maximum):
                    try:
                        min_val = exposure_node.Minimum()
                        max_val = exposure_node.Maximum()
                        logger.info(f"Exposure range: {min_val} to {max_val}")
                        
                        # Determine if we need to use microseconds or milliseconds
                        # If max value is large (e.g., > 1000), likely in microseconds
                        if max_val > 1000:
                            # Use microseconds
                            self.set_remote_device_value(exposure_node.Name(), exposure_time_us)
                        else:
                            # Use milliseconds directly
                            self.set_remote_device_value(exposure_node.Name(), exposure_time)

                        # After setting exposure time, get frame rate values
                        maxFrameRate = self.nodemap.FindNode("AcquisitionFrameRate").Maximum()
                        minFrameRate = self.nodemap.FindNode("AcquisitionFrameRate").Minimum()
                        valueFrameRate = self.nodemap.FindNode("AcquisitionFrameRate").Value()

                        # Emit signal with frame rate information matching the slot format
                        frameRateInfo = {
                            "min": minFrameRate,
                            "max": maxFrameRate,
                            "value": valueFrameRate
                        }
                        self.frame_rate_updated.emit(frameRateInfo)
                        
                        logger.info(f"Frame rate values - min: {minFrameRate}, max: {maxFrameRate}, current: {valueFrameRate}")

                    except Exception as e:
                        logger.error(f"Error determining exposure range: {e}")
                        # Default to microseconds
                        self.set_remote_device_value(exposure_node.Name(), exposure_time_us)
                else:
                    # Default to microseconds
                    self.set_remote_device_value(exposure_node.Name(), exposure_time_us)
                
                # Print the new value
                try:
                    new_value = exposure_node.Value()
                    logger.info(f"New exposure time: {new_value}")
                except:
                    pass
            else:
                logger.warning("No exposure time parameter found. Available parameters:")
                
        except Exception as e:
            logger.error(f"Error updating exposure time: {e}")

    def update_analog_gain(self, gain):
        # Update the analog gain - check if your camera has a specific parameter for this
        logger.info(f"Updating analog gain to {gain}")
        # Common parameter names for analog gain might be:
        # "AnalogGain", "GainAnalog", etc.
        try:
            # Try a few common names for analog gain
            if self.has_node("AnalogGain"):
                self.set_remote_device_value("AnalogGain", gain)
            elif self.has_node("GainAnalog"):
                self.set_remote_device_value("GainAnalog", gain)
            elif self.has_node("Gain"):
                # If only generic gain is available, use that
                self.set_remote_device_value("Gain", gain)
            else:
                logger.info("No analog gain parameter found")
        except Exception as e:
            logger.error(f"Error updating analog gain: {e}")

    def update_digital_gain(self, gain):
        # Update the digital gain - check if your camera has a specific parameter for this
        logger.info(f"Updating digital gain to {gain}")
        # Common parameter names for digital gain might be:
        # "DigitalGain", "GainDigital", "Brightness", etc.
        try:
            # Try a few common names for digital gain
            if self.has_node("DigitalGain"):
                self.set_remote_device_value("DigitalGain", gain)
            elif self.has_node("GainDigital"):
                self.set_remote_device_value("GainDigital", gain)
            elif self.has_node("Brightness"):
                self.set_remote_device_value("Brightness", gain)
            else:
                logger.info("No digital gain parameter found")
        except Exception as e:
            logger.error(f"Error updating digital gain: {e}")

    def has_node(self, name):
        """Check if the nodemap has a node with the given name"""
        try:
            self.nodemap.FindNode(name)
            return True
        except:
            return False

    def set_remote_device_value(self, name: str, value: any):
        try:
            self.nodemap.FindNode(name).SetValue(value)
        except ids_peak.Exception:
            self.warning.emit(f"Could not set value for {name}!")

    def start_acquisition(self, cameraSettings: dict):
        if self.device is None:
            return False
        if self._acquisition_running is True:
            return True

        try:
            # Get cameras maximums possible FPS
            self.max_fps = self.nodemap.FindNode("AcquisitionFrameRate").Maximum()

            # Set frames per second to given maximum
            self.target_fps = self.nodemap.FindNode("AcquisitionFrameRate").Value()
            self.set_remote_device_value("AcquisitionFrameRate", cameraSettings["frame_rate"] if "frame_rate" in cameraSettings else self.target_fps)
        except ids_peak.Exception:
            self.warning.emit(
                "Warning: Unable to limit fps, since node AcquisitionFrameRate is not supported. "
                "Program will continue without set limit.")

        try:
            # Lock parameters
            self.nodemap.FindNode("TLParamsLocked").SetValue(1)

            # Pre-allocate conversion buffers
            image_width = self.nodemap.FindNode("Width").Value()
            image_height = self.nodemap.FindNode("Height").Value()
            input_pixel_format = ids_peak_ipl.PixelFormat(
                self.nodemap.FindNode("PixelFormat").CurrentEntry().Value())

            self._image_converter = ids_peak_ipl.ImageConverter()
            self._image_converter.PreAllocateConversion(
                input_pixel_format, TARGET_PIXEL_FORMAT,
                image_width, image_height)

            # Start acquisition
            self.dataStream.StartAcquisition()
            self.nodemap.FindNode("AcquisitionStart").Execute()
            self.nodemap.FindNode("AcquisitionStart").WaitUntilDone()
            
            self._acquisition_running = True
            
        except Exception as e:
            logger.error(f"Error starting acquisition: {e}")
            return False

        return True

    def stop_camera(self):
        try:
            if self.acquisition_thread:
                self.acquisition_thread.stop()
                self.acquisition_thread = None

            if self.device:
                self.nodemap.FindNode("AcquisitionStop").Execute()
                
                if self.dataStream:
                    # Kill pending operations
                    self.dataStream.KillWait()
                    self.dataStream.StopAcquisition(ids_peak.AcquisitionStopMode_Default)
                    # Flush buffers
                    self.dataStream.Flush(ids_peak.DataStreamFlushMode_DiscardAll)
                    
                    # Revoke all buffers
                    for buffer in self.dataStream.AnnouncedBuffers():
                        self.dataStream.RevokeBuffer(buffer)
                    
                    self.dataStream = None
                
                # Unlock parameters
                self.nodemap.FindNode("TLParamsLocked").SetValue(0)
                self.device = None
                self._acquisition_running = False
                
        except Exception as e:
            logger.error(f"Error stopping camera: {e}")

    def _valid_name(self, path: str, ext: str):
        num = 0
        def build_string():
            return f"{path}_{num}{ext}"
        while exists(build_string()):
            num += 1
        return build_string()

    def get_data_stream_image(self):
        # Wait until the image is completed
        buffer = self.dataStream.WaitForFinishedBuffer(500)
        
        # Create IDS peak IPL image and convert it to target format
        ipl_image = ids_peak_ipl_extension.BufferToImage(buffer)
        converted_ipl_image = self._image_converter.Convert(
                ipl_image, TARGET_PIXEL_FORMAT)
        
        try:
            # Apply rotation if needed using ImageTransformer
            if self.rotation != 0:
                transformer = ids_peak_ipl.ImageTransformer()
                if self.rotation == 90:
                    converted_ipl_image = transformer.Rotate(converted_ipl_image, 90)
                elif self.rotation == 180:
                    converted_ipl_image = transformer.Rotate(converted_ipl_image, 180)
                elif self.rotation == 270:
                    converted_ipl_image = transformer.Rotate(converted_ipl_image, 270)

            self.dataStream.QueueBuffer(buffer)
            return converted_ipl_image
        
        except Exception as e:
            logger.error(f"Error in get_data_stream_image: {e}")
            self.dataStream.QueueBuffer(buffer)  # Make sure to queue the buffer even on error
            return converted_ipl_image

    def record(self, timer: int):
        """Records image frames into an AVI-container"""
        video = ids_peak_ipl.VideoWriter()
        cwd = os.getcwd()

        dropped_before = 0
        lost_before = 0

        try:
            # Create a new file
            video.Open(self._valid_name(cwd + "/" + "video", ".avi"))

            # Set target frame rate and gain
            self.set_remote_device_value("AcquisitionFrameRate", self.target_fps)
            self.set_remote_device_value("Gain", self.target_gain)

            video.Container().SetFramerate(self.target_fps)

            logger.info("Recording with: ")
            logger.info(f"  Framerate: {self.nodemap.FindNode('AcquisitionFrameRate').Value():.2f}")
            logger.info(f"  Gain: {self.nodemap.FindNode('Gain').Value():.2f}")
            
            data_stream_node_map = self.dataStream.NodeMaps()[0]
            dropped_before = data_stream_node_map.FindNode("StreamDroppedFrameCount").Value()
            lost_before = data_stream_node_map.FindNode("StreamLostFrameCount").Value()

        except Exception as e:
            self.warning.emit(str(e))
            raise

        logger.info("Recording...")
        limit = timer + time.time()
        while (limit - time.time()) > 0 and not self.killed:
            try:
                image = self.get_data_stream_image()
                # Append to video
                video.Append(image)

            except Exception as e:
                logger.warning(f"Warning: Exception caught: {str(e)}")

        if self.killed:
            return

        # Calculate statistics
        data_stream_node_map = self.dataStream.NodeMaps()[0]
        dropped_stream_frames = data_stream_node_map.FindNode(
            "StreamDroppedFrameCount").Value() - dropped_before
        lost_stream_frames = data_stream_node_map.FindNode(
            "StreamLostFrameCount").Value() - lost_before

        stats = RecordingStatistics(
            frames_encoded=video.NumFramesEncoded(),
            frames_video_dropped=video.NumFramesDropped(),
            frames_stream_dropped=dropped_stream_frames,
            frames_lost_stream=lost_stream_frames,
            duration=timer)

        video.Container().SetFramerate(stats.fps())
        video.WaitUntilFrameDone(10000)
        video.Close()
        self.recordingFinished.emit(stats)

    def start_recording(self):
        if self.acquisition_thread:
            self.acquisition_thread.is_recording = True

    def stop_recording(self):
        if self.acquisition_thread:
            self.acquisition_thread.is_recording = False

    def get_exposure_range(self):
        """Get the exposure time range in milliseconds"""
        try:
            for name in ["ExposureTime", "ExposureTimeAbs", "ExposureValue"]:
                if self.has_node(name):
                    node = self.nodemap.FindNode(name)
                    min_val = node.Minimum()
                    max_val = node.Maximum()
                    
                    # Convert to milliseconds if needed
                    if max_val > 1000:
                        return min_val/1000, max_val/1000
                    return min_val, max_val
        except:
            pass
        
        # Default range if not found
        return 0.1, 50
    
    def get_camera_settings(self):
        try:
            return {
                "frame_rate": self.nodemap.FindNode("AcquisitionFrameRate").Value(),
                "frame_rate_min": self.nodemap.FindNode("AcquisitionFrameRate").Minimum(),
                "frame_rate_max": self.nodemap.FindNode("AcquisitionFrameRate").Maximum(),
                "gain": self.nodemap.FindNode("Gain").Value(),
                "gain_min": self.nodemap.FindNode("Gain").Minimum(),
                "gain_max": self.nodemap.FindNode("Gain").Maximum(),
                "exposure_time": (self.nodemap.FindNode("ExposureTime").Value() / 1000 
                                if self.nodemap.FindNode("ExposureTime").Maximum() > 1000 
                                else self.nodemap.FindNode("ExposureTime").Value()),
                "exposure_time_min": (self.nodemap.FindNode("ExposureTime").Minimum() / 1000 
                                    if self.nodemap.FindNode("ExposureTime").Maximum() > 1000 
                                    else self.nodemap.FindNode("ExposureTime").Minimum()),
                "exposure_time_max": (self.nodemap.FindNode("ExposureTime").Maximum() / 1000 
                                    if self.nodemap.FindNode("ExposureTime").Maximum() > 1000 
                                    else self.nodemap.FindNode("ExposureTime").Maximum()),
                "width": self.nodemap.FindNode("Width").Value(),
                "height": self.nodemap.FindNode("Height").Value()
            }
        except Exception as e:
            logger.error(f"Error getting camera settings: {e}")
            return {
                "frame_rate": 0, "frame_rate_min": 0, "frame_rate_max": 0,
                "gain": 0, "gain_min": 0, "gain_max": 0,
                "exposure_time": 0, "exposure_time_min": 0, "exposure_time_max": 0,
                "width": 0, "height": 0
            }

    def apply_transformations(self, is_f_h):
        """Apply image transformations to the camera stream"""
        try:
            if not self._acquisition_running:
                return

            # Apply horizontal flip using ReverseX_Value_ctrl
            if self.has_node("ReverseX_Value_ctrl") and is_f_h:
                self.flip_horizontal = not self.flip_horizontal
                self.set_remote_device_value("ReverseX_Value_ctrl", 1 if self.flip_horizontal else 0)
            elif self.has_node("ReverseY_Value_ctrl") and not is_f_h:
                self.flip_vertical = not self.flip_vertical
                self.set_remote_device_value("ReverseY_Value_ctrl", 1 if self.flip_vertical else 0)

            # Log the current transformation state
            logger.info(f"Applied transformations: H-flip={self.flip_horizontal}, "
                       f"V-flip={self.flip_vertical}, Rotation={self.rotation}")

        except Exception as e:
            logger.error(f"Error applying transformations: {e}")

    def print_ids_peak_ipl_info(self):
        """Print all available functions and attributes of ids_peak_ipl package"""
        logger.info("=== IDS Peak IPL Package Information ===")
        
        # Print Image class methods
        logger.info("Image class methods:")
        for item in dir(ids_peak_ipl.Image):
            if not item.startswith('__'):
                logger.info(f"- {item}")
        
        # Print general package attributes
        logger.info("\nGeneral package attributes:")
        for item in dir(ids_peak_ipl):
            if not item.startswith('__'):
                logger.info(f"- {item}")
                
        # Print specific information about Image creation methods
        try:
            logger.info("\nImage creation method signatures:")
            logger.info(f"Image.__init__: {ids_peak_ipl.Image.__init__.__doc__}")
            # Try to get other creation methods if they exist
            if hasattr(ids_peak_ipl.Image, 'create'):
                logger.info(f"Image.create: {ids_peak_ipl.Image.create.__doc__}")
            if hasattr(ids_peak_ipl.Image, 'CreateFromSizeAndData'):
                logger.info(f"Image.CreateFromSizeAndData: {ids_peak_ipl.Image.CreateFromSizeAndData.__doc__}")
        except Exception as e:
            logger.error(f"Error getting method signatures: {e}")

class CameraListModel(QAbstractListModel):
    cameraFrameReady = Signal(str, int)  # Signal now includes serial number
    warning = Signal(str)  # Add warning signal
    recordingFinished = Signal(RecordingStatistics)
    cameraStateChanged = Signal()  # Add recordingFinished signal
    cameraListChanged = Signal()
    cameraConnected = Signal(str)  # New signal for camera connection (serial number)
    cameraDisconnected = Signal(str)  # New signal for camera disconnection (serial number)
    frameRateUpdated = Signal(dict)  # Add new signal for frame rate updates

    def __init__(self, engine):
        super().__init__()
        self.engine = engine  # Store the engine instance
        self.cameras = []
        self.camera_controllers = {}  # Dictionary to store controllers by serial number
        self.camera_started_flags = {}  # Dictionary to store start flags by serial number
        self.db = WorkspaceDatabase()
        
        # Initialize IDS peak library
        try:
            ids_peak.Library.Initialize()
        except Exception as e:
            logger.error(f"Failed to initialize IDS peak library: {e}")

        # Create timer for periodic device checking
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._check_devices)
        self.update_timer.start(3000)  # Check every 3 second
        
        # Initial device scan
        self.update_camera_list()

    def _check_devices(self):
        """Periodically check for device changes"""
        try:
            device_manager = ids_peak.DeviceManager.Instance()
            device_manager.Update()
            
            # Get current list of devices
            current_devices = []
            for device in device_manager.Devices():
                current_devices.append({
                    "modelName": device.ModelName(),
                    "serialNumber": device.SerialNumber()
                })
            
            # Get sets of serial numbers for comparison
            current_serials = {dev["serialNumber"] for dev in self.cameras}
            new_serials = {dev["serialNumber"] for dev in current_devices}
            
            # Check for new and disconnected cameras
            newly_connected = new_serials - current_serials
            disconnected = current_serials - new_serials
            
            if newly_connected or disconnected:
                self.beginResetModel()
                self.cameras = current_devices
                self.endResetModel()
                
                # Emit signals for newly connected cameras
                for serial in newly_connected:
                    logger.info(f"New camera connected: {serial}")
                    self.cameraConnected.emit(serial)
                
                # Handle disconnected cameras
                for serial in disconnected:
                    logger.info(f"Camera disconnected: {serial}")
                    if serial in self.camera_controllers:
                        self.stop_camera_stream(serial)
                    self.cameraDisconnected.emit(serial)
                
                self.cameraListChanged.emit()
                logger.info("Camera list updated: " + str(self.cameras))

        except Exception as e:
            logger.error(f"Error checking devices: {e}")

    @Slot(str, result=QObject)
    def get_camera_image_provider(self, serial_number):
        if serial_number in self.camera_controllers:
            return self.camera_controllers[serial_number].get_image_provider()
        return None

    def rowCount(self, parent=None):
        return len(self.cameras)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None
        
        camera = self.cameras[index.row()]
        if role == Qt.DisplayRole:
            return {
                "modelName": camera["modelName"],
                "serialNumber": camera["serialNumber"]
            }
        return None

    @Slot(result='QVariantList')
    def getCameraList(self):
        return [{"modelName": camera["modelName"], "serialNumber": camera["serialNumber"]} for camera in self.cameras]
    
    @Slot()
    def update_camera_list(self):
        self.beginResetModel()
        self.cameras = []
        
        try:
            ids_peak.Library.Initialize()
            device_manager = ids_peak.DeviceManager.Instance()
            device_manager.Update()
            
            for device in device_manager.Devices():
                logger.info(f": {device.ModelName()} ({device.SerialNumber()})")
                self.cameras.append({
                    "modelName": device.ModelName(),
                    "serialNumber": device.SerialNumber()
                })
                
        except Exception as e:
            logger.error(f"Error updating camera list: {e}")
            
        self.endResetModel()

    @Slot(str, int, dict)
    def start_camera_stream(self, serial_number, cameraspaceId, cameraSettings):
        if serial_number not in self.camera_controllers:
            try:
                # Create new controller for this camera
                controller = CameraController(serial_number)
                controller.cameraFrameReady.connect(
                    lambda: self.cameraFrameReady.emit(serial_number, cameraspaceId)
                )
                controller.warning.connect(self.warning)
                controller.recordingFinished.connect(self.recordingFinished)
                # Connect frame_rate_updated signal
                controller.frame_rate_updated.connect(self.frameRateUpdated)
                
                self.camera_controllers[serial_number] = controller
                
                # Register the image provider with a unique name
                self.engine.addImageProvider(f"live_{serial_number}", controller.get_image_provider())
                
                self.camera_controllers[serial_number].start_camera(serial_number, cameraSettings)
                self.camera_started_flags[serial_number] = True
                
                # Emit signals to update UI
                self.cameraStateChanged.emit()
                self.cameraListChanged.emit()
                
                logger.info(f"Camera started successfully: {serial_number}")
            except Exception as e:
                logger.error(f"Error starting camera {serial_number}: {e}")
                self.warning.emit(f"Failed to start camera: {str(e)}")

    @Slot(str)
    def stop_camera_stream(self, serial_number):
        if serial_number in self.camera_controllers:
            try:
                # Stop the specific camera controller
                controller = self.camera_controllers[serial_number]
                controller.stop_camera()
                
                # Update the camera started flag for this serial number
                self.camera_started_flags[serial_number] = False
                
                # Remove the controller
                del self.camera_controllers[serial_number]
                
                # Emit signals to update UI
                self.cameraStateChanged.emit()
                self.cameraListChanged.emit()
                
                logger.info(f"Camera stopped successfully: {serial_number}")
            except Exception as e:
                logger.error(f"Error stopping camera {serial_number}: {e}")

    @Slot()
    def start_recording(self):
        """Start recording video"""
        for controller in self.camera_controllers.values():
            controller.start_recording()

    @Slot()
    def stop_recording(self):
        """Stop recording video"""
        for controller in self.camera_controllers.values():
            controller.stop_recording()

    @Slot(str, result=bool)
    def is_camera_started(self, serial_number):
        started = self.camera_started_flags.get(serial_number, False)
        logger.info(f"Checking if camera {serial_number} is started: {started}")
        return started

    @Slot(str, result=dict)
    def get_camera_settings(self, serial_number):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            return controller.get_camera_settings()
        return {}

    @Slot(str, float)
    def update_frame_rate(self, serial_number, frame_rate):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.update_frame_rate(frame_rate)
        else:
            logger.warning(f"Camera with serial number {serial_number} not found.")

    @Slot(str, float)
    def update_gain(self, serial_number, gain):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.update_gain(gain)
        else:
            logger.warning(f"Camera with serial number {serial_number} not found.")

    @Slot(str, float)
    def update_exposure_time(self, serial_number, exposure_time):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.update_exposure_time(exposure_time)
        else:
            logger.warning(f"Camera with serial number {serial_number} not found.")

    @Slot(str, float)
    def update_analog_gain(self, serial_number, gain):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.update_analog_gain(gain)
        else:
            logger.warning(f"Camera with serial number {serial_number} not found.")

    @Slot(str, float)
    def update_digital_gain(self, serial_number, gain):
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.update_digital_gain(gain)
        else:
            logger.warning(f"Camera with serial number {serial_number} not found.")

    @Slot(str)
    def flip_horizontal(self, serial_number):
        """Toggle horizontal flip for specific camera"""
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            if(controller.rotation % 180 == 0):
                controller.apply_transformations(True)
            else:
                controller.apply_transformations(False)

    @Slot(str)
    def flip_vertical(self, serial_number):
        """Toggle vertical flip for specific camera"""
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            if(controller.rotation % 180 == 0):
                controller.apply_transformations(False)
            else:
                controller.apply_transformations(True)

    @Slot(str)
    def rotate_left(self, serial_number):
        """Rotate left (counter-clockwise) for specific camera"""
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.rotation = (controller.rotation + 90) % 360

    @Slot(str)
    def rotate_right(self, serial_number):
        """Rotate right (clockwise) for specific camera"""
        if serial_number in self.camera_controllers:
            controller = self.camera_controllers[serial_number]
            controller.rotation = (controller.rotation + 270) % 360

    @Slot(result=bool)
    def has_available_camera(self):
        """Check if there are any available (not started) cameras."""
        for camera in self.cameras:
            if not self.is_camera_started(camera['serialNumber']):
                return True
        return False

    @Slot(result='QVariant')
    def get_first_available_camera(self):
        """Get the first available (not started) camera."""
        for camera in self.cameras:
            if not self.is_camera_started(camera['serialNumber']):
                return {
                    'modelName': camera['modelName'],
                    'serialNumber': camera['serialNumber']
                }
        return None
