import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15

import "../components"
import "../components/separators"
import "../constants/WindowsConstants.js" as WindowsConstants

Rectangle {
    width: WindowsConstants.subWindowWidth
    Layout.fillHeight: true
    color: "lightgray"

    signal brightnessClosed

    property bool isCameraWindowsEmpty: true // Default value
    property int cameraspaceId: 0 // Set this to the correct cameraspace ID
    property string serialNumber: "" // Set this to the correct serial number in cameraManager

    function updateSettings(cameraspace_id, settings) {
        cameraspaceId = cameraspace_id;
        frameRateSlider.sliderValue = settings["frame_rate"];
        frameRateSlider.sliderFrom = settings["frame_rate_min"];
        frameRateSlider.sliderTo = settings["frame_rate_max"];

        exposureTimeSlider.sliderValue = settings["exposure_time"];
        exposureTimeSlider.sliderFrom = settings["exposure_time_min"];
        exposureTimeSlider.sliderTo = settings["exposure_time_max"];

        analogGainSlider.sliderValue = settings["gain"];
        analogGainSlider.sliderFrom = settings["gain_min"];
        analogGainSlider.sliderTo = settings["gain_max"];
    }

    ColumnLayout {
        width: parent.width
        height: parent.height
        spacing: 0

        Titlebar {
            Layout.fillWidth: true
            title: "Brightness/frame rate"
            titleIcon: "qrc:/resources/icons/brightness.png"

            onSubWindowClosed: {
                brightnessClosed();
            }
        }

        ColumnLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.margins: 10
            spacing: 10

            // Add Connections to handle frame rate updates
            Connections {
                target: cameraListModel
                function onFrameRateUpdated(frameRateInfo) {
                    frameRateSlider.sliderFrom = frameRateInfo.min
                    frameRateSlider.sliderTo = frameRateInfo.max
                    frameRateSlider.sliderValue = frameRateInfo.value
                }
            }

            // Slider for frame control
            TitleSliderSpin {
                id: frameRateSlider
                Layout.fillWidth: true
                title: "Frame rate [fps]"
                sliderFrom: 1
                sliderTo: 50.0
                sliderValue: 25.0
                enabled: !isCameraWindowsEmpty
                onSliderValueChanged: {
                    workspaceDatabase.set_camera_setting(serialNumber, "frame_rate", sliderValue);             

                    if (serialNumber !== "") {
                        cameraListModel.update_frame_rate(serialNumber, sliderValue);
                    } else {
                        console.log("Cannot update frame rate: Serial number is empty");
                    }
                }
            }

            // Slider for exposure control
            TitleSliderSpin {
                id: exposureTimeSlider
                Layout.fillWidth: true
                title: "Exposure time [ms]"
                sliderFrom: 1
                sliderTo: 50
                sliderValue: 5
                enabled: !isCameraWindowsEmpty
                onSliderValueChanged: {
                    workspaceDatabase.set_camera_setting(serialNumber, "exposure_time", sliderValue);

                    if (serialNumber !== "") {
                        cameraListModel.update_exposure_time(serialNumber, sliderValue);
                    } else {
                        console.log("Cannot update exposure time: Serial number is empty");
                    }
                }
            }

            HorizontalSeparator {
                Layout.fillWidth: true
                Layout.preferredWidth: parent.width * 0.9
            }

            // Slider for Analog control
            TitleSliderSpin {
                id: analogGainSlider
                Layout.fillWidth: true
                title: "Analog gain"
                sliderFrom: 0.1
                sliderTo: 50
                sliderValue: 10
                enabled: !isCameraWindowsEmpty
                onSliderValueChanged: {
                    workspaceDatabase.set_camera_setting(serialNumber, "analog_gain", sliderValue);

                    if (serialNumber !== "") {
                        cameraListModel.update_analog_gain(serialNumber, sliderValue);
                    } else {
                        console.log("Cannot update gain: Serial number is empty");
                    }
                }
            }

            // Slider for digital control
            TitleSliderSpin {
                id: digitalGainSlider
                Layout.fillWidth: true
                title: "Digital gain"
                sliderFrom: 0.1
                sliderTo: 50
                sliderValue: 10
                enabled: false
                onSliderValueChanged: {
                    workspaceDatabase.set_camera_setting(serialNumber, "digital_gain", sliderValue);

                    if (serialNumber !== "") {
                        cameraListModel.update_digital_gain(serialNumber, sliderValue);
                    } else {
                        console.log("Cannot update digital gain: Serial number is empty");
                    }
                }
            }

            HorizontalSeparator {
                Layout.fillWidth: true
                Layout.preferredWidth: parent.width * 0.9
            }

            // Add a spacer at the bottom
            Item {
                Layout.fillHeight: true
            }
        }
    }
}
