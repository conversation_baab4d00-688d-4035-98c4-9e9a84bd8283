import sys
import traceback
from PySide6.QtCore import <PERSON><PERSON><PERSON>Application
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine

def main():
    try:
        # Print initial debug info
        print("Starting application...")
        print("Python version:", sys.version)
        print("Current directory:", sys.executable)
        
        app = QGuiApplication(sys.argv)
        print("QGuiApplication created successfully")
        
        engine = QQmlApplicationEngine()
        print("QQmlApplicationEngine created successfully")
        
        # Print Qt version
        print("Qt version:", QCoreApplication.applicationVersion())
        
        return app.exec()
    
    except Exception as e:
        print("ERROR: Application failed to start")
        print(f"Exception type: {type(e).__name__}")
        print(f"Exception message: {str(e)}")
        print("\nTraceback:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"Critical error: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")  # Keep the window open
        sys.exit(1)
