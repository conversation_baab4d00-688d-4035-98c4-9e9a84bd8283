# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtMultimedia, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtMultimedia`

import PySide6.QtMultimedia
import PySide6.QtCore
import PySide6.QtGui

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QAbstractVideoBuffer(Shiboken.Object):

    class MapData(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, MapData: PySide6.QtMultimedia.QAbstractVideoBuffer.MapData, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def __init__(self, /) -> None: ...

    def format(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat: ...
    def map(self, mode: PySide6.QtMultimedia.QVideoFrame.MapMode, /) -> PySide6.QtMultimedia.QAbstractVideoBuffer.MapData: ...
    def unmap(self, /) -> None: ...


class QAudio(Shiboken.Object):

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        OpenError                 = ...  # 0x1
        IOError                   = ...  # 0x2
        UnderrunError             = ...  # 0x3
        FatalError                = ...  # 0x4

    class State(enum.Enum):

        ActiveState               = ...  # 0x0
        SuspendedState            = ...  # 0x1
        StoppedState              = ...  # 0x2
        IdleState                 = ...  # 0x3

    class VolumeScale(enum.Enum):

        LinearVolumeScale         = ...  # 0x0
        CubicVolumeScale          = ...  # 0x1
        LogarithmicVolumeScale    = ...  # 0x2
        DecibelVolumeScale        = ...  # 0x3


    @staticmethod
    def convertVolume(volume: float, from_: PySide6.QtMultimedia.QAudio.VolumeScale, to: PySide6.QtMultimedia.QAudio.VolumeScale, /) -> float: ...


class QAudioBuffer(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QAudioBuffer, /) -> None: ...
    @typing.overload
    def __init__(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, format: PySide6.QtMultimedia.QAudioFormat, /, startTime: int = ...) -> None: ...
    @typing.overload
    def __init__(self, numFrames: int, format: PySide6.QtMultimedia.QAudioFormat, /, startTime: int = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def byteCount(self, /) -> int: ...
    def constData(self, /) -> bytes | bytearray | memoryview: ...
    def data(self, /) -> bytes | bytearray | memoryview: ...
    def duration(self, /) -> int: ...
    def format(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def frameCount(self, /) -> int: ...
    def isValid(self, /) -> bool: ...
    def sampleCount(self, /) -> int: ...
    def startTime(self, /) -> int: ...
    def swap(self, other: PySide6.QtMultimedia.QAudioBuffer, /) -> None: ...


class QAudioBufferInput(PySide6.QtCore.QObject):

    readyToSendAudioBuffer   : typing.ClassVar[Signal] = ... # readyToSendAudioBuffer()

    @typing.overload
    def __init__(self, format: PySide6.QtMultimedia.QAudioFormat, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def format(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def sendAudioBuffer(self, audioBuffer: PySide6.QtMultimedia.QAudioBuffer, /) -> bool: ...


class QAudioBufferOutput(PySide6.QtCore.QObject):

    audioBufferReceived      : typing.ClassVar[Signal] = ... # audioBufferReceived(QAudioBuffer)

    @typing.overload
    def __init__(self, format: PySide6.QtMultimedia.QAudioFormat, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def format(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...


class QAudioDecoder(PySide6.QtCore.QObject):

    bufferAvailableChanged   : typing.ClassVar[Signal] = ... # bufferAvailableChanged(bool)
    bufferReady              : typing.ClassVar[Signal] = ... # bufferReady()
    durationChanged          : typing.ClassVar[Signal] = ... # durationChanged(qlonglong)
    error                    : typing.ClassVar[Signal] = ... # error(QAudioDecoder::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()
    formatChanged            : typing.ClassVar[Signal] = ... # formatChanged(QAudioFormat)
    isDecodingChanged        : typing.ClassVar[Signal] = ... # isDecodingChanged(bool)
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged(qlonglong)
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        ResourceError             = ...  # 0x1
        FormatError               = ...  # 0x2
        AccessDeniedError         = ...  # 0x3
        NotSupportedError         = ...  # 0x4


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, source: PySide6.QtCore.QUrl | None = ..., isDecoding: bool | None = ..., error: str | None = ..., bufferAvailable: bool | None = ...) -> None: ...

    def audioFormat(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def bufferAvailable(self, /) -> bool: ...
    def duration(self, /) -> int: ...
    def errorString(self, /) -> str: ...
    def isDecoding(self, /) -> bool: ...
    def isSupported(self, /) -> bool: ...
    def position(self, /) -> int: ...
    def read(self, /) -> PySide6.QtMultimedia.QAudioBuffer: ...
    def setAudioFormat(self, format: PySide6.QtMultimedia.QAudioFormat, /) -> None: ...
    def setSource(self, fileName: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setSourceDevice(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def sourceDevice(self, /) -> PySide6.QtCore.QIODevice: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...


class QAudioDevice(Shiboken.Object):

    class Mode(enum.Enum):

        Null                      = ...  # 0x0
        Input                     = ...  # 0x1
        Output                    = ...  # 0x2


    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QAudioDevice, /, *, id: PySide6.QtCore.QByteArray | None = ..., description: str | None = ..., isDefault: bool | None = ..., mode: PySide6.QtMultimedia.QAudioDevice.Mode | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, id: PySide6.QtCore.QByteArray | None = ..., description: str | None = ..., isDefault: bool | None = ..., mode: PySide6.QtMultimedia.QAudioDevice.Mode | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtMultimedia.QAudioDevice, /) -> bool: ...
    def __ne__(self, other: PySide6.QtMultimedia.QAudioDevice, /) -> bool: ...
    def channelConfiguration(self, /) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def description(self, /) -> str: ...
    def id(self, /) -> PySide6.QtCore.QByteArray: ...
    def isDefault(self, /) -> bool: ...
    def isFormatSupported(self, format: PySide6.QtMultimedia.QAudioFormat, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def maximumChannelCount(self, /) -> int: ...
    def maximumSampleRate(self, /) -> int: ...
    def minimumChannelCount(self, /) -> int: ...
    def minimumSampleRate(self, /) -> int: ...
    def mode(self, /) -> PySide6.QtMultimedia.QAudioDevice.Mode: ...
    def preferredFormat(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def supportedSampleFormats(self, /) -> typing.List[PySide6.QtMultimedia.QAudioFormat.SampleFormat]: ...
    def swap(self, other: PySide6.QtMultimedia.QAudioDevice, /) -> None: ...


class QAudioFormat(Shiboken.Object):

    class AudioChannelPosition(enum.Enum):

        UnknownPosition           = ...  # 0x0
        FrontLeft                 = ...  # 0x1
        FrontRight                = ...  # 0x2
        FrontCenter               = ...  # 0x3
        LFE                       = ...  # 0x4
        BackLeft                  = ...  # 0x5
        BackRight                 = ...  # 0x6
        FrontLeftOfCenter         = ...  # 0x7
        FrontRightOfCenter        = ...  # 0x8
        BackCenter                = ...  # 0x9
        SideLeft                  = ...  # 0xa
        SideRight                 = ...  # 0xb
        TopCenter                 = ...  # 0xc
        TopFrontLeft              = ...  # 0xd
        TopFrontCenter            = ...  # 0xe
        TopFrontRight             = ...  # 0xf
        TopBackLeft               = ...  # 0x10
        TopBackCenter             = ...  # 0x11
        TopBackRight              = ...  # 0x12
        LFE2                      = ...  # 0x13
        TopSideLeft               = ...  # 0x14
        TopSideRight              = ...  # 0x15
        BottomFrontCenter         = ...  # 0x16
        BottomFrontLeft           = ...  # 0x17
        BottomFrontRight          = ...  # 0x18

    class ChannelConfig(enum.Enum):

        ChannelConfigUnknown      = ...  # 0x0
        ChannelConfigStereo       = ...  # 0x6
        ChannelConfigMono         = ...  # 0x8
        ChannelConfig3Dot0        = ...  # 0xe
        ChannelConfig2Dot1        = ...  # 0x16
        ChannelConfig3Dot1        = ...  # 0x1e
        ChannelConfigSurround5Dot0 = ...  # 0x6e
        ChannelConfigSurround5Dot1 = ...  # 0x7e
        ChannelConfigSurround7Dot0 = ...  # 0xc6e
        ChannelConfigSurround7Dot1 = ...  # 0xc7e

    class SampleFormat(enum.Enum):

        Unknown                   = ...  # 0x0
        UInt8                     = ...  # 0x1
        Int16                     = ...  # 0x2
        Int32                     = ...  # 0x3
        Float                     = ...  # 0x4
        NSampleFormats            = ...  # 0x5


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QAudioFormat: PySide6.QtMultimedia.QAudioFormat, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtMultimedia.QAudioFormat, /) -> bool: ...
    def __ne__(self, b: PySide6.QtMultimedia.QAudioFormat, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def bytesForDuration(self, microseconds: int, /) -> int: ...
    def bytesForFrames(self, frameCount: int, /) -> int: ...
    def bytesPerFrame(self, /) -> int: ...
    def bytesPerSample(self, /) -> int: ...
    def channelConfig(self, /) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def channelCount(self, /) -> int: ...
    def channelOffset(self, channel: PySide6.QtMultimedia.QAudioFormat.AudioChannelPosition, /) -> int: ...
    @staticmethod
    def defaultChannelConfigForChannelCount(channelCount: int, /) -> PySide6.QtMultimedia.QAudioFormat.ChannelConfig: ...
    def durationForBytes(self, byteCount: int, /) -> int: ...
    def durationForFrames(self, frameCount: int, /) -> int: ...
    def framesForBytes(self, byteCount: int, /) -> int: ...
    def framesForDuration(self, microseconds: int, /) -> int: ...
    def isValid(self, /) -> bool: ...
    def normalizedSampleValue(self, sample: bytes | bytearray | memoryview, /) -> float: ...
    def sampleFormat(self, /) -> PySide6.QtMultimedia.QAudioFormat.SampleFormat: ...
    def sampleRate(self, /) -> int: ...
    def setChannelConfig(self, config: PySide6.QtMultimedia.QAudioFormat.ChannelConfig, /) -> None: ...
    def setChannelCount(self, channelCount: int, /) -> None: ...
    def setSampleFormat(self, f: PySide6.QtMultimedia.QAudioFormat.SampleFormat, /) -> None: ...
    def setSampleRate(self, sampleRate: int, /) -> None: ...


class QAudioInput(PySide6.QtCore.QObject):

    deviceChanged            : typing.ClassVar[Signal] = ... # deviceChanged()
    mutedChanged             : typing.ClassVar[Signal] = ... # mutedChanged(bool)
    volumeChanged            : typing.ClassVar[Signal] = ... # volumeChanged(float)

    @typing.overload
    def __init__(self, deviceInfo: PySide6.QtMultimedia.QAudioDevice, /, parent: PySide6.QtCore.QObject | None = ..., *, device: PySide6.QtMultimedia.QAudioDevice | None = ..., volume: float | None = ..., muted: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, device: PySide6.QtMultimedia.QAudioDevice | None = ..., volume: float | None = ..., muted: bool | None = ...) -> None: ...

    def device(self, /) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isMuted(self, /) -> bool: ...
    def setDevice(self, device: PySide6.QtMultimedia.QAudioDevice, /) -> None: ...
    def setMuted(self, muted: bool, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    def volume(self, /) -> float: ...


class QAudioOutput(PySide6.QtCore.QObject):

    deviceChanged            : typing.ClassVar[Signal] = ... # deviceChanged()
    mutedChanged             : typing.ClassVar[Signal] = ... # mutedChanged(bool)
    volumeChanged            : typing.ClassVar[Signal] = ... # volumeChanged(float)

    @typing.overload
    def __init__(self, device: PySide6.QtMultimedia.QAudioDevice, /, parent: PySide6.QtCore.QObject | None = ..., *, volume: float | None = ..., muted: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, device: PySide6.QtMultimedia.QAudioDevice | None = ..., volume: float | None = ..., muted: bool | None = ...) -> None: ...

    def device(self, /) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isMuted(self, /) -> bool: ...
    def setDevice(self, device: PySide6.QtMultimedia.QAudioDevice, /) -> None: ...
    def setMuted(self, muted: bool, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    def volume(self, /) -> float: ...


class QAudioSink(PySide6.QtCore.QObject):

    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QAudio::State)

    @typing.overload
    def __init__(self, audioDeviceInfo: PySide6.QtMultimedia.QAudioDevice, /, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def bufferSize(self, /) -> int: ...
    def bytesFree(self, /) -> int: ...
    def elapsedUSecs(self, /) -> int: ...
    def error(self, /) -> PySide6.QtMultimedia.QAudio.Error: ...
    def format(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def isNull(self, /) -> bool: ...
    def processedUSecs(self, /) -> int: ...
    def reset(self, /) -> None: ...
    def resume(self, /) -> None: ...
    def setBufferSize(self, bytes: int, /) -> None: ...
    def setVolume(self, arg__1: float, /) -> None: ...
    @typing.overload
    def start(self, /) -> PySide6.QtCore.QIODevice: ...
    @typing.overload
    def start(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def state(self, /) -> PySide6.QtMultimedia.QAudio.State: ...
    def stop(self, /) -> None: ...
    def suspend(self, /) -> None: ...
    def volume(self, /) -> float: ...


class QAudioSource(PySide6.QtCore.QObject):

    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QAudio::State)

    @typing.overload
    def __init__(self, audioDeviceInfo: PySide6.QtMultimedia.QAudioDevice, /, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, format: PySide6.QtMultimedia.QAudioFormat = ..., parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def bufferSize(self, /) -> int: ...
    def bytesAvailable(self, /) -> int: ...
    def elapsedUSecs(self, /) -> int: ...
    def error(self, /) -> PySide6.QtMultimedia.QAudio.Error: ...
    def format(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def isNull(self, /) -> bool: ...
    def processedUSecs(self, /) -> int: ...
    def reset(self, /) -> None: ...
    def resume(self, /) -> None: ...
    def setBufferSize(self, bytes: int, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    @typing.overload
    def start(self, /) -> PySide6.QtCore.QIODevice: ...
    @typing.overload
    def start(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def state(self, /) -> PySide6.QtMultimedia.QAudio.State: ...
    def stop(self, /) -> None: ...
    def suspend(self, /) -> None: ...
    def volume(self, /) -> float: ...


class QCamera(PySide6.QtCore.QObject):

    activeChanged            : typing.ClassVar[Signal] = ... # activeChanged(bool)
    brightnessChanged        : typing.ClassVar[Signal] = ... # brightnessChanged()
    cameraDeviceChanged      : typing.ClassVar[Signal] = ... # cameraDeviceChanged()
    cameraFormatChanged      : typing.ClassVar[Signal] = ... # cameraFormatChanged()
    colorTemperatureChanged  : typing.ClassVar[Signal] = ... # colorTemperatureChanged()
    contrastChanged          : typing.ClassVar[Signal] = ... # contrastChanged()
    customFocusPointChanged  : typing.ClassVar[Signal] = ... # customFocusPointChanged()
    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QCamera::Error,QString)
    exposureCompensationChanged: typing.ClassVar[Signal] = ... # exposureCompensationChanged(float)
    exposureModeChanged      : typing.ClassVar[Signal] = ... # exposureModeChanged()
    exposureTimeChanged      : typing.ClassVar[Signal] = ... # exposureTimeChanged(float)
    flashModeChanged         : typing.ClassVar[Signal] = ... # flashModeChanged()
    flashReady               : typing.ClassVar[Signal] = ... # flashReady(bool)
    focusDistanceChanged     : typing.ClassVar[Signal] = ... # focusDistanceChanged(float)
    focusModeChanged         : typing.ClassVar[Signal] = ... # focusModeChanged()
    focusPointChanged        : typing.ClassVar[Signal] = ... # focusPointChanged()
    hueChanged               : typing.ClassVar[Signal] = ... # hueChanged()
    isoSensitivityChanged    : typing.ClassVar[Signal] = ... # isoSensitivityChanged(int)
    manualExposureTimeChanged: typing.ClassVar[Signal] = ... # manualExposureTimeChanged(float)
    manualIsoSensitivityChanged: typing.ClassVar[Signal] = ... # manualIsoSensitivityChanged(int)
    maximumZoomFactorChanged : typing.ClassVar[Signal] = ... # maximumZoomFactorChanged(float)
    minimumZoomFactorChanged : typing.ClassVar[Signal] = ... # minimumZoomFactorChanged(float)
    saturationChanged        : typing.ClassVar[Signal] = ... # saturationChanged()
    supportedFeaturesChanged : typing.ClassVar[Signal] = ... # supportedFeaturesChanged()
    torchModeChanged         : typing.ClassVar[Signal] = ... # torchModeChanged()
    whiteBalanceModeChanged  : typing.ClassVar[Signal] = ... # whiteBalanceModeChanged()
    zoomFactorChanged        : typing.ClassVar[Signal] = ... # zoomFactorChanged(float)

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        CameraError               = ...  # 0x1

    class ExposureMode(enum.Enum):

        ExposureAuto              = ...  # 0x0
        ExposureManual            = ...  # 0x1
        ExposurePortrait          = ...  # 0x2
        ExposureNight             = ...  # 0x3
        ExposureSports            = ...  # 0x4
        ExposureSnow              = ...  # 0x5
        ExposureBeach             = ...  # 0x6
        ExposureAction            = ...  # 0x7
        ExposureLandscape         = ...  # 0x8
        ExposureNightPortrait     = ...  # 0x9
        ExposureTheatre           = ...  # 0xa
        ExposureSunset            = ...  # 0xb
        ExposureSteadyPhoto       = ...  # 0xc
        ExposureFireworks         = ...  # 0xd
        ExposureParty             = ...  # 0xe
        ExposureCandlelight       = ...  # 0xf
        ExposureBarcode           = ...  # 0x10

    class Feature(enum.Flag):

        ColorTemperature          = ...  # 0x1
        ExposureCompensation      = ...  # 0x2
        IsoSensitivity            = ...  # 0x4
        ManualExposureTime        = ...  # 0x8
        CustomFocusPoint          = ...  # 0x10
        FocusDistance             = ...  # 0x20

    class FlashMode(enum.Enum):

        FlashOff                  = ...  # 0x0
        FlashOn                   = ...  # 0x1
        FlashAuto                 = ...  # 0x2

    class FocusMode(enum.Enum):

        FocusModeAuto             = ...  # 0x0
        FocusModeAutoNear         = ...  # 0x1
        FocusModeAutoFar          = ...  # 0x2
        FocusModeHyperfocal       = ...  # 0x3
        FocusModeInfinity         = ...  # 0x4
        FocusModeManual           = ...  # 0x5

    class TorchMode(enum.Enum):

        TorchOff                  = ...  # 0x0
        TorchOn                   = ...  # 0x1
        TorchAuto                 = ...  # 0x2

    class WhiteBalanceMode(enum.Enum):

        WhiteBalanceAuto          = ...  # 0x0
        WhiteBalanceManual        = ...  # 0x1
        WhiteBalanceSunlight      = ...  # 0x2
        WhiteBalanceCloudy        = ...  # 0x3
        WhiteBalanceShade         = ...  # 0x4
        WhiteBalanceTungsten      = ...  # 0x5
        WhiteBalanceFluorescent   = ...  # 0x6
        WhiteBalanceFlash         = ...  # 0x7
        WhiteBalanceSunset        = ...  # 0x8


    @typing.overload
    def __init__(self, position: PySide6.QtMultimedia.QCameraDevice.Position, /, parent: PySide6.QtCore.QObject | None = ..., *, active: bool | None = ..., cameraDevice: PySide6.QtMultimedia.QCameraDevice | None = ..., error: PySide6.QtMultimedia.QCamera.Error | None = ..., errorString: str | None = ..., cameraFormat: PySide6.QtMultimedia.QCameraFormat | None = ..., focusMode: PySide6.QtMultimedia.QCamera.FocusMode | None = ..., focusPoint: PySide6.QtCore.QPointF | None = ..., customFocusPoint: PySide6.QtCore.QPointF | None = ..., focusDistance: float | None = ..., minimumZoomFactor: float | None = ..., maximumZoomFactor: float | None = ..., zoomFactor: float | None = ..., exposureTime: float | None = ..., manualExposureTime: float | None = ..., isoSensitivity: int | None = ..., manualIsoSensitivity: int | None = ..., exposureCompensation: float | None = ..., exposureMode: PySide6.QtMultimedia.QCamera.ExposureMode | None = ..., flashReady: bool | None = ..., flashMode: PySide6.QtMultimedia.QCamera.FlashMode | None = ..., torchMode: PySide6.QtMultimedia.QCamera.TorchMode | None = ..., whiteBalanceMode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode | None = ..., colorTemperature: int | None = ..., supportedFeatures: PySide6.QtMultimedia.QCamera.Feature | None = ...) -> None: ...
    @typing.overload
    def __init__(self, cameraDevice: PySide6.QtMultimedia.QCameraDevice, /, parent: PySide6.QtCore.QObject | None = ..., *, active: bool | None = ..., error: PySide6.QtMultimedia.QCamera.Error | None = ..., errorString: str | None = ..., cameraFormat: PySide6.QtMultimedia.QCameraFormat | None = ..., focusMode: PySide6.QtMultimedia.QCamera.FocusMode | None = ..., focusPoint: PySide6.QtCore.QPointF | None = ..., customFocusPoint: PySide6.QtCore.QPointF | None = ..., focusDistance: float | None = ..., minimumZoomFactor: float | None = ..., maximumZoomFactor: float | None = ..., zoomFactor: float | None = ..., exposureTime: float | None = ..., manualExposureTime: float | None = ..., isoSensitivity: int | None = ..., manualIsoSensitivity: int | None = ..., exposureCompensation: float | None = ..., exposureMode: PySide6.QtMultimedia.QCamera.ExposureMode | None = ..., flashReady: bool | None = ..., flashMode: PySide6.QtMultimedia.QCamera.FlashMode | None = ..., torchMode: PySide6.QtMultimedia.QCamera.TorchMode | None = ..., whiteBalanceMode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode | None = ..., colorTemperature: int | None = ..., supportedFeatures: PySide6.QtMultimedia.QCamera.Feature | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, active: bool | None = ..., cameraDevice: PySide6.QtMultimedia.QCameraDevice | None = ..., error: PySide6.QtMultimedia.QCamera.Error | None = ..., errorString: str | None = ..., cameraFormat: PySide6.QtMultimedia.QCameraFormat | None = ..., focusMode: PySide6.QtMultimedia.QCamera.FocusMode | None = ..., focusPoint: PySide6.QtCore.QPointF | None = ..., customFocusPoint: PySide6.QtCore.QPointF | None = ..., focusDistance: float | None = ..., minimumZoomFactor: float | None = ..., maximumZoomFactor: float | None = ..., zoomFactor: float | None = ..., exposureTime: float | None = ..., manualExposureTime: float | None = ..., isoSensitivity: int | None = ..., manualIsoSensitivity: int | None = ..., exposureCompensation: float | None = ..., exposureMode: PySide6.QtMultimedia.QCamera.ExposureMode | None = ..., flashReady: bool | None = ..., flashMode: PySide6.QtMultimedia.QCamera.FlashMode | None = ..., torchMode: PySide6.QtMultimedia.QCamera.TorchMode | None = ..., whiteBalanceMode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode | None = ..., colorTemperature: int | None = ..., supportedFeatures: PySide6.QtMultimedia.QCamera.Feature | None = ...) -> None: ...

    def cameraDevice(self, /) -> PySide6.QtMultimedia.QCameraDevice: ...
    def cameraFormat(self, /) -> PySide6.QtMultimedia.QCameraFormat: ...
    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def colorTemperature(self, /) -> int: ...
    def customFocusPoint(self, /) -> PySide6.QtCore.QPointF: ...
    def error(self, /) -> PySide6.QtMultimedia.QCamera.Error: ...
    def errorString(self, /) -> str: ...
    def exposureCompensation(self, /) -> float: ...
    def exposureMode(self, /) -> PySide6.QtMultimedia.QCamera.ExposureMode: ...
    def exposureTime(self, /) -> float: ...
    def flashMode(self, /) -> PySide6.QtMultimedia.QCamera.FlashMode: ...
    def focusDistance(self, /) -> float: ...
    def focusMode(self, /) -> PySide6.QtMultimedia.QCamera.FocusMode: ...
    def focusPoint(self, /) -> PySide6.QtCore.QPointF: ...
    def isActive(self, /) -> bool: ...
    def isAvailable(self, /) -> bool: ...
    def isExposureModeSupported(self, mode: PySide6.QtMultimedia.QCamera.ExposureMode, /) -> bool: ...
    def isFlashModeSupported(self, mode: PySide6.QtMultimedia.QCamera.FlashMode, /) -> bool: ...
    def isFlashReady(self, /) -> bool: ...
    def isFocusModeSupported(self, mode: PySide6.QtMultimedia.QCamera.FocusMode, /) -> bool: ...
    def isTorchModeSupported(self, mode: PySide6.QtMultimedia.QCamera.TorchMode, /) -> bool: ...
    def isWhiteBalanceModeSupported(self, mode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode, /) -> bool: ...
    def isoSensitivity(self, /) -> int: ...
    def manualExposureTime(self, /) -> float: ...
    def manualIsoSensitivity(self, /) -> int: ...
    def maximumExposureTime(self, /) -> float: ...
    def maximumIsoSensitivity(self, /) -> int: ...
    def maximumZoomFactor(self, /) -> float: ...
    def minimumExposureTime(self, /) -> float: ...
    def minimumIsoSensitivity(self, /) -> int: ...
    def minimumZoomFactor(self, /) -> float: ...
    def setActive(self, active: bool, /) -> None: ...
    def setAutoExposureTime(self, /) -> None: ...
    def setAutoIsoSensitivity(self, /) -> None: ...
    def setCameraDevice(self, cameraDevice: PySide6.QtMultimedia.QCameraDevice, /) -> None: ...
    def setCameraFormat(self, format: PySide6.QtMultimedia.QCameraFormat, /) -> None: ...
    def setColorTemperature(self, colorTemperature: int, /) -> None: ...
    def setCustomFocusPoint(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    def setExposureCompensation(self, ev: float, /) -> None: ...
    def setExposureMode(self, mode: PySide6.QtMultimedia.QCamera.ExposureMode, /) -> None: ...
    def setFlashMode(self, mode: PySide6.QtMultimedia.QCamera.FlashMode, /) -> None: ...
    def setFocusDistance(self, d: float, /) -> None: ...
    def setFocusMode(self, mode: PySide6.QtMultimedia.QCamera.FocusMode, /) -> None: ...
    def setManualExposureTime(self, seconds: float, /) -> None: ...
    def setManualIsoSensitivity(self, iso: int, /) -> None: ...
    def setTorchMode(self, mode: PySide6.QtMultimedia.QCamera.TorchMode, /) -> None: ...
    def setWhiteBalanceMode(self, mode: PySide6.QtMultimedia.QCamera.WhiteBalanceMode, /) -> None: ...
    def setZoomFactor(self, factor: float, /) -> None: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...
    def supportedFeatures(self, /) -> PySide6.QtMultimedia.QCamera.Feature: ...
    def torchMode(self, /) -> PySide6.QtMultimedia.QCamera.TorchMode: ...
    def whiteBalanceMode(self, /) -> PySide6.QtMultimedia.QCamera.WhiteBalanceMode: ...
    def zoomFactor(self, /) -> float: ...
    def zoomTo(self, zoom: float, rate: float, /) -> None: ...


class QCameraDevice(Shiboken.Object):

    class Position(enum.Enum):

        UnspecifiedPosition       = ...  # 0x0
        BackFace                  = ...  # 0x1
        FrontFace                 = ...  # 0x2


    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QCameraDevice, /, *, id: PySide6.QtCore.QByteArray | None = ..., description: str | None = ..., isDefault: bool | None = ..., position: PySide6.QtMultimedia.QCameraDevice.Position | None = ..., videoFormats: collections.abc.Sequence[PySide6.QtMultimedia.QCameraFormat] | None = ..., correctionAngle: PySide6.QtMultimedia.QtVideo.Rotation | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, id: PySide6.QtCore.QByteArray | None = ..., description: str | None = ..., isDefault: bool | None = ..., position: PySide6.QtMultimedia.QCameraDevice.Position | None = ..., videoFormats: collections.abc.Sequence[PySide6.QtMultimedia.QCameraFormat] | None = ..., correctionAngle: PySide6.QtMultimedia.QtVideo.Rotation | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtMultimedia.QCameraDevice, /) -> bool: ...
    def __ne__(self, other: PySide6.QtMultimedia.QCameraDevice, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def correctionAngle(self, /) -> PySide6.QtMultimedia.QtVideo.Rotation: ...
    def description(self, /) -> str: ...
    def id(self, /) -> PySide6.QtCore.QByteArray: ...
    def isDefault(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def photoResolutions(self, /) -> typing.List[PySide6.QtCore.QSize]: ...
    def position(self, /) -> PySide6.QtMultimedia.QCameraDevice.Position: ...
    def videoFormats(self, /) -> typing.List[PySide6.QtMultimedia.QCameraFormat]: ...


class QCameraFormat(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QCameraFormat, /, *, resolution: PySide6.QtCore.QSize | None = ..., pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat | None = ..., minFrameRate: float | None = ..., maxFrameRate: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, resolution: PySide6.QtCore.QSize | None = ..., pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat | None = ..., minFrameRate: float | None = ..., maxFrameRate: float | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtMultimedia.QCameraFormat, /) -> bool: ...
    def __ne__(self, other: PySide6.QtMultimedia.QCameraFormat, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def maxFrameRate(self, /) -> float: ...
    def minFrameRate(self, /) -> float: ...
    def pixelFormat(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    def resolution(self, /) -> PySide6.QtCore.QSize: ...


class QCapturableWindow(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QCapturableWindow, /, *, description: str | None = ..., isValid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, description: str | None = ..., isValid: bool | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __ne__(self, rhs: PySide6.QtMultimedia.QCapturableWindow, /) -> bool: ...
    def description(self, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def swap(self, other: PySide6.QtMultimedia.QCapturableWindow, /) -> None: ...


class QImageCapture(PySide6.QtCore.QObject):

    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(int,QImageCapture::Error,QString)
    fileFormatChanged        : typing.ClassVar[Signal] = ... # fileFormatChanged()
    imageAvailable           : typing.ClassVar[Signal] = ... # imageAvailable(int,QVideoFrame)
    imageCaptured            : typing.ClassVar[Signal] = ... # imageCaptured(int,QImage)
    imageExposed             : typing.ClassVar[Signal] = ... # imageExposed(int)
    imageMetadataAvailable   : typing.ClassVar[Signal] = ... # imageMetadataAvailable(int,QMediaMetaData)
    imageSaved               : typing.ClassVar[Signal] = ... # imageSaved(int,QString)
    metaDataChanged          : typing.ClassVar[Signal] = ... # metaDataChanged()
    qualityChanged           : typing.ClassVar[Signal] = ... # qualityChanged()
    readyForCaptureChanged   : typing.ClassVar[Signal] = ... # readyForCaptureChanged(bool)
    resolutionChanged        : typing.ClassVar[Signal] = ... # resolutionChanged()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        NotReadyError             = ...  # 0x1
        ResourceError             = ...  # 0x2
        OutOfSpaceError           = ...  # 0x3
        NotSupportedFeatureError  = ...  # 0x4
        FormatError               = ...  # 0x5

    class FileFormat(enum.Enum):

        UnspecifiedFormat         = ...  # 0x0
        JPEG                      = ...  # 0x1
        PNG                       = ...  # 0x2
        WebP                      = ...  # 0x3
        LastFileFormat            = ...  # 0x4
        Tiff                      = ...  # 0x4

    class Quality(enum.Enum):

        VeryLowQuality            = ...  # 0x0
        LowQuality                = ...  # 0x1
        NormalQuality             = ...  # 0x2
        HighQuality               = ...  # 0x3
        VeryHighQuality           = ...  # 0x4


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, readyForCapture: bool | None = ..., metaData: PySide6.QtMultimedia.QMediaMetaData | None = ..., error: PySide6.QtMultimedia.QImageCapture.Error | None = ..., errorString: str | None = ..., fileFormat: PySide6.QtMultimedia.QImageCapture.FileFormat | None = ..., quality: PySide6.QtMultimedia.QImageCapture.Quality | None = ...) -> None: ...

    def addMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData, /) -> None: ...
    def capture(self, /) -> int: ...
    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def captureToFile(self, /, location: str = ...) -> int: ...
    def error(self, /) -> PySide6.QtMultimedia.QImageCapture.Error: ...
    def errorString(self, /) -> str: ...
    def fileFormat(self, /) -> PySide6.QtMultimedia.QImageCapture.FileFormat: ...
    @staticmethod
    def fileFormatDescription(c: PySide6.QtMultimedia.QImageCapture.FileFormat, /) -> str: ...
    @staticmethod
    def fileFormatName(c: PySide6.QtMultimedia.QImageCapture.FileFormat, /) -> str: ...
    def isAvailable(self, /) -> bool: ...
    def isReadyForCapture(self, /) -> bool: ...
    def metaData(self, /) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def quality(self, /) -> PySide6.QtMultimedia.QImageCapture.Quality: ...
    def resolution(self, /) -> PySide6.QtCore.QSize: ...
    def setFileFormat(self, format: PySide6.QtMultimedia.QImageCapture.FileFormat, /) -> None: ...
    def setMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData, /) -> None: ...
    def setQuality(self, quality: PySide6.QtMultimedia.QImageCapture.Quality, /) -> None: ...
    @typing.overload
    def setResolution(self, arg__1: PySide6.QtCore.QSize, /) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int, /) -> None: ...
    @staticmethod
    def supportedFormats() -> typing.List[PySide6.QtMultimedia.QImageCapture.FileFormat]: ...


class QIntList: ...


class QMediaCaptureSession(PySide6.QtCore.QObject):

    audioBufferInputChanged  : typing.ClassVar[Signal] = ... # audioBufferInputChanged()
    audioInputChanged        : typing.ClassVar[Signal] = ... # audioInputChanged()
    audioOutputChanged       : typing.ClassVar[Signal] = ... # audioOutputChanged()
    cameraChanged            : typing.ClassVar[Signal] = ... # cameraChanged()
    imageCaptureChanged      : typing.ClassVar[Signal] = ... # imageCaptureChanged()
    recorderChanged          : typing.ClassVar[Signal] = ... # recorderChanged()
    screenCaptureChanged     : typing.ClassVar[Signal] = ... # screenCaptureChanged()
    videoFrameInputChanged   : typing.ClassVar[Signal] = ... # videoFrameInputChanged()
    videoOutputChanged       : typing.ClassVar[Signal] = ... # videoOutputChanged()
    windowCaptureChanged     : typing.ClassVar[Signal] = ... # windowCaptureChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, audioInput: PySide6.QtMultimedia.QAudioInput | None = ..., audioBufferInput: PySide6.QtMultimedia.QAudioBufferInput | None = ..., audioOutput: PySide6.QtMultimedia.QAudioOutput | None = ..., camera: PySide6.QtMultimedia.QCamera | None = ..., screenCapture: PySide6.QtMultimedia.QScreenCapture | None = ..., windowCapture: PySide6.QtMultimedia.QWindowCapture | None = ..., videoFrameInput: PySide6.QtMultimedia.QVideoFrameInput | None = ..., imageCapture: PySide6.QtMultimedia.QImageCapture | None = ..., recorder: PySide6.QtMultimedia.QMediaRecorder | None = ..., videoOutput: PySide6.QtCore.QObject | None = ...) -> None: ...

    def audioBufferInput(self, /) -> PySide6.QtMultimedia.QAudioBufferInput: ...
    def audioInput(self, /) -> PySide6.QtMultimedia.QAudioInput: ...
    def audioOutput(self, /) -> PySide6.QtMultimedia.QAudioOutput: ...
    def camera(self, /) -> PySide6.QtMultimedia.QCamera: ...
    def imageCapture(self, /) -> PySide6.QtMultimedia.QImageCapture: ...
    def recorder(self, /) -> PySide6.QtMultimedia.QMediaRecorder: ...
    def screenCapture(self, /) -> PySide6.QtMultimedia.QScreenCapture: ...
    def setAudioBufferInput(self, input: PySide6.QtMultimedia.QAudioBufferInput, /) -> None: ...
    def setAudioInput(self, input: PySide6.QtMultimedia.QAudioInput, /) -> None: ...
    def setAudioOutput(self, output: PySide6.QtMultimedia.QAudioOutput, /) -> None: ...
    def setCamera(self, camera: PySide6.QtMultimedia.QCamera, /) -> None: ...
    def setImageCapture(self, imageCapture: PySide6.QtMultimedia.QImageCapture, /) -> None: ...
    def setRecorder(self, recorder: PySide6.QtMultimedia.QMediaRecorder, /) -> None: ...
    def setScreenCapture(self, screenCapture: PySide6.QtMultimedia.QScreenCapture, /) -> None: ...
    def setVideoFrameInput(self, input: PySide6.QtMultimedia.QVideoFrameInput, /) -> None: ...
    def setVideoOutput(self, output: PySide6.QtCore.QObject, /) -> None: ...
    def setVideoSink(self, sink: PySide6.QtMultimedia.QVideoSink, /) -> None: ...
    def setWindowCapture(self, windowCapture: PySide6.QtMultimedia.QWindowCapture, /) -> None: ...
    def videoFrameInput(self, /) -> PySide6.QtMultimedia.QVideoFrameInput: ...
    def videoOutput(self, /) -> PySide6.QtCore.QObject: ...
    def videoSink(self, /) -> PySide6.QtMultimedia.QVideoSink: ...
    def windowCapture(self, /) -> PySide6.QtMultimedia.QWindowCapture: ...


class QMediaDevices(PySide6.QtCore.QObject):

    audioInputsChanged       : typing.ClassVar[Signal] = ... # audioInputsChanged()
    audioOutputsChanged      : typing.ClassVar[Signal] = ... # audioOutputsChanged()
    videoInputsChanged       : typing.ClassVar[Signal] = ... # videoInputsChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, audioInputs: collections.abc.Sequence[PySide6.QtMultimedia.QAudioDevice] | None = ..., audioOutputs: collections.abc.Sequence[PySide6.QtMultimedia.QAudioDevice] | None = ..., videoInputs: collections.abc.Sequence[PySide6.QtMultimedia.QCameraDevice] | None = ..., defaultAudioInput: PySide6.QtMultimedia.QAudioDevice | None = ..., defaultAudioOutput: PySide6.QtMultimedia.QAudioDevice | None = ..., defaultVideoInput: PySide6.QtMultimedia.QCameraDevice | None = ...) -> None: ...

    @staticmethod
    def audioInputs() -> typing.List[PySide6.QtMultimedia.QAudioDevice]: ...
    @staticmethod
    def audioOutputs() -> typing.List[PySide6.QtMultimedia.QAudioDevice]: ...
    def connectNotify(self, signal: PySide6.QtCore.QMetaMethod, /) -> None: ...
    @staticmethod
    def defaultAudioInput() -> PySide6.QtMultimedia.QAudioDevice: ...
    @staticmethod
    def defaultAudioOutput() -> PySide6.QtMultimedia.QAudioDevice: ...
    @staticmethod
    def defaultVideoInput() -> PySide6.QtMultimedia.QCameraDevice: ...
    @staticmethod
    def videoInputs() -> typing.List[PySide6.QtMultimedia.QCameraDevice]: ...


class QMediaFormat(Shiboken.Object):

    class AudioCodec(enum.Enum):

        Unspecified               = ...  # -1
        MP3                       = ...  # 0x0
        AAC                       = ...  # 0x1
        AC3                       = ...  # 0x2
        EAC3                      = ...  # 0x3
        FLAC                      = ...  # 0x4
        DolbyTrueHD               = ...  # 0x5
        Opus                      = ...  # 0x6
        Vorbis                    = ...  # 0x7
        Wave                      = ...  # 0x8
        WMA                       = ...  # 0x9
        ALAC                      = ...  # 0xa
        LastAudioCodec            = ...  # 0xa

    class ConversionMode(enum.Enum):

        Encode                    = ...  # 0x0
        Decode                    = ...  # 0x1

    class FileFormat(enum.Enum):

        UnspecifiedFormat         = ...  # -1
        WMV                       = ...  # 0x0
        AVI                       = ...  # 0x1
        Matroska                  = ...  # 0x2
        MPEG4                     = ...  # 0x3
        Ogg                       = ...  # 0x4
        QuickTime                 = ...  # 0x5
        WebM                      = ...  # 0x6
        Mpeg4Audio                = ...  # 0x7
        AAC                       = ...  # 0x8
        WMA                       = ...  # 0x9
        MP3                       = ...  # 0xa
        FLAC                      = ...  # 0xb
        LastFileFormat            = ...  # 0xc
        Wave                      = ...  # 0xc

    class ResolveFlags(enum.Enum):

        NoFlags                   = ...  # 0x0
        RequiresVideo             = ...  # 0x1

    class VideoCodec(enum.Enum):

        Unspecified               = ...  # -1
        MPEG1                     = ...  # 0x0
        MPEG2                     = ...  # 0x1
        MPEG4                     = ...  # 0x2
        H264                      = ...  # 0x3
        H265                      = ...  # 0x4
        VP8                       = ...  # 0x5
        VP9                       = ...  # 0x6
        AV1                       = ...  # 0x7
        Theora                    = ...  # 0x8
        WMV                       = ...  # 0x9
        LastVideoCodec            = ...  # 0xa
        MotionJPEG                = ...  # 0xa


    @typing.overload
    def __init__(self, /, format: PySide6.QtMultimedia.QMediaFormat.FileFormat = ..., *, fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat | None = ..., audioCodec: PySide6.QtMultimedia.QMediaFormat.AudioCodec | None = ..., videoCodec: PySide6.QtMultimedia.QMediaFormat.VideoCodec | None = ...) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QMediaFormat, /, *, fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat | None = ..., audioCodec: PySide6.QtMultimedia.QMediaFormat.AudioCodec | None = ..., videoCodec: PySide6.QtMultimedia.QMediaFormat.VideoCodec | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtMultimedia.QMediaFormat | PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> bool: ...
    def __ne__(self, other: PySide6.QtMultimedia.QMediaFormat | PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> bool: ...
    def audioCodec(self, /) -> PySide6.QtMultimedia.QMediaFormat.AudioCodec: ...
    @staticmethod
    def audioCodecDescription(codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec, /) -> str: ...
    @staticmethod
    def audioCodecName(codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec, /) -> str: ...
    def fileFormat(self, /) -> PySide6.QtMultimedia.QMediaFormat.FileFormat: ...
    @staticmethod
    def fileFormatDescription(fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> str: ...
    @staticmethod
    def fileFormatName(fileFormat: PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> str: ...
    def isSupported(self, mode: PySide6.QtMultimedia.QMediaFormat.ConversionMode, /) -> bool: ...
    def mimeType(self, /) -> PySide6.QtCore.QMimeType: ...
    def resolveForEncoding(self, flags: PySide6.QtMultimedia.QMediaFormat.ResolveFlags, /) -> None: ...
    def setAudioCodec(self, codec: PySide6.QtMultimedia.QMediaFormat.AudioCodec, /) -> None: ...
    def setFileFormat(self, f: PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> None: ...
    def setVideoCodec(self, codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec, /) -> None: ...
    def supportedAudioCodecs(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode, /) -> typing.List[PySide6.QtMultimedia.QMediaFormat.AudioCodec]: ...
    def supportedFileFormats(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode, /) -> typing.List[PySide6.QtMultimedia.QMediaFormat.FileFormat]: ...
    def supportedVideoCodecs(self, m: PySide6.QtMultimedia.QMediaFormat.ConversionMode, /) -> typing.List[PySide6.QtMultimedia.QMediaFormat.VideoCodec]: ...
    def swap(self, other: PySide6.QtMultimedia.QMediaFormat | PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> None: ...
    def videoCodec(self, /) -> PySide6.QtMultimedia.QMediaFormat.VideoCodec: ...
    @staticmethod
    def videoCodecDescription(codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec, /) -> str: ...
    @staticmethod
    def videoCodecName(codec: PySide6.QtMultimedia.QMediaFormat.VideoCodec, /) -> str: ...


class QMediaMetaData(Shiboken.Object):

    class Key(enum.Enum):

        Title                     = ...  # 0x0
        Author                    = ...  # 0x1
        Comment                   = ...  # 0x2
        Description               = ...  # 0x3
        Genre                     = ...  # 0x4
        Date                      = ...  # 0x5
        Language                  = ...  # 0x6
        Publisher                 = ...  # 0x7
        Copyright                 = ...  # 0x8
        Url                       = ...  # 0x9
        Duration                  = ...  # 0xa
        MediaType                 = ...  # 0xb
        FileFormat                = ...  # 0xc
        AudioBitRate              = ...  # 0xd
        AudioCodec                = ...  # 0xe
        VideoBitRate              = ...  # 0xf
        VideoCodec                = ...  # 0x10
        VideoFrameRate            = ...  # 0x11
        AlbumTitle                = ...  # 0x12
        AlbumArtist               = ...  # 0x13
        ContributingArtist        = ...  # 0x14
        TrackNumber               = ...  # 0x15
        Composer                  = ...  # 0x16
        LeadPerformer             = ...  # 0x17
        ThumbnailImage            = ...  # 0x18
        CoverArtImage             = ...  # 0x19
        Orientation               = ...  # 0x1a
        Resolution                = ...  # 0x1b
        HasHdrContent             = ...  # 0x1c


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QMediaMetaData: PySide6.QtMultimedia.QMediaMetaData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtMultimedia.QMediaMetaData, /) -> bool: ...
    def __ne__(self, b: PySide6.QtMultimedia.QMediaMetaData, /) -> bool: ...
    def clear(self, /) -> None: ...
    def insert(self, k: PySide6.QtMultimedia.QMediaMetaData.Key, value: typing.Any, /) -> None: ...
    def isEmpty(self, /) -> bool: ...
    @staticmethod
    def keyType(key: PySide6.QtMultimedia.QMediaMetaData.Key, /) -> PySide6.QtCore.QMetaType: ...
    def keys(self, /) -> typing.List[PySide6.QtMultimedia.QMediaMetaData.Key]: ...
    @staticmethod
    def metaDataKeyToString(k: PySide6.QtMultimedia.QMediaMetaData.Key, /) -> str: ...
    def remove(self, k: PySide6.QtMultimedia.QMediaMetaData.Key, /) -> None: ...
    def stringValue(self, k: PySide6.QtMultimedia.QMediaMetaData.Key, /) -> str: ...
    def value(self, k: PySide6.QtMultimedia.QMediaMetaData.Key, /) -> typing.Any: ...


class QMediaPlayer(PySide6.QtCore.QObject):

    activeTracksChanged      : typing.ClassVar[Signal] = ... # activeTracksChanged()
    audioBufferOutputChanged : typing.ClassVar[Signal] = ... # audioBufferOutputChanged()
    audioOutputChanged       : typing.ClassVar[Signal] = ... # audioOutputChanged()
    bufferProgressChanged    : typing.ClassVar[Signal] = ... # bufferProgressChanged(float)
    durationChanged          : typing.ClassVar[Signal] = ... # durationChanged(qlonglong)
    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QMediaPlayer::Error,QString)
    hasAudioChanged          : typing.ClassVar[Signal] = ... # hasAudioChanged(bool)
    hasVideoChanged          : typing.ClassVar[Signal] = ... # hasVideoChanged(bool)
    loopsChanged             : typing.ClassVar[Signal] = ... # loopsChanged()
    mediaStatusChanged       : typing.ClassVar[Signal] = ... # mediaStatusChanged(QMediaPlayer::MediaStatus)
    metaDataChanged          : typing.ClassVar[Signal] = ... # metaDataChanged()
    playbackRateChanged      : typing.ClassVar[Signal] = ... # playbackRateChanged(double)
    playbackStateChanged     : typing.ClassVar[Signal] = ... # playbackStateChanged(QMediaPlayer::PlaybackState)
    playingChanged           : typing.ClassVar[Signal] = ... # playingChanged(bool)
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged(qlonglong)
    seekableChanged          : typing.ClassVar[Signal] = ... # seekableChanged(bool)
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged(QUrl)
    tracksChanged            : typing.ClassVar[Signal] = ... # tracksChanged()
    videoOutputChanged       : typing.ClassVar[Signal] = ... # videoOutputChanged()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        ResourceError             = ...  # 0x1
        FormatError               = ...  # 0x2
        NetworkError              = ...  # 0x3
        AccessDeniedError         = ...  # 0x4

    class Loops(enum.IntEnum):

        Infinite                  = ...  # -1
        Once                      = ...  # 0x1

    class MediaStatus(enum.Enum):

        NoMedia                   = ...  # 0x0
        LoadingMedia              = ...  # 0x1
        LoadedMedia               = ...  # 0x2
        StalledMedia              = ...  # 0x3
        BufferingMedia            = ...  # 0x4
        BufferedMedia             = ...  # 0x5
        EndOfMedia                = ...  # 0x6
        InvalidMedia              = ...  # 0x7

    class PlaybackState(enum.Enum):

        StoppedState              = ...  # 0x0
        PlayingState              = ...  # 0x1
        PausedState               = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, source: PySide6.QtCore.QUrl | None = ..., duration: int | None = ..., position: int | None = ..., bufferProgress: float | None = ..., hasAudio: bool | None = ..., hasVideo: bool | None = ..., seekable: bool | None = ..., playing: bool | None = ..., playbackRate: float | None = ..., loops: int | None = ..., playbackState: PySide6.QtMultimedia.QMediaPlayer.PlaybackState | None = ..., mediaStatus: PySide6.QtMultimedia.QMediaPlayer.MediaStatus | None = ..., metaData: PySide6.QtMultimedia.QMediaMetaData | None = ..., error: PySide6.QtMultimedia.QMediaPlayer.Error | None = ..., errorString: str | None = ..., videoOutput: PySide6.QtCore.QObject | None = ..., audioOutput: PySide6.QtMultimedia.QAudioOutput | None = ..., audioBufferOutput: PySide6.QtMultimedia.QAudioBufferOutput | None = ..., audioTracks: collections.abc.Sequence[PySide6.QtMultimedia.QMediaMetaData] | None = ..., videoTracks: collections.abc.Sequence[PySide6.QtMultimedia.QMediaMetaData] | None = ..., subtitleTracks: collections.abc.Sequence[PySide6.QtMultimedia.QMediaMetaData] | None = ..., activeAudioTrack: int | None = ..., activeVideoTrack: int | None = ..., activeSubtitleTrack: int | None = ...) -> None: ...

    def activeAudioTrack(self, /) -> int: ...
    def activeSubtitleTrack(self, /) -> int: ...
    def activeVideoTrack(self, /) -> int: ...
    def audioBufferOutput(self, /) -> PySide6.QtMultimedia.QAudioBufferOutput: ...
    def audioOutput(self, /) -> PySide6.QtMultimedia.QAudioOutput: ...
    def audioTracks(self, /) -> typing.List[PySide6.QtMultimedia.QMediaMetaData]: ...
    def bufferProgress(self, /) -> float: ...
    def bufferedTimeRange(self, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    def duration(self, /) -> int: ...
    def error(self, /) -> PySide6.QtMultimedia.QMediaPlayer.Error: ...
    def errorString(self, /) -> str: ...
    def hasAudio(self, /) -> bool: ...
    def hasVideo(self, /) -> bool: ...
    def isAvailable(self, /) -> bool: ...
    def isPlaying(self, /) -> bool: ...
    def isSeekable(self, /) -> bool: ...
    def loops(self, /) -> int: ...
    def mediaStatus(self, /) -> PySide6.QtMultimedia.QMediaPlayer.MediaStatus: ...
    def metaData(self, /) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def pause(self, /) -> None: ...
    def play(self, /) -> None: ...
    def playbackRate(self, /) -> float: ...
    def playbackState(self, /) -> PySide6.QtMultimedia.QMediaPlayer.PlaybackState: ...
    def position(self, /) -> int: ...
    def setActiveAudioTrack(self, index: int, /) -> None: ...
    def setActiveSubtitleTrack(self, index: int, /) -> None: ...
    def setActiveVideoTrack(self, index: int, /) -> None: ...
    def setAudioBufferOutput(self, output: PySide6.QtMultimedia.QAudioBufferOutput, /) -> None: ...
    def setAudioOutput(self, output: PySide6.QtMultimedia.QAudioOutput, /) -> None: ...
    def setLoops(self, loops: int, /) -> None: ...
    def setPlaybackRate(self, rate: float, /) -> None: ...
    def setPosition(self, position: int, /) -> None: ...
    def setSource(self, source: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setSourceDevice(self, device: PySide6.QtCore.QIODevice, /, sourceUrl: PySide6.QtCore.QUrl | str = ...) -> None: ...
    def setVideoOutput(self, arg__1: PySide6.QtCore.QObject, /) -> None: ...
    def setVideoSink(self, sink: PySide6.QtMultimedia.QVideoSink, /) -> None: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def sourceDevice(self, /) -> PySide6.QtCore.QIODevice: ...
    def stop(self, /) -> None: ...
    def subtitleTracks(self, /) -> typing.List[PySide6.QtMultimedia.QMediaMetaData]: ...
    def videoOutput(self, /) -> PySide6.QtCore.QObject: ...
    def videoSink(self, /) -> PySide6.QtMultimedia.QVideoSink: ...
    def videoTracks(self, /) -> typing.List[PySide6.QtMultimedia.QMediaMetaData]: ...


class QMediaRecorder(PySide6.QtCore.QObject):

    actualLocationChanged    : typing.ClassVar[Signal] = ... # actualLocationChanged(QUrl)
    audioBitRateChanged      : typing.ClassVar[Signal] = ... # audioBitRateChanged()
    audioChannelCountChanged : typing.ClassVar[Signal] = ... # audioChannelCountChanged()
    audioSampleRateChanged   : typing.ClassVar[Signal] = ... # audioSampleRateChanged()
    autoStopChanged          : typing.ClassVar[Signal] = ... # autoStopChanged()
    durationChanged          : typing.ClassVar[Signal] = ... # durationChanged(qlonglong)
    encoderSettingsChanged   : typing.ClassVar[Signal] = ... # encoderSettingsChanged()
    encodingModeChanged      : typing.ClassVar[Signal] = ... # encodingModeChanged()
    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(Error,QString)
    mediaFormatChanged       : typing.ClassVar[Signal] = ... # mediaFormatChanged()
    metaDataChanged          : typing.ClassVar[Signal] = ... # metaDataChanged()
    qualityChanged           : typing.ClassVar[Signal] = ... # qualityChanged()
    recorderStateChanged     : typing.ClassVar[Signal] = ... # recorderStateChanged(RecorderState)
    videoBitRateChanged      : typing.ClassVar[Signal] = ... # videoBitRateChanged()
    videoFrameRateChanged    : typing.ClassVar[Signal] = ... # videoFrameRateChanged()
    videoResolutionChanged   : typing.ClassVar[Signal] = ... # videoResolutionChanged()

    class EncodingMode(enum.Enum):

        ConstantQualityEncoding   = ...  # 0x0
        ConstantBitRateEncoding   = ...  # 0x1
        AverageBitRateEncoding    = ...  # 0x2
        TwoPassEncoding           = ...  # 0x3

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        ResourceError             = ...  # 0x1
        FormatError               = ...  # 0x2
        OutOfSpaceError           = ...  # 0x3
        LocationNotWritable       = ...  # 0x4

    class Quality(enum.Enum):

        VeryLowQuality            = ...  # 0x0
        LowQuality                = ...  # 0x1
        NormalQuality             = ...  # 0x2
        HighQuality               = ...  # 0x3
        VeryHighQuality           = ...  # 0x4

    class RecorderState(enum.Enum):

        StoppedState              = ...  # 0x0
        RecordingState            = ...  # 0x1
        PausedState               = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, recorderState: PySide6.QtMultimedia.QMediaRecorder.RecorderState | None = ..., duration: int | None = ..., outputLocation: PySide6.QtCore.QUrl | None = ..., actualLocation: PySide6.QtCore.QUrl | None = ..., metaData: PySide6.QtMultimedia.QMediaMetaData | None = ..., error: PySide6.QtMultimedia.QMediaRecorder.Error | None = ..., errorString: str | None = ..., mediaFormat: PySide6.QtMultimedia.QMediaFormat | None = ..., quality: PySide6.QtMultimedia.QMediaRecorder.Quality | None = ..., encodingMode: PySide6.QtMultimedia.QMediaRecorder.EncodingMode | None = ..., videoResolution: PySide6.QtCore.QSize | None = ..., videoFrameRate: float | None = ..., videoBitRate: int | None = ..., audioBitRate: int | None = ..., audioChannelCount: int | None = ..., audioSampleRate: int | None = ..., autoStop: bool | None = ...) -> None: ...

    def actualLocation(self, /) -> PySide6.QtCore.QUrl: ...
    def addMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData, /) -> None: ...
    def audioBitRate(self, /) -> int: ...
    def audioChannelCount(self, /) -> int: ...
    def audioSampleRate(self, /) -> int: ...
    def autoStop(self, /) -> bool: ...
    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def duration(self, /) -> int: ...
    def encodingMode(self, /) -> PySide6.QtMultimedia.QMediaRecorder.EncodingMode: ...
    def error(self, /) -> PySide6.QtMultimedia.QMediaRecorder.Error: ...
    def errorString(self, /) -> str: ...
    def isAvailable(self, /) -> bool: ...
    def mediaFormat(self, /) -> PySide6.QtMultimedia.QMediaFormat: ...
    def metaData(self, /) -> PySide6.QtMultimedia.QMediaMetaData: ...
    def outputDevice(self, /) -> PySide6.QtCore.QIODevice: ...
    def outputLocation(self, /) -> PySide6.QtCore.QUrl: ...
    def pause(self, /) -> None: ...
    def quality(self, /) -> PySide6.QtMultimedia.QMediaRecorder.Quality: ...
    def record(self, /) -> None: ...
    def recorderState(self, /) -> PySide6.QtMultimedia.QMediaRecorder.RecorderState: ...
    def setAudioBitRate(self, bitRate: int, /) -> None: ...
    def setAudioChannelCount(self, channels: int, /) -> None: ...
    def setAudioSampleRate(self, sampleRate: int, /) -> None: ...
    def setAutoStop(self, autoStop: bool, /) -> None: ...
    def setEncodingMode(self, arg__1: PySide6.QtMultimedia.QMediaRecorder.EncodingMode, /) -> None: ...
    def setMediaFormat(self, format: PySide6.QtMultimedia.QMediaFormat | PySide6.QtMultimedia.QMediaFormat.FileFormat, /) -> None: ...
    def setMetaData(self, metaData: PySide6.QtMultimedia.QMediaMetaData, /) -> None: ...
    def setOutputDevice(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    def setOutputLocation(self, location: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setQuality(self, quality: PySide6.QtMultimedia.QMediaRecorder.Quality, /) -> None: ...
    def setVideoBitRate(self, bitRate: int, /) -> None: ...
    def setVideoFrameRate(self, frameRate: float, /) -> None: ...
    @typing.overload
    def setVideoResolution(self, arg__1: PySide6.QtCore.QSize, /) -> None: ...
    @typing.overload
    def setVideoResolution(self, width: int, height: int, /) -> None: ...
    def stop(self, /) -> None: ...
    def videoBitRate(self, /) -> int: ...
    def videoFrameRate(self, /) -> float: ...
    def videoResolution(self, /) -> PySide6.QtCore.QSize: ...


class QMediaTimeRange(Shiboken.Object):

    class Interval(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Interval: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
        @typing.overload
        def __init__(self, start: int, end: int, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def __eq__(self, rhs: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> bool: ...
        def __ne__(self, rhs: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> bool: ...
        def __repr__(self, /) -> str: ...
        def contains(self, time: int, /) -> bool: ...
        def end(self, /) -> int: ...
        def isNormal(self, /) -> bool: ...
        def normalized(self, /) -> PySide6.QtMultimedia.QMediaTimeRange.Interval: ...
        def start(self, /) -> int: ...
        def translated(self, offset: int, /) -> PySide6.QtMultimedia.QMediaTimeRange.Interval: ...


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
    @typing.overload
    def __init__(self, range: PySide6.QtMultimedia.QMediaTimeRange, /) -> None: ...
    @typing.overload
    def __init__(self, start: int, end: int, /) -> None: ...

    def __add__(self, r2: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> bool: ...
    @typing.overload  # type: ignore[misc]
    def __iadd__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @typing.overload
    def __iadd__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @typing.overload  # type: ignore[misc]
    def __isub__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @typing.overload
    def __isub__(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    def __ne__(self, rhs: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def __sub__(self, r2: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> PySide6.QtMultimedia.QMediaTimeRange: ...
    @typing.overload
    def addInterval(self, interval: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
    @typing.overload
    def addInterval(self, start: int, end: int, /) -> None: ...
    def addTimeRange(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
    def clear(self, /) -> None: ...
    def contains(self, time: int, /) -> bool: ...
    def earliestTime(self, /) -> int: ...
    def intervals(self, /) -> typing.List[PySide6.QtMultimedia.QMediaTimeRange.Interval]: ...
    def isContinuous(self, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def latestTime(self, /) -> int: ...
    @typing.overload
    def removeInterval(self, interval: PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
    @typing.overload
    def removeInterval(self, start: int, end: int, /) -> None: ...
    def removeTimeRange(self, arg__1: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...
    def swap(self, other: PySide6.QtMultimedia.QMediaTimeRange | PySide6.QtMultimedia.QMediaTimeRange.Interval, /) -> None: ...


class QScreenCapture(PySide6.QtCore.QObject):

    activeChanged            : typing.ClassVar[Signal] = ... # activeChanged(bool)
    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QScreenCapture::Error,QString)
    screenChanged            : typing.ClassVar[Signal] = ... # screenChanged(QScreen*)

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InternalError             = ...  # 0x1
        CapturingNotSupported     = ...  # 0x2
        CaptureFailed             = ...  # 0x4
        NotFound                  = ...  # 0x5


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, active: bool | None = ..., screen: PySide6.QtGui.QScreen | None = ..., error: PySide6.QtMultimedia.QScreenCapture.Error | None = ..., errorString: str | None = ...) -> None: ...

    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def error(self, /) -> PySide6.QtMultimedia.QScreenCapture.Error: ...
    def errorString(self, /) -> str: ...
    def isActive(self, /) -> bool: ...
    def screen(self, /) -> PySide6.QtGui.QScreen: ...
    def setActive(self, active: bool, /) -> None: ...
    def setScreen(self, screen: PySide6.QtGui.QScreen, /) -> None: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...


class QSoundEffect(PySide6.QtCore.QObject):

    audioDeviceChanged       : typing.ClassVar[Signal] = ... # audioDeviceChanged()
    loadedChanged            : typing.ClassVar[Signal] = ... # loadedChanged()
    loopCountChanged         : typing.ClassVar[Signal] = ... # loopCountChanged()
    loopsRemainingChanged    : typing.ClassVar[Signal] = ... # loopsRemainingChanged()
    mutedChanged             : typing.ClassVar[Signal] = ... # mutedChanged()
    playingChanged           : typing.ClassVar[Signal] = ... # playingChanged()
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged()
    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged()
    volumeChanged            : typing.ClassVar[Signal] = ... # volumeChanged()

    class Loop(enum.Enum):

        Infinite                  = ...  # -2

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Loading                   = ...  # 0x1
        Ready                     = ...  # 0x2
        Error                     = ...  # 0x3


    @typing.overload
    def __init__(self, audioDevice: PySide6.QtMultimedia.QAudioDevice, /, parent: PySide6.QtCore.QObject | None = ..., *, source: PySide6.QtCore.QUrl | None = ..., loops: int | None = ..., loopsRemaining: int | None = ..., volume: float | None = ..., muted: bool | None = ..., playing: bool | None = ..., status: PySide6.QtMultimedia.QSoundEffect.Status | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, source: PySide6.QtCore.QUrl | None = ..., loops: int | None = ..., loopsRemaining: int | None = ..., volume: float | None = ..., muted: bool | None = ..., playing: bool | None = ..., status: PySide6.QtMultimedia.QSoundEffect.Status | None = ..., audioDevice: PySide6.QtMultimedia.QAudioDevice | None = ...) -> None: ...

    def audioDevice(self, /) -> PySide6.QtMultimedia.QAudioDevice: ...
    def isLoaded(self, /) -> bool: ...
    def isMuted(self, /) -> bool: ...
    def isPlaying(self, /) -> bool: ...
    def loopCount(self, /) -> int: ...
    def loopsRemaining(self, /) -> int: ...
    def play(self, /) -> None: ...
    def setAudioDevice(self, device: PySide6.QtMultimedia.QAudioDevice, /) -> None: ...
    def setLoopCount(self, loopCount: int, /) -> None: ...
    def setMuted(self, muted: bool, /) -> None: ...
    def setSource(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def status(self, /) -> PySide6.QtMultimedia.QSoundEffect.Status: ...
    def stop(self, /) -> None: ...
    @staticmethod
    def supportedMimeTypes() -> typing.List[str]: ...
    def volume(self, /) -> float: ...


class QVideoFrame(Shiboken.Object):

    class HandleType(enum.Enum):

        NoHandle                  = ...  # 0x0
        RhiTextureHandle          = ...  # 0x1

    class MapMode(enum.Enum):

        NotMapped                 = ...  # 0x0
        ReadOnly                  = ...  # 0x1
        WriteOnly                 = ...  # 0x2
        ReadWrite                 = ...  # 0x3

    class PaintOptions(Shiboken.Object):

        class PaintFlag(enum.Flag):

            DontDrawSubtitles         = ...  # 0x1


        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, PaintOptions: PySide6.QtMultimedia.QVideoFrame.PaintOptions, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class RotationAngle(enum.Enum):

        Rotation0                 = ...  # 0x0
        Rotation90                = ...  # 0x5a
        Rotation180               = ...  # 0xb4
        Rotation270               = ...  # 0x10e


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, image: PySide6.QtGui.QImage, /) -> None: ...
    @typing.overload
    def __init__(self, buffer: PySide6.QtMultimedia.QAbstractVideoBuffer, format: PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtMultimedia.QVideoFrame, /) -> None: ...
    @typing.overload
    def __init__(self, format: PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, /) -> bool: ...
    def __ne__(self, other: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def bits(self, plane: int, /) -> bytes | bytearray | memoryview: ...
    def bytesPerLine(self, plane: int, /) -> int: ...
    def endTime(self, /) -> int: ...
    def handleType(self, /) -> PySide6.QtMultimedia.QVideoFrame.HandleType: ...
    def height(self, /) -> int: ...
    def isMapped(self, /) -> bool: ...
    def isReadable(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def isWritable(self, /) -> bool: ...
    def map(self, mode: PySide6.QtMultimedia.QVideoFrame.MapMode, /) -> bool: ...
    def mapMode(self, /) -> PySide6.QtMultimedia.QVideoFrame.MapMode: ...
    def mappedBytes(self, plane: int, /) -> int: ...
    def mirrored(self, /) -> bool: ...
    def paint(self, painter: PySide6.QtGui.QPainter, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, options: PySide6.QtMultimedia.QVideoFrame.PaintOptions, /) -> None: ...
    def pixelFormat(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    def planeCount(self, /) -> int: ...
    def rotation(self, /) -> PySide6.QtMultimedia.QtVideo.Rotation: ...
    def rotationAngle(self, /) -> PySide6.QtMultimedia.QVideoFrame.RotationAngle: ...
    def setEndTime(self, time: int, /) -> None: ...
    def setMirrored(self, arg__1: bool, /) -> None: ...
    def setRotation(self, angle: PySide6.QtMultimedia.QtVideo.Rotation, /) -> None: ...
    def setRotationAngle(self, angle: PySide6.QtMultimedia.QVideoFrame.RotationAngle, /) -> None: ...
    def setStartTime(self, time: int, /) -> None: ...
    def setStreamFrameRate(self, rate: float, /) -> None: ...
    def setSubtitleText(self, text: str, /) -> None: ...
    def size(self, /) -> PySide6.QtCore.QSize: ...
    def startTime(self, /) -> int: ...
    def streamFrameRate(self, /) -> float: ...
    def subtitleText(self, /) -> str: ...
    def surfaceFormat(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat: ...
    def swap(self, other: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...
    def toImage(self, /) -> PySide6.QtGui.QImage: ...
    def unmap(self, /) -> None: ...
    def videoBuffer(self, /) -> PySide6.QtMultimedia.QAbstractVideoBuffer: ...
    def width(self, /) -> int: ...


class QVideoFrameFormat(Shiboken.Object):

    class ColorRange(enum.Enum):

        ColorRange_Unknown        = ...  # 0x0
        ColorRange_Video          = ...  # 0x1
        ColorRange_Full           = ...  # 0x2

    class ColorSpace(enum.Enum):

        ColorSpace_Undefined      = ...  # 0x0
        ColorSpace_BT601          = ...  # 0x1
        ColorSpace_BT709          = ...  # 0x2
        ColorSpace_AdobeRgb       = ...  # 0x5
        ColorSpace_BT2020         = ...  # 0x6

    class ColorTransfer(enum.Enum):

        ColorTransfer_Unknown     = ...  # 0x0
        ColorTransfer_BT709       = ...  # 0x1
        ColorTransfer_BT601       = ...  # 0x2
        ColorTransfer_Linear      = ...  # 0x3
        ColorTransfer_Gamma22     = ...  # 0x4
        ColorTransfer_Gamma28     = ...  # 0x5
        ColorTransfer_ST2084      = ...  # 0x6
        ColorTransfer_STD_B67     = ...  # 0x7

    class Direction(enum.Enum):

        TopToBottom               = ...  # 0x0
        BottomToTop               = ...  # 0x1

    class PixelFormat(enum.Enum):

        Format_Invalid            = ...  # 0x0
        Format_ARGB8888           = ...  # 0x1
        Format_ARGB8888_Premultiplied = ...  # 0x2
        Format_XRGB8888           = ...  # 0x3
        Format_BGRA8888           = ...  # 0x4
        Format_BGRA8888_Premultiplied = ...  # 0x5
        Format_BGRX8888           = ...  # 0x6
        Format_ABGR8888           = ...  # 0x7
        Format_XBGR8888           = ...  # 0x8
        Format_RGBA8888           = ...  # 0x9
        Format_RGBX8888           = ...  # 0xa
        Format_AYUV               = ...  # 0xb
        Format_AYUV_Premultiplied = ...  # 0xc
        Format_YUV420P            = ...  # 0xd
        Format_YUV422P            = ...  # 0xe
        Format_YV12               = ...  # 0xf
        Format_UYVY               = ...  # 0x10
        Format_YUYV               = ...  # 0x11
        Format_NV12               = ...  # 0x12
        Format_NV21               = ...  # 0x13
        Format_IMC1               = ...  # 0x14
        Format_IMC2               = ...  # 0x15
        Format_IMC3               = ...  # 0x16
        Format_IMC4               = ...  # 0x17
        Format_Y8                 = ...  # 0x18
        Format_Y16                = ...  # 0x19
        Format_P010               = ...  # 0x1a
        Format_P016               = ...  # 0x1b
        Format_SamplerExternalOES = ...  # 0x1c
        Format_Jpeg               = ...  # 0x1d
        Format_SamplerRect        = ...  # 0x1e
        Format_YUV420P10          = ...  # 0x1f

    class YCbCrColorSpace(enum.Enum):

        YCbCr_Undefined           = ...  # 0x0
        YCbCr_BT601               = ...  # 0x1
        YCbCr_BT709               = ...  # 0x2
        YCbCr_xvYCC601            = ...  # 0x3
        YCbCr_xvYCC709            = ...  # 0x4
        YCbCr_JPEG                = ...  # 0x5
        YCbCr_BT2020              = ...  # 0x6


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, size: PySide6.QtCore.QSize, pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat, /) -> None: ...
    @typing.overload
    def __init__(self, format: PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, format: PySide6.QtMultimedia.QVideoFrameFormat, /) -> bool: ...
    def __ne__(self, format: PySide6.QtMultimedia.QVideoFrameFormat, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def colorRange(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorRange: ...
    def colorSpace(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorSpace: ...
    def colorTransfer(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.ColorTransfer: ...
    def fragmentShaderFileName(self, /) -> str: ...
    def frameHeight(self, /) -> int: ...
    def frameRate(self, /) -> float: ...
    def frameSize(self, /) -> PySide6.QtCore.QSize: ...
    def frameWidth(self, /) -> int: ...
    @staticmethod
    def imageFormatFromPixelFormat(format: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat, /) -> PySide6.QtGui.QImage.Format: ...
    def isMirrored(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def maxLuminance(self, /) -> float: ...
    def pixelFormat(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    @staticmethod
    def pixelFormatFromImageFormat(format: PySide6.QtGui.QImage.Format, /) -> PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat: ...
    @staticmethod
    def pixelFormatToString(pixelFormat: PySide6.QtMultimedia.QVideoFrameFormat.PixelFormat, /) -> str: ...
    def planeCount(self, /) -> int: ...
    def rotation(self, /) -> PySide6.QtMultimedia.QtVideo.Rotation: ...
    def scanLineDirection(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.Direction: ...
    def setColorRange(self, range: PySide6.QtMultimedia.QVideoFrameFormat.ColorRange, /) -> None: ...
    def setColorSpace(self, colorSpace: PySide6.QtMultimedia.QVideoFrameFormat.ColorSpace, /) -> None: ...
    def setColorTransfer(self, colorTransfer: PySide6.QtMultimedia.QVideoFrameFormat.ColorTransfer, /) -> None: ...
    def setFrameRate(self, rate: float, /) -> None: ...
    @typing.overload
    def setFrameSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
    @typing.overload
    def setFrameSize(self, width: int, height: int, /) -> None: ...
    def setMaxLuminance(self, lum: float, /) -> None: ...
    def setMirrored(self, mirrored: bool, /) -> None: ...
    def setRotation(self, rotation: PySide6.QtMultimedia.QtVideo.Rotation, /) -> None: ...
    def setScanLineDirection(self, direction: PySide6.QtMultimedia.QVideoFrameFormat.Direction, /) -> None: ...
    def setStreamFrameRate(self, rate: float, /) -> None: ...
    def setViewport(self, viewport: PySide6.QtCore.QRect, /) -> None: ...
    def setYCbCrColorSpace(self, colorSpace: PySide6.QtMultimedia.QVideoFrameFormat.YCbCrColorSpace, /) -> None: ...
    def streamFrameRate(self, /) -> float: ...
    def swap(self, other: PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...
    def updateUniformData(self, dst: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, frame: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, transform: PySide6.QtGui.QMatrix4x4 | PySide6.QtGui.QTransform, opacity: float, /) -> None: ...
    def vertexShaderFileName(self, /) -> str: ...
    def viewport(self, /) -> PySide6.QtCore.QRect: ...
    def yCbCrColorSpace(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat.YCbCrColorSpace: ...


class QVideoFrameInput(PySide6.QtCore.QObject):

    readyToSendVideoFrame    : typing.ClassVar[Signal] = ... # readyToSendVideoFrame()

    @typing.overload
    def __init__(self, format: PySide6.QtMultimedia.QVideoFrameFormat, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def format(self, /) -> PySide6.QtMultimedia.QVideoFrameFormat: ...
    def sendVideoFrame(self, frame: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, /) -> bool: ...


class QVideoSink(PySide6.QtCore.QObject):

    subtitleTextChanged      : typing.ClassVar[Signal] = ... # subtitleTextChanged(QString)
    videoFrameChanged        : typing.ClassVar[Signal] = ... # videoFrameChanged(QVideoFrame)
    videoSizeChanged         : typing.ClassVar[Signal] = ... # videoSizeChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, subtitleText: str | None = ..., videoSize: PySide6.QtCore.QSize | None = ...) -> None: ...

    def rhi(self, /) -> PySide6.QtGui.QRhi: ...
    def setRhi(self, rhi: PySide6.QtGui.QRhi, /) -> None: ...
    def setSubtitleText(self, subtitle: str, /) -> None: ...
    def setVideoFrame(self, frame: PySide6.QtMultimedia.QVideoFrame | PySide6.QtMultimedia.QVideoFrameFormat, /) -> None: ...
    def subtitleText(self, /) -> str: ...
    def videoFrame(self, /) -> PySide6.QtMultimedia.QVideoFrame: ...
    def videoSize(self, /) -> PySide6.QtCore.QSize: ...


class QWaveDecoder(PySide6.QtCore.QIODevice):

    formatKnown              : typing.ClassVar[Signal] = ... # formatKnown()
    parsingError             : typing.ClassVar[Signal] = ... # parsingError()

    @typing.overload
    def __init__(self, device: PySide6.QtCore.QIODevice, format: PySide6.QtMultimedia.QAudioFormat, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, device: PySide6.QtCore.QIODevice, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def audioFormat(self, /) -> PySide6.QtMultimedia.QAudioFormat: ...
    def bytesAvailable(self, /) -> int: ...
    def close(self, /) -> None: ...
    def duration(self, /) -> int: ...
    def getDevice(self, /) -> PySide6.QtCore.QIODevice: ...
    @staticmethod
    def headerLength() -> int: ...
    def isSequential(self, /) -> bool: ...
    def open(self, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag, /) -> bool: ...
    def pos(self, /) -> int: ...
    def readData(self, maxlen: int, /) -> object: ...
    def seek(self, pos: int, /) -> bool: ...
    def size(self, /) -> int: ...
    def writeData(self, data: bytes | bytearray | memoryview, len: int, /) -> int: ...


class QWindowCapture(PySide6.QtCore.QObject):

    activeChanged            : typing.ClassVar[Signal] = ... # activeChanged(bool)
    errorChanged             : typing.ClassVar[Signal] = ... # errorChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QWindowCapture::Error,QString)
    windowChanged            : typing.ClassVar[Signal] = ... # windowChanged(QCapturableWindow)

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InternalError             = ...  # 0x1
        CapturingNotSupported     = ...  # 0x2
        CaptureFailed             = ...  # 0x4
        NotFound                  = ...  # 0x5


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, active: bool | None = ..., window: PySide6.QtMultimedia.QCapturableWindow | None = ..., error: PySide6.QtMultimedia.QWindowCapture.Error | None = ..., errorString: str | None = ...) -> None: ...

    @staticmethod
    def capturableWindows() -> typing.List[PySide6.QtMultimedia.QCapturableWindow]: ...
    def captureSession(self, /) -> PySide6.QtMultimedia.QMediaCaptureSession: ...
    def error(self, /) -> PySide6.QtMultimedia.QWindowCapture.Error: ...
    def errorString(self, /) -> str: ...
    def isActive(self, /) -> bool: ...
    def setActive(self, active: bool, /) -> None: ...
    def setWindow(self, window: PySide6.QtMultimedia.QCapturableWindow, /) -> None: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...
    def window(self, /) -> PySide6.QtMultimedia.QCapturableWindow: ...


class QtVideo(Shiboken.Object):

    class Rotation(enum.Enum):

        None_                     = ...  # 0x0
        Clockwise90               = ...  # 0x5a
        Clockwise180              = ...  # 0xb4
        Clockwise270              = ...  # 0x10e


# eof
