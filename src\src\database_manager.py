from PySide6.QtCore import QObject, Signal, Slot, Property
import sqlite3
import os
import sys

class WorkspaceDatabase(QObject):
    workspaceAdded = Signal(str)

    def __init__(self, db_name='factoryeye.db'):
        super(WorkspaceDatabase, self).__init__()
        self._workspace_names = []  # Use a regular Python list
        
        # Determine database path
        if getattr(sys, 'frozen', False):
            # If running as bundled exe, use AppData
            db_directory = os.path.join(os.getenv('APPDATA'), 'FactoryEye', 'data')
            if not os.path.exists(db_directory):
                os.makedirs(db_directory, exist_ok=True)
            db_path = os.path.join(db_directory, db_name)
        else:
            # If running from Python interpreter, use local directory
            db_path = db_name

        self.connection = sqlite3.connect(db_path)
        self.cursor = self.connection.cursor()
        self.create_table()
        self.load_workspaces()
        self.ensure_default_linestyle()

    def create_table(self):
        # Create a table to store workspace data
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_workspace (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                camera_list INTEGER NOT NULL DEFAULT 0,
                camera_setting INTEGER NOT NULL DEFAULT 0,
                camera_ipl INTEGER NOT NULL DEFAULT 0,
                camera_drawing INTEGER NOT NULL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create a table to store cameraspace data
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_cameraspace (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                workspace_id INTEGER NOT NULL,
                camera_name TEXT NOT NULL,
                camera_serial TEXT NOT NULL,
                window_x INTEGER NOT NULL DEFAULT 0,
                window_y INTEGER NOT NULL DEFAULT 0,
                window_w INTEGER NOT NULL DEFAULT 800,
                window_h INTEGER NOT NULL DEFAULT 600,
                zoom REAL NOT NULL DEFAULT 1.0,
                pan_x INTEGER NOT NULL DEFAULT 0,
                pan_y INTEGER NOT NULL DEFAULT 0,
                is_play INTEGER NOT NULL DEFAULT 0
            )
        ''')

        # Create a table to store camera setting data
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_camera_setting (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_serial TEXT NOT NULL,
                frame_rate REAL NOT NULL DEFAULT 24,
                exposure_time REAL NOT NULL DEFAULT 30,
                digital_gain REAL NOT NULL DEFAULT 3,
                analog_gain REAL NOT NULL DEFAULT 5,
                width INTEGER NOT NULL DEFAULT 0,
                height INTEGER NOT NULL DEFAULT 0             
            )
        ''')

        # Create a table to store camera IPL data
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_camera_ipl (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_serial TEXT NOT NULL,
                cameraspace_id INTEGER NOT NULL,
                flip_h INTEGER NOT NULL DEFAULT 0,
                flip_v INTEGER NOT NULL DEFAULT 0,
                rotation INTEGER NOT NULL DEFAULT 0     
            )
        ''')

        # Create a table to store drawing shape data
        # Draw type: 0: pen, 1: line, 2: rectangle, 3: circle, 4: distance, 5: text
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_drawing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cameraspace_id INTEGER NOT NULL,
                number INTEGER NOT NULL,
                json_data TEXT NOT NULL
            )
        ''')
        
        # Create a table to store drawing text data
        #line_sylte: 0: solid, 1: dashed, 2: dotted
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tb_linestyle (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                red REAL NOT NULL DEFAULT 1,
                green REAL NOT NULL DEFAULT 1,
                blue REAL NOT NULL DEFAULT 1,
                alpha REAL NOT NULL DEFAULT 1,
                line_width INTEGER NOT NULL DEFAULT 1, 
                line_style INTEGER NOT NULL DEFAULT 0
            )
        ''')
        
        self.connection.commit()

    def load_workspaces(self):
        self.cursor.execute('SELECT name FROM tb_workspace')
        names = [row[0] for row in self.cursor.fetchall()]
        self._workspace_names = ["No workspace"] + names  # Assign the list of names

    def save_workspace(self, workspace_name):
        self.cursor.execute('''
            INSERT INTO workspace (name)
            VALUES (?)
        ''', (workspace_name))
        self.connection.commit()

    def close(self):
        self.connection.close()

    @Property('QVariantList', constant=True)
    def workspaceNames(self):
        return self._workspace_names
    
    @Slot(str, result='QVariant')
    def get_workspace(self, workspace_name):
        self.cursor.execute('''
            SELECT tb_workspace.*, tb_cameraspace.*
            FROM tb_workspace
            LEFT JOIN tb_cameraspace ON tb_cameraspace.workspace_id = tb_workspace.id
            WHERE tb_workspace.name = ?
        ''', (workspace_name,))
        
        results = self.cursor.fetchall()

        if not results:
            print("No data found for workspace:", workspace_name)
            return QVariant()

        workspace_info = {
            "id": results[0][0],
            "name": results[0][1],
            "camera_list": results[0][2],
            "camera_setting": results[0][3],
            "camera_ipl": results[0][4],
            "camera_drawing": results[0][5],
            "timestamp": results[0][6],
            "cameraspace": []
        }

        for row in results:
            if row[7] is not None:  # Check if there is a cameraspace entry
                cameraspace_info = {
                    "id": row[7],
                    "workspace_id": row[8],
                    "camera_name": row[9],
                    "camera_serial": row[10],
                    "window_x": row[11],
                    "window_y": row[12],
                    "window_w": row[13],
                    "window_h": row[14],
                    "zoom": row[15],
                    "pan_x": row[16],
                    "pan_y": row[17],
                    "is_play": row[18]
                }
                workspace_info["cameraspace"].append(cameraspace_info)

        return workspace_info

    @Slot(str, result=int)
    @Slot(str, int, int, int, int, result=int)
    def add_workspace(self, name, camera_list=0, camera_setting=0, camera_ipl=0, camera_drawing=0):
        self.cursor.execute('INSERT INTO tb_workspace (name, camera_list, camera_setting, camera_ipl, camera_drawing) VALUES (?, ?, ?, ?, ?)', (name, camera_list, camera_setting, camera_ipl, camera_drawing))
        self.connection.commit()
        workspace_id = self.cursor.lastrowid  # Get the ID of the newly inserted workspace
        self.load_workspaces()  # Reload workspaces after adding a new one
        self.workspaceAdded.emit(name)
        return workspace_id

    @Slot(str)
    def remove_workspace(self, workspace_id):
        self.cursor.execute("DELETE FROM tb_workspace WHERE id = ?", (workspace_id,))
        self.connection.commit()
        self.load_workspaces()  # Reload workspaces after removing one
        self.workspaceAdded.emit("No workspace")

    @Slot(int, str, int)
    def set_workspace_subwindow_status(self, workspace_id, subwindow_name, flag):
        # Ensure subwindow_name is a valid column name
        valid_columns = ["camera_list", "camera_setting", "camera_ipl", "camera_drawing"]
        if subwindow_name not in valid_columns:
            raise ValueError(f"Invalid column name: {subwindow_name}")

        # Construct the SQL query with the column name directly
        query = f"UPDATE tb_workspace SET {subwindow_name} = ? WHERE id = ?"
        
        # Execute the query with the values
        self.cursor.execute(query, (flag, workspace_id))
        self.connection.commit()

    ##tb_camera_setting table api
    @Slot(str, result='QVariant')
    def get_camera_settings(self, serial_number):
        try:
            self.cursor.execute('''
                SELECT * FROM tb_camera_setting
                WHERE camera_serial = ?
            ''', (serial_number,))
            
            settings = self.cursor.fetchone()

            if settings:
                camera_setting = {
                    "frame_rate": settings[2],
                    "exposure_time": settings[3],
                    "digital_gain": settings[4],
                    "analog_gain": settings[5],
                    "width": settings[6],
                    "height": settings[7]
                }
                return camera_setting
            else:
                print(f"No settings found for {serial_number}")
                return None
        except sqlite3.Error as e:
            print(f"An error occurred: {e}")
            return None

    @Slot(str, str, float)
    def set_camera_setting(self, serial_number, field_name, new_value):
        # Ensure field_name is a valid column name
        valid_fields = ["frame_rate", "exposure_time", "digital_gain", "analog_gain"]
        if field_name not in valid_fields:
            raise ValueError(f"Invalid field name: {field_name}")

        # Construct the SQL query with the column name directly
        query = f'''
            UPDATE tb_camera_setting
            SET {field_name} = ?
            WHERE camera_serial = ?
        '''

        try:
            self.cursor.execute(query, (new_value, serial_number))
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"An error occurred: {e}")

    @Slot(str, float, float, float, float, int, int)
    def add_camera_setting(self, serial_number, frame_rate, exposure_time, digital_gain, analog_gain, width, height):
        try:
            # Check if camera_serial exists
            self.cursor.execute('''
                SELECT COUNT(*) FROM tb_camera_setting
                WHERE camera_serial = ?
            ''', (serial_number,))
            
            exists = self.cursor.fetchone()[0] > 0
            
            if exists:
                # Update existing settings
                self.cursor.execute('''
                    UPDATE tb_camera_setting 
                    SET frame_rate = ?, exposure_time = ?, digital_gain = ?, 
                        analog_gain = ?, width = ?, height = ?
                    WHERE camera_serial = ?
                ''', (frame_rate, exposure_time, digital_gain, analog_gain, width, height, serial_number))
            else:
                # Insert new settings
                self.cursor.execute('''
                    INSERT INTO tb_camera_setting (camera_serial, frame_rate, exposure_time, digital_gain, analog_gain, width, height)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (serial_number, frame_rate, exposure_time, digital_gain, analog_gain, width, height,))
                
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"An error occurred while managing camera setting: {e}")

    ##tb_cameraspace table api
    @Slot(str, str, int, int, int, int, int, result='QVariantMap')
    def add_cameraspace(self, camera_name, camera_serial, workspace_id, x, y, w, h):
        try:
            # Insert with default values for window position and size
            self.cursor.execute('''
                INSERT INTO tb_cameraspace (
                    workspace_id, camera_name, camera_serial,
                    window_x, window_y, window_w, window_h
                )
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (workspace_id, camera_name, camera_serial, x, y, w, h))
            self.connection.commit()
            
            # Get the inserted row
            cameraspace_id = self.cursor.lastrowid
            self.cursor.execute('''
                SELECT id, workspace_id, camera_name, camera_serial,
                       window_x, window_y, window_w, window_h,
                       zoom, pan_x, pan_y, is_play
                FROM tb_cameraspace
                WHERE id = ?
            ''', (cameraspace_id,))
            
            row = self.cursor.fetchone()
            if row:
                return {
                    "id": row[0],
                    "workspace_id": row[1],
                    "camera_name": row[2],
                    "camera_serial": row[3],
                    "window_x": row[4],
                    "window_y": row[5],
                    "window_w": row[6],
                    "window_h": row[7],
                    "zoom": row[8],
                    "pan_x": row[9],
                    "pan_y": row[10],
                    "is_play": row[11]
                }
            return None
        except sqlite3.Error as e:
            print(f"An error occurred while adding cameraspace: {e}")
            return None
        
    @Slot(int)
    def remove_cameraspace(self, cameraspace_id):
        try:
            # Delete associated drawings first
            self.cursor.execute('''
                DELETE FROM tb_drawing 
                WHERE cameraspace_id = ?
            ''', (cameraspace_id,))
            
            # Then delete the cameraspace
            self.cursor.execute('''
                DELETE FROM tb_cameraspace 
                WHERE id = ?
            ''', (cameraspace_id,))

            # Then delete the IPL setting
            self.cursor.execute('''
                DELETE FROM tb_camera_ipl
                WHERE cameraspace_id = ?
            ''', (cameraspace_id,))
            
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"An error occurred while removing cameraspace: {e}")

    @Slot(int, result='QVariantList')
    def get_cameraspace_list(self, workspace_id):
        try:
            self.cursor.execute('''
                SELECT id FROM tb_cameraspace
                WHERE workspace_id = ?
            ''', (workspace_id,))
            cameraspace_list = [row[0] for row in self.cursor.fetchall()]
            return cameraspace_list
        except sqlite3.Error as e:
            print(f"An error occurred while getting cameraspace list: {e}")
            return []
        
    @Slot(int, str, 'QVariant')
    def set_cameraspace_field(self, cameraspace_id, field_name, new_value):
        """Update a single field in tb_cameraspace table
        
        Args:
            cameraspace_id (int): The ID of the cameraspace to update
            field_name (str): Name of the field to update
            new_value (QVariant): New value for the field
        """
        # Ensure field_name is a valid column name
        valid_fields = [
            "workspace_id", "camera_name", "camera_serial",
            "window_x", "window_y", "window_w", "window_h",
            "zoom", "pan_x", "pan_y", "is_play"
        ]
        
        if field_name not in valid_fields:
            raise ValueError(f"Invalid field name: {field_name}")

        # Construct the SQL query with the column name directly
        query = f'''
            UPDATE tb_cameraspace
            SET {field_name} = ?
            WHERE id = ?
        '''

        try:
            self.cursor.execute(query, (new_value, cameraspace_id))
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"An error occurred while updating cameraspace field: {e}")

    @Slot(int, result='QVariantMap')
    def get_cameraspace(self, cameraspace_id):
        try:
            self.cursor.execute('''
                SELECT *
                FROM tb_cameraspace 
                WHERE id = ?
            ''', (cameraspace_id,))
            
            row = self.cursor.fetchone()
            if row:
                return {
                    "id": row[0],
                    "workspace_id": row[1],
                    "camera_name": row[2],
                    "camera_serial": row[3],
                    "window_x": row[4],
                    "window_y": row[5],
                    "window_w": row[6],
                    "window_h": row[7],
                    "zoom": row[8],
                    "pan_x": row[9],
                    "pan_y": row[10],
                    "is_play": row[11]
                }
            return None
        except sqlite3.Error as e:
            print(f"Error getting cameraspace: {e}")
            return None

    ##tb_linestyle table api
    def ensure_default_linestyle(self):
        # Check if the table is empty
        self.cursor.execute('SELECT COUNT(*) FROM tb_linestyle')
        count = self.cursor.fetchone()[0]
        if count == 0:
            # Insert a default row if the table is empty
            self.cursor.execute('''
                INSERT INTO tb_linestyle (red, green, blue, alpha, line_width, line_style)
                VALUES (1, 1, 1, 1, 1.0, 0)
            ''')
            self.connection.commit()

    @Slot(result='QVariant')
    def get_linestyle(self):
        try:
            self.cursor.execute('''
                SELECT * FROM tb_linestyle
            ''')
            linestyle = self.cursor.fetchone()
            linestyle_dict = {
                "red": linestyle[1],
                "green": linestyle[2],
                "blue": linestyle[3],
                "alpha": linestyle[4],
                "line_width": linestyle[5],
                "line_style": linestyle[6]
            }
            return linestyle_dict
        except sqlite3.Error as e:
            print(f"An error occurred while getting linestyle: {e}")
            return None
        
    @Slot(str, float)
    def set_linestyle(self, field_name, new_value):
        # Ensure field_name is a valid column name
        valid_fields = ["red", "green", "blue", "alpha", "line_width", "line_style"]
        if field_name not in valid_fields:
            raise ValueError(f"Invalid field name: {field_name}")

        # Construct the SQL query with the column name directly 
        query = f'''
            UPDATE tb_linestyle
            SET {field_name} = ?
        '''

        try:
            self.cursor.execute(query, (new_value,))
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"An error occurred while updating linestyle: {e}")

    ##tb_drawing table api
    # Add function to save drawing data to database
    @Slot(int, str)
    def save_drawing(self, cameraspace_id, json_data):
        try:
            # Get the highest drawing number for this cameraspace
            self.cursor.execute('''
                SELECT MAX(number) FROM tb_drawing
                WHERE cameraspace_id = ?
            ''', (cameraspace_id,))
            
            max_number = self.cursor.fetchone()[0]
            new_number = 1 if max_number is None else max_number + 1
            
            
            # Insert the drawing data
            self.cursor.execute('''
                INSERT INTO tb_drawing (cameraspace_id, number, json_data)
                VALUES (?, ?, ?)
            ''', (cameraspace_id, new_number, json_data))
            
            self.connection.commit()
            return True
        except Exception as e:
            print(f"Error saving drawing to database: {e}")
            return False

    @Slot(int, result='QVariantList')
    def get_drawings(self, cameraspace_id):
        """
        Retrieve all drawings for a specific cameraspace
        Returns a list of dictionaries containing drawing data
        """
        try:
            self.cursor.execute('''
                SELECT number, json_data FROM tb_drawing
                WHERE cameraspace_id = ?
                ORDER BY number ASC
            ''', (cameraspace_id,))
            
            drawings = self.cursor.fetchall()
            drawing_list = []
            
            for drawing in drawings:
                drawing_dict = {
                    "number": drawing[0],
                    "json_data": drawing[1]
                }
                drawing_list.append(drawing_dict)
                
            return drawing_list
        except sqlite3.Error as e:
            print(f"Error retrieving drawings from database: {e}")
            return []

    @Slot(int)
    def remove_last_drawing(self, cameraspace_id):
        """Remove the last drawing for a specific cameraspace"""
        try:
            self.cursor.execute('''
                DELETE FROM tb_drawing 
                WHERE cameraspace_id = ? 
                AND number = (
                    SELECT MAX(number) 
                    FROM tb_drawing 
                    WHERE cameraspace_id = ?
                )
            ''', (cameraspace_id, cameraspace_id))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error removing last drawing: {e}")
            return False

    @Slot(int)
    def clear_drawings(self, cameraspace_id):
        """Clear all drawings for a specific cameraspace"""
        try:
            self.cursor.execute('''
                DELETE FROM tb_drawing 
                WHERE cameraspace_id = ?
            ''', (cameraspace_id,))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error clearing drawings: {e}")
            return False

    @Slot(int, int, str)
    def update_drawing(self, cameraspace_id, number, json_data):
        """Update a specific drawing in the database
        
        Args:
            cameraspace_id (int): The ID of the cameraspace
            number (int): The drawing number to update
            json_data (str): The new drawing data in JSON format
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.cursor.execute('''
                UPDATE tb_drawing 
                SET json_data = ?
                WHERE cameraspace_id = ? AND number = ?
            ''', (json_data, cameraspace_id, number))
            
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating drawing in database: {e}")
            return False

    ##tb_camera_ipl table api
    @Slot(int, str, str, int)
    def set_camera_ipl_status(self, cameraspace_id, camera_serial, field_name, value):
        try:
            # First check if entry exists
            self.cursor.execute('''
                SELECT COUNT(*) FROM tb_camera_ipl
                WHERE cameraspace_id = ?
            ''', (cameraspace_id,))
            
            exists = self.cursor.fetchone()[0] > 0
            
            if exists:
                # Update existing entry
                query = f'''
                    UPDATE tb_camera_ipl
                    SET {field_name} = ?
                    WHERE cameraspace_id = ?
                '''
                self.cursor.execute(query, (value, cameraspace_id))
            else:
                # Create new entry with default values
                self.cursor.execute('''
                    INSERT INTO tb_camera_ipl (cameraspace_id, camera_serial, flip_h, flip_v, rotation)
                    VALUES (?, ?, 0, 0, 0)
                ''', (cameraspace_id, camera_serial,))
                
                # Then update the specific field
                query = f'''
                    UPDATE tb_camera_ipl
                    SET {field_name} = ?
                    WHERE cameraspace_id = ?
                '''
                self.cursor.execute(query, (value, cameraspace_id))
            
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"Error setting camera IPL status: {e}")

    @Slot(str, int, result='QVariantMap')
    def get_camera_ipl_settings(self, camera_serial, cameraspace_id):
        try:
            self.cursor.execute('''
                SELECT flip_h, flip_v, rotation
                FROM tb_camera_ipl
                WHERE camera_serial = ? AND cameraspace_id = ?
            ''', (camera_serial, cameraspace_id,))
            
            row = self.cursor.fetchone()
            if row:
                return {
                    "flip_h": row[0],
                    "flip_v": row[1],
                    "rotation": row[2]
                }

            return {
                "flip_h": 0,
                "flip_v": 0,
                "rotation": 0
            }
        except sqlite3.Error as e:
            print(f"Error getting camera IPL settings: {e}")
            return None
