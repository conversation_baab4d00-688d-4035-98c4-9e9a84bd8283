VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [StringStruct(u'CompanyName', u'Ternpack'),
          StringStruct(u'FileDescription', u'Industrial Camera Management Tool'),
          StringStruct(u'FileVersion', u'1.0.0'),
          StringStruct(u'InternalName', u'FactoryEye'),
          StringStruct(u'LegalCopyright', u'Copyright © 2024'),
          StringStruct(u'OriginalFilename', u'FactoryEye.exe'),
          StringStruct(u'ProductName', u'FactoryEye'),
          StringStruct(u'ProductVersion', u'1.0.0')])
      ])
  ]
)