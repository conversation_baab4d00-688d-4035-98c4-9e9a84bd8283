import QtQuick 2.15
import QtQuick.Controls 2.15

Window {
    id: workspaceDialog
    title: "Create Workspace"
    width: 220
    height: 85
    minimumWidth: 220
    minimumHeight: 85
    visible: false
    flags: Qt.Dialog
    color: "darkgray"

    property string workspaceName: ""

    onVisibleChanged: {
        if (visible) {
            workspaceNameInput.forceActiveFocus();
            workspaceNameInput.selectAll();
        }
    }

    function acceptDialog() {
        if (workspaceNameInput.text !== "") {
            if (!workspaceDatabase.workspaceNames.includes(workspaceNameInput.text)) {
                workspaceDatabase.add_workspace(workspaceNameInput.text);
            }
            workspaceDialog.close();
        } else {
            console.log("Workspace name cannot be empty");
        }
    }

    Column {
        spacing: 10
        padding: 20

        TextField {
            id: workspaceNameInput
            placeholderText: "Enter Workspace Name"
            anchors.horizontalCenter: parent.horizontalCenter
            width: 180
            text: workspaceName
            onTextChanged: {
                workspaceName = text;
            }
            Keys.onReturnPressed: acceptDialog()
            Keys.onEnterPressed: acceptDialog()
        }

        Row {
            spacing: 10
            anchors.horizontalCenter: parent.horizontalCenter
            Button {
                width: 86
                text: "OK"
                onClicked: acceptDialog()
            }
            Button {
                width: 86
                text: "Cancel"
                onClicked: {
                    workspaceDialog.close();
                }
            }
        }
    }
}
