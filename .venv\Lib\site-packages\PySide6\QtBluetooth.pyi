# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtBluetooth, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtBluetooth`

import PySide6.QtBluetooth
import PySide6.QtCore

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QBluetooth(Shiboken.Object):

    class AttAccessConstraint(enum.Flag):

        AttAuthorizationRequired  = ...  # 0x1
        AttAuthenticationRequired = ...  # 0x2
        AttEncryptionRequired     = ...  # 0x4

    class Security(enum.Flag):

        NoSecurity                = ...  # 0x0
        Authorization             = ...  # 0x1
        Authentication            = ...  # 0x2
        Encryption                = ...  # 0x4
        Secure                    = ...  # 0x8


class QBluetoothAddress(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QBluetoothAddress, /) -> None: ...
    @typing.overload
    def __init__(self, address: str, /) -> None: ...
    @typing.overload
    def __init__(self, address: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QBluetoothAddress, /) -> bool: ...
    def __hash__(self, /) -> int: ...
    def __lt__(self, b: PySide6.QtBluetooth.QBluetoothAddress, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QBluetoothAddress, /) -> bool: ...
    def __repr__(self, /) -> str: ...
    def clear(self, /) -> None: ...
    def isNull(self, /) -> bool: ...
    def toString(self, /) -> str: ...
    def toUInt64(self, /) -> int: ...


class QBluetoothDeviceDiscoveryAgent(PySide6.QtCore.QObject):

    canceled                 : typing.ClassVar[Signal] = ... # canceled()
    deviceDiscovered         : typing.ClassVar[Signal] = ... # deviceDiscovered(QBluetoothDeviceInfo)
    deviceUpdated            : typing.ClassVar[Signal] = ... # deviceUpdated(QBluetoothDeviceInfo,QBluetoothDeviceInfo::Fields)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QBluetoothDeviceDiscoveryAgent::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()

    class DiscoveryMethod(enum.Flag):

        NoMethod                  = ...  # 0x0
        ClassicMethod             = ...  # 0x1
        LowEnergyMethod           = ...  # 0x2

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InputOutputError          = ...  # 0x1
        PoweredOffError           = ...  # 0x2
        InvalidBluetoothAdapterError = ...  # 0x3
        UnsupportedPlatformError  = ...  # 0x4
        UnsupportedDiscoveryMethod = ...  # 0x5
        LocationServiceTurnedOffError = ...  # 0x6
        MissingPermissionsError   = ...  # 0x7
        UnknownError              = ...  # 0x64


    @typing.overload
    def __init__(self, deviceAdapter: PySide6.QtBluetooth.QBluetoothAddress, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def discoveredDevices(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothDeviceInfo]: ...
    def error(self, /) -> PySide6.QtBluetooth.QBluetoothDeviceDiscoveryAgent.Error: ...
    def errorString(self, /) -> str: ...
    def isActive(self, /) -> bool: ...
    def lowEnergyDiscoveryTimeout(self, /) -> int: ...
    def setLowEnergyDiscoveryTimeout(self, msTimeout: int, /) -> None: ...
    @typing.overload
    def start(self, /) -> None: ...
    @typing.overload
    def start(self, method: PySide6.QtBluetooth.QBluetoothDeviceDiscoveryAgent.DiscoveryMethod, /) -> None: ...
    def stop(self, /) -> None: ...
    @staticmethod
    def supportedDiscoveryMethods() -> PySide6.QtBluetooth.QBluetoothDeviceDiscoveryAgent.DiscoveryMethod: ...


class QBluetoothDeviceInfo(Shiboken.Object):

    class CoreConfiguration(enum.Flag):

        UnknownCoreConfiguration  = ...  # 0x0
        LowEnergyCoreConfiguration = ...  # 0x1
        BaseRateCoreConfiguration = ...  # 0x2
        BaseRateAndLowEnergyCoreConfiguration = ...  # 0x3

    class Field(enum.Flag):

        None_                     = ...  # 0x0
        RSSI                      = ...  # 0x1
        ManufacturerData          = ...  # 0x2
        ServiceData               = ...  # 0x4
        All                       = ...  # 0x7fff

    class MajorDeviceClass(enum.Enum):

        MiscellaneousDevice       = ...  # 0x0
        ComputerDevice            = ...  # 0x1
        PhoneDevice               = ...  # 0x2
        NetworkDevice             = ...  # 0x3
        AudioVideoDevice          = ...  # 0x4
        PeripheralDevice          = ...  # 0x5
        ImagingDevice             = ...  # 0x6
        WearableDevice            = ...  # 0x7
        ToyDevice                 = ...  # 0x8
        HealthDevice              = ...  # 0x9
        UncategorizedDevice       = ...  # 0x1f

    class MinorAudioVideoClass(enum.Enum):

        UncategorizedAudioVideoDevice = ...  # 0x0
        WearableHeadsetDevice     = ...  # 0x1
        HandsFreeDevice           = ...  # 0x2
        Microphone                = ...  # 0x4
        Loudspeaker               = ...  # 0x5
        Headphones                = ...  # 0x6
        PortableAudioDevice       = ...  # 0x7
        CarAudio                  = ...  # 0x8
        SetTopBox                 = ...  # 0x9
        HiFiAudioDevice           = ...  # 0xa
        Vcr                       = ...  # 0xb
        VideoCamera               = ...  # 0xc
        Camcorder                 = ...  # 0xd
        VideoMonitor              = ...  # 0xe
        VideoDisplayAndLoudspeaker = ...  # 0xf
        VideoConferencing         = ...  # 0x10
        GamingDevice              = ...  # 0x12

    class MinorComputerClass(enum.Enum):

        UncategorizedComputer     = ...  # 0x0
        DesktopComputer           = ...  # 0x1
        ServerComputer            = ...  # 0x2
        LaptopComputer            = ...  # 0x3
        HandheldClamShellComputer = ...  # 0x4
        HandheldComputer          = ...  # 0x5
        WearableComputer          = ...  # 0x6

    class MinorHealthClass(enum.Enum):

        UncategorizedHealthDevice = ...  # 0x0
        HealthBloodPressureMonitor = ...  # 0x1
        HealthThermometer         = ...  # 0x2
        HealthWeightScale         = ...  # 0x3
        HealthGlucoseMeter        = ...  # 0x4
        HealthPulseOximeter       = ...  # 0x5
        HealthDataDisplay         = ...  # 0x7
        HealthStepCounter         = ...  # 0x8

    class MinorImagingClass(enum.Enum):

        UncategorizedImagingDevice = ...  # 0x0
        ImageDisplay              = ...  # 0x4
        ImageCamera               = ...  # 0x8
        ImageScanner              = ...  # 0x10
        ImagePrinter              = ...  # 0x20

    class MinorMiscellaneousClass(enum.Enum):

        UncategorizedMiscellaneous = ...  # 0x0

    class MinorNetworkClass(enum.Enum):

        NetworkFullService        = ...  # 0x0
        NetworkLoadFactorOne      = ...  # 0x8
        NetworkLoadFactorTwo      = ...  # 0x10
        NetworkLoadFactorThree    = ...  # 0x18
        NetworkLoadFactorFour     = ...  # 0x20
        NetworkLoadFactorFive     = ...  # 0x28
        NetworkLoadFactorSix      = ...  # 0x30
        NetworkNoService          = ...  # 0x38

    class MinorPeripheralClass(enum.Enum):

        UncategorizedPeripheral   = ...  # 0x0
        JoystickPeripheral        = ...  # 0x1
        GamepadPeripheral         = ...  # 0x2
        RemoteControlPeripheral   = ...  # 0x3
        SensingDevicePeripheral   = ...  # 0x4
        DigitizerTabletPeripheral = ...  # 0x5
        CardReaderPeripheral      = ...  # 0x6
        KeyboardPeripheral        = ...  # 0x10
        PointingDevicePeripheral  = ...  # 0x20
        KeyboardWithPointingDevicePeripheral = ...  # 0x30

    class MinorPhoneClass(enum.Enum):

        UncategorizedPhone        = ...  # 0x0
        CellularPhone             = ...  # 0x1
        CordlessPhone             = ...  # 0x2
        SmartPhone                = ...  # 0x3
        WiredModemOrVoiceGatewayPhone = ...  # 0x4
        CommonIsdnAccessPhone     = ...  # 0x5

    class MinorToyClass(enum.Enum):

        UncategorizedToy          = ...  # 0x0
        ToyRobot                  = ...  # 0x1
        ToyVehicle                = ...  # 0x2
        ToyDoll                   = ...  # 0x3
        ToyController             = ...  # 0x4
        ToyGame                   = ...  # 0x5

    class MinorWearableClass(enum.Enum):

        UncategorizedWearableDevice = ...  # 0x0
        WearableWristWatch        = ...  # 0x1
        WearablePager             = ...  # 0x2
        WearableJacket            = ...  # 0x3
        WearableHelmet            = ...  # 0x4
        WearableGlasses           = ...  # 0x5

    class ServiceClass(enum.Flag):

        NoService                 = ...  # 0x0
        PositioningService        = ...  # 0x1
        NetworkingService         = ...  # 0x2
        RenderingService          = ...  # 0x4
        CapturingService          = ...  # 0x8
        ObjectTransferService     = ...  # 0x10
        AudioService              = ...  # 0x20
        TelephonyService          = ...  # 0x40
        InformationService        = ...  # 0x80
        AllServices               = ...  # 0x7ff


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, address: PySide6.QtBluetooth.QBluetoothAddress, name: str, classOfDevice: int, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QBluetoothDeviceInfo, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, name: str, classOfDevice: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QBluetoothDeviceInfo, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QBluetoothDeviceInfo, /) -> bool: ...
    def address(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def coreConfigurations(self, /) -> PySide6.QtBluetooth.QBluetoothDeviceInfo.CoreConfiguration: ...
    def deviceUuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def isCached(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def majorDeviceClass(self, /) -> PySide6.QtBluetooth.QBluetoothDeviceInfo.MajorDeviceClass: ...
    @typing.overload
    def manufacturerData(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    @typing.overload
    def manufacturerData(self, manufacturerId: int, /) -> PySide6.QtCore.QByteArray: ...
    def manufacturerIds(self, /) -> typing.List[int]: ...
    def minorDeviceClass(self, /) -> int: ...
    def name(self, /) -> str: ...
    def rssi(self, /) -> int: ...
    def serviceClasses(self, /) -> PySide6.QtBluetooth.QBluetoothDeviceInfo.ServiceClass: ...
    @typing.overload
    def serviceData(self, /) -> typing.Dict[PySide6.QtBluetooth.QBluetoothUuid, PySide6.QtCore.QByteArray]: ...
    @typing.overload
    def serviceData(self, serviceId: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> PySide6.QtCore.QByteArray: ...
    def serviceIds(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def serviceUuids(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def setCached(self, cached: bool, /) -> None: ...
    def setCoreConfigurations(self, coreConfigs: PySide6.QtBluetooth.QBluetoothDeviceInfo.CoreConfiguration, /) -> None: ...
    def setDeviceUuid(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def setManufacturerData(self, manufacturerId: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def setName(self, name: str, /) -> None: ...
    def setRssi(self, signal: int, /) -> None: ...
    def setServiceData(self, serviceId: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def setServiceUuids(self, uuids: collections.abc.Sequence[PySide6.QtBluetooth.QBluetoothUuid], /) -> None: ...


class QBluetoothHostInfo(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QBluetoothHostInfo, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QBluetoothHostInfo, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QBluetoothHostInfo, /) -> bool: ...
    def address(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def name(self, /) -> str: ...
    def setAddress(self, address: PySide6.QtBluetooth.QBluetoothAddress, /) -> None: ...
    def setName(self, name: str, /) -> None: ...


class QBluetoothLocalDevice(PySide6.QtCore.QObject):

    deviceConnected          : typing.ClassVar[Signal] = ... # deviceConnected(QBluetoothAddress)
    deviceDisconnected       : typing.ClassVar[Signal] = ... # deviceDisconnected(QBluetoothAddress)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QBluetoothLocalDevice::Error)
    hostModeStateChanged     : typing.ClassVar[Signal] = ... # hostModeStateChanged(QBluetoothLocalDevice::HostMode)
    pairingFinished          : typing.ClassVar[Signal] = ... # pairingFinished(QBluetoothAddress,QBluetoothLocalDevice::Pairing)

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        PairingError              = ...  # 0x1
        MissingPermissionsError   = ...  # 0x2
        UnknownError              = ...  # 0x64

    class HostMode(enum.Enum):

        HostPoweredOff            = ...  # 0x0
        HostConnectable           = ...  # 0x1
        HostDiscoverable          = ...  # 0x2
        HostDiscoverableLimitedInquiry = ...  # 0x3

    class Pairing(enum.Enum):

        Unpaired                  = ...  # 0x0
        Paired                    = ...  # 0x1
        AuthorizedPaired          = ...  # 0x2


    @typing.overload
    def __init__(self, address: PySide6.QtBluetooth.QBluetoothAddress, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def address(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    @staticmethod
    def allDevices() -> typing.List[PySide6.QtBluetooth.QBluetoothHostInfo]: ...
    def connectedDevices(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothAddress]: ...
    def hostMode(self, /) -> PySide6.QtBluetooth.QBluetoothLocalDevice.HostMode: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def pairingStatus(self, address: PySide6.QtBluetooth.QBluetoothAddress, /) -> PySide6.QtBluetooth.QBluetoothLocalDevice.Pairing: ...
    def powerOn(self, /) -> None: ...
    def requestPairing(self, address: PySide6.QtBluetooth.QBluetoothAddress, pairing: PySide6.QtBluetooth.QBluetoothLocalDevice.Pairing, /) -> None: ...
    def setHostMode(self, mode: PySide6.QtBluetooth.QBluetoothLocalDevice.HostMode, /) -> None: ...


class QBluetoothServer(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QBluetoothServer::Error)
    newConnection            : typing.ClassVar[Signal] = ... # newConnection()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        UnknownError              = ...  # 0x1
        PoweredOffError           = ...  # 0x2
        InputOutputError          = ...  # 0x3
        ServiceAlreadyRegisteredError = ...  # 0x4
        UnsupportedProtocolError  = ...  # 0x5
        MissingPermissionsError   = ...  # 0x6


    def __init__(self, serverType: PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def error(self, /) -> PySide6.QtBluetooth.QBluetoothServer.Error: ...
    def hasPendingConnections(self, /) -> bool: ...
    def isListening(self, /) -> bool: ...
    @typing.overload
    def listen(self, /, address: PySide6.QtBluetooth.QBluetoothAddress = ..., port: int | None = ...) -> bool: ...
    @typing.overload
    def listen(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /, serviceName: str = ...) -> PySide6.QtBluetooth.QBluetoothServiceInfo: ...
    def maxPendingConnections(self, /) -> int: ...
    def nextPendingConnection(self, /) -> PySide6.QtBluetooth.QBluetoothSocket: ...
    def securityFlags(self, /) -> PySide6.QtBluetooth.QBluetooth.Security: ...
    def serverAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def serverPort(self, /) -> int: ...
    def serverType(self, /) -> PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol: ...
    def setMaxPendingConnections(self, numConnections: int, /) -> None: ...
    def setSecurityFlags(self, security: PySide6.QtBluetooth.QBluetooth.Security, /) -> None: ...


class QBluetoothServiceDiscoveryAgent(PySide6.QtCore.QObject):

    canceled                 : typing.ClassVar[Signal] = ... # canceled()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QBluetoothServiceDiscoveryAgent::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()
    serviceDiscovered        : typing.ClassVar[Signal] = ... # serviceDiscovered(QBluetoothServiceInfo)

    class DiscoveryMode(enum.Enum):

        MinimalDiscovery          = ...  # 0x0
        FullDiscovery             = ...  # 0x1

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        InputOutputError          = ...  # 0x1
        PoweredOffError           = ...  # 0x2
        InvalidBluetoothAdapterError = ...  # 0x3
        MissingPermissionsError   = ...  # 0x7
        UnknownError              = ...  # 0x64


    @typing.overload
    def __init__(self, deviceAdapter: PySide6.QtBluetooth.QBluetoothAddress, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def clear(self, /) -> None: ...
    def discoveredServices(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothServiceInfo]: ...
    def error(self, /) -> PySide6.QtBluetooth.QBluetoothServiceDiscoveryAgent.Error: ...
    def errorString(self, /) -> str: ...
    def isActive(self, /) -> bool: ...
    def remoteAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def setRemoteAddress(self, address: PySide6.QtBluetooth.QBluetoothAddress, /) -> bool: ...
    @typing.overload
    def setUuidFilter(self, uuids: collections.abc.Sequence[PySide6.QtBluetooth.QBluetoothUuid], /) -> None: ...
    @typing.overload
    def setUuidFilter(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def start(self, /, mode: PySide6.QtBluetooth.QBluetoothServiceDiscoveryAgent.DiscoveryMode = ...) -> None: ...
    def stop(self, /) -> None: ...
    def uuidFilter(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...


class QBluetoothServiceInfo(Shiboken.Object):

    class Alternative(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Alternative: PySide6.QtBluetooth.QBluetoothServiceInfo.Alternative, /) -> None: ...
        @typing.overload
        def __init__(self, list: collections.abc.Sequence[typing.Any], /) -> None: ...

        def __add__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def __copy__(self, /) -> typing.Self: ...
        def __iadd__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def __lshift__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        @typing.overload
        def append(self, l: collections.abc.Sequence[typing.Any], /) -> None: ...
        @typing.overload
        def append(self, arg__1: typing.Any, /) -> None: ...
        def at(self, i: int, /) -> typing.Any: ...
        def back(self, /) -> typing.Any: ...
        def capacity(self, /) -> int: ...
        def clear(self, /) -> None: ...
        def constData(self, /) -> object: ...
        def constFirst(self, /) -> typing.Any: ...
        def constLast(self, /) -> typing.Any: ...
        def count(self, /) -> int: ...
        def data(self, /) -> object: ...
        def empty(self, /) -> bool: ...
        @typing.overload
        def first(self, /) -> typing.Any: ...
        @typing.overload
        def first(self, n: int, /) -> typing.List[typing.Any]: ...
        @staticmethod
        def fromList(list: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        @staticmethod
        def fromVector(vector: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def front(self, /) -> typing.Any: ...
        def insert(self, arg__1: int, arg__2: typing.Any, /) -> None: ...
        def isEmpty(self, /) -> bool: ...
        def isSharedWith(self, other: collections.abc.Sequence[typing.Any], /) -> bool: ...
        @typing.overload
        def last(self, /) -> typing.Any: ...
        @typing.overload
        def last(self, n: int, /) -> typing.List[typing.Any]: ...
        def length(self, /) -> int: ...
        @staticmethod
        def maxSize() -> int: ...
        def max_size(self, /) -> int: ...
        def mid(self, pos: int, /, len: int = ...) -> typing.List[typing.Any]: ...
        def move(self, from_: int, to: int, /) -> None: ...
        def pop_back(self, /) -> None: ...
        def pop_front(self, /) -> None: ...
        def prepend(self, arg__1: typing.Any, /) -> None: ...
        def push_back(self, arg__1: typing.Any, /) -> None: ...
        def push_front(self, arg__1: typing.Any, /) -> None: ...
        def remove(self, i: int, /, n: int = ...) -> None: ...
        def removeAll(self, arg__1: typing.Any, /) -> None: ...
        def removeAt(self, i: int, /) -> None: ...
        def removeFirst(self, /) -> None: ...
        def removeLast(self, /) -> None: ...
        def removeOne(self, arg__1: typing.Any, /) -> None: ...
        def reserve(self, size: int, /) -> None: ...
        def resize(self, size: int, /) -> None: ...
        def resizeForOverwrite(self, size: int, /) -> None: ...
        def shrink_to_fit(self, /) -> None: ...
        def size(self, /) -> int: ...
        @typing.overload
        def sliced(self, pos: int, /) -> typing.List[typing.Any]: ...
        @typing.overload
        def sliced(self, pos: int, n: int, /) -> typing.List[typing.Any]: ...
        def squeeze(self, /) -> None: ...
        def swap(self, other: collections.abc.Sequence[typing.Any], /) -> None: ...
        def swapItemsAt(self, i: int, j: int, /) -> None: ...
        def takeAt(self, i: int, /) -> typing.Any: ...
        def toList(self, /) -> typing.List[typing.Any]: ...
        def toVector(self, /) -> typing.List[typing.Any]: ...
        def value(self, i: int, /) -> typing.Any: ...

    class AttributeId(enum.Enum):

        ServiceRecordHandle       = ...  # 0x0
        ServiceClassIds           = ...  # 0x1
        ServiceRecordState        = ...  # 0x2
        ServiceId                 = ...  # 0x3
        ProtocolDescriptorList    = ...  # 0x4
        BrowseGroupList           = ...  # 0x5
        LanguageBaseAttributeIdList = ...  # 0x6
        ServiceInfoTimeToLive     = ...  # 0x7
        ServiceAvailability       = ...  # 0x8
        BluetoothProfileDescriptorList = ...  # 0x9
        DocumentationUrl          = ...  # 0xa
        ClientExecutableUrl       = ...  # 0xb
        IconUrl                   = ...  # 0xc
        AdditionalProtocolDescriptorList = ...  # 0xd
        PrimaryLanguageBase       = ...  # 0x100
        ServiceName               = ...  # 0x100
        ServiceDescription        = ...  # 0x101
        ServiceProvider           = ...  # 0x102

    class Protocol(enum.Enum):

        UnknownProtocol           = ...  # 0x0
        L2capProtocol             = ...  # 0x1
        RfcommProtocol            = ...  # 0x2

    class Sequence(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Sequence: PySide6.QtBluetooth.QBluetoothServiceInfo.Sequence, /) -> None: ...
        @typing.overload
        def __init__(self, list: collections.abc.Sequence[typing.Any], /) -> None: ...

        def __add__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def __copy__(self, /) -> typing.Self: ...
        def __iadd__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def __lshift__(self, l: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        @typing.overload
        def append(self, l: collections.abc.Sequence[typing.Any], /) -> None: ...
        @typing.overload
        def append(self, arg__1: typing.Any, /) -> None: ...
        def at(self, i: int, /) -> typing.Any: ...
        def back(self, /) -> typing.Any: ...
        def capacity(self, /) -> int: ...
        def clear(self, /) -> None: ...
        def constData(self, /) -> object: ...
        def constFirst(self, /) -> typing.Any: ...
        def constLast(self, /) -> typing.Any: ...
        def count(self, /) -> int: ...
        def data(self, /) -> object: ...
        def empty(self, /) -> bool: ...
        @typing.overload
        def first(self, /) -> typing.Any: ...
        @typing.overload
        def first(self, n: int, /) -> typing.List[typing.Any]: ...
        @staticmethod
        def fromList(list: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        @staticmethod
        def fromVector(vector: collections.abc.Sequence[typing.Any], /) -> typing.List[typing.Any]: ...
        def front(self, /) -> typing.Any: ...
        def insert(self, arg__1: int, arg__2: typing.Any, /) -> None: ...
        def isEmpty(self, /) -> bool: ...
        def isSharedWith(self, other: collections.abc.Sequence[typing.Any], /) -> bool: ...
        @typing.overload
        def last(self, /) -> typing.Any: ...
        @typing.overload
        def last(self, n: int, /) -> typing.List[typing.Any]: ...
        def length(self, /) -> int: ...
        @staticmethod
        def maxSize() -> int: ...
        def max_size(self, /) -> int: ...
        def mid(self, pos: int, /, len: int = ...) -> typing.List[typing.Any]: ...
        def move(self, from_: int, to: int, /) -> None: ...
        def pop_back(self, /) -> None: ...
        def pop_front(self, /) -> None: ...
        def prepend(self, arg__1: typing.Any, /) -> None: ...
        def push_back(self, arg__1: typing.Any, /) -> None: ...
        def push_front(self, arg__1: typing.Any, /) -> None: ...
        def remove(self, i: int, /, n: int = ...) -> None: ...
        def removeAll(self, arg__1: typing.Any, /) -> None: ...
        def removeAt(self, i: int, /) -> None: ...
        def removeFirst(self, /) -> None: ...
        def removeLast(self, /) -> None: ...
        def removeOne(self, arg__1: typing.Any, /) -> None: ...
        def reserve(self, size: int, /) -> None: ...
        def resize(self, size: int, /) -> None: ...
        def resizeForOverwrite(self, size: int, /) -> None: ...
        def shrink_to_fit(self, /) -> None: ...
        def size(self, /) -> int: ...
        @typing.overload
        def sliced(self, pos: int, /) -> typing.List[typing.Any]: ...
        @typing.overload
        def sliced(self, pos: int, n: int, /) -> typing.List[typing.Any]: ...
        def squeeze(self, /) -> None: ...
        def swap(self, other: collections.abc.Sequence[typing.Any], /) -> None: ...
        def swapItemsAt(self, i: int, j: int, /) -> None: ...
        def takeAt(self, i: int, /) -> typing.Any: ...
        def toList(self, /) -> typing.List[typing.Any]: ...
        def toVector(self, /) -> typing.List[typing.Any]: ...
        def value(self, i: int, /) -> typing.Any: ...


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QBluetoothServiceInfo, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    def attribute(self, attributeId: int, /) -> typing.Any: ...
    def attributes(self, /) -> typing.List[int]: ...
    def contains(self, attributeId: int, /) -> bool: ...
    def device(self, /) -> PySide6.QtBluetooth.QBluetoothDeviceInfo: ...
    def isComplete(self, /) -> bool: ...
    def isRegistered(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def protocolDescriptor(self, protocol: PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid, /) -> PySide6.QtBluetooth.QBluetoothServiceInfo.Sequence: ...
    def protocolServiceMultiplexer(self, /) -> int: ...
    def registerService(self, /, localAdapter: PySide6.QtBluetooth.QBluetoothAddress = ...) -> bool: ...
    def removeAttribute(self, attributeId: int, /) -> None: ...
    def serverChannel(self, /) -> int: ...
    def serviceAvailability(self, /) -> int: ...
    def serviceClassUuids(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def serviceDescription(self, /) -> str: ...
    def serviceName(self, /) -> str: ...
    def serviceProvider(self, /) -> str: ...
    def serviceUuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: PySide6.QtBluetooth.QBluetoothServiceInfo.Alternative | collections.abc.Sequence[typing.Any], /) -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: PySide6.QtBluetooth.QBluetoothServiceInfo.Sequence | collections.abc.Sequence[typing.Any], /) -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    @typing.overload
    def setAttribute(self, attributeId: int, value: typing.Any, /) -> None: ...
    def setDevice(self, info: PySide6.QtBluetooth.QBluetoothDeviceInfo, /) -> None: ...
    def setServiceAvailability(self, availability: int, /) -> None: ...
    def setServiceDescription(self, description: str, /) -> None: ...
    def setServiceName(self, name: str, /) -> None: ...
    def setServiceProvider(self, provider: str, /) -> None: ...
    def setServiceUuid(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def socketProtocol(self, /) -> PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol: ...
    def unregisterService(self, /) -> bool: ...


class QBluetoothSocket(PySide6.QtCore.QIODevice):

    connected                : typing.ClassVar[Signal] = ... # connected()
    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QBluetoothSocket::SocketError)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QBluetoothSocket::SocketState)

    class SocketError(enum.Enum):

        NoSocketError             = ...  # 0x0
        UnknownSocketError        = ...  # 0x1
        RemoteHostClosedError     = ...  # 0x2
        HostNotFoundError         = ...  # 0x3
        ServiceNotFoundError      = ...  # 0x4
        NetworkError              = ...  # 0x5
        UnsupportedProtocolError  = ...  # 0x6
        OperationError            = ...  # 0x7
        MissingPermissionsError   = ...  # 0x8

    class SocketState(enum.Enum):

        UnconnectedState          = ...  # 0x0
        ServiceLookupState        = ...  # 0x1
        ConnectingState           = ...  # 0x2
        ConnectedState            = ...  # 0x3
        BoundState                = ...  # 0x4
        ClosingState              = ...  # 0x5
        ListeningState            = ...  # 0x6


    @typing.overload
    def __init__(self, socketType: PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def bytesAvailable(self, /) -> int: ...
    def bytesToWrite(self, /) -> int: ...
    def canReadLine(self, /) -> bool: ...
    def close(self, /) -> None: ...
    @typing.overload
    def connectToService(self, address: PySide6.QtBluetooth.QBluetoothAddress, uuid: PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid, /, mode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToService(self, address: PySide6.QtBluetooth.QBluetoothAddress, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToService(self, address: PySide6.QtBluetooth.QBluetoothAddress, port: int, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToService(self, service: PySide6.QtBluetooth.QBluetoothServiceInfo, /, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    def disconnectFromService(self, /) -> None: ...
    def doDeviceDiscovery(self, service: PySide6.QtBluetooth.QBluetoothServiceInfo, openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag, /) -> None: ...
    def error(self, /) -> PySide6.QtBluetooth.QBluetoothSocket.SocketError: ...
    def errorString(self, /) -> str: ...
    def isSequential(self, /) -> bool: ...
    def localAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def localName(self, /) -> str: ...
    def localPort(self, /) -> int: ...
    def peerAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def peerName(self, /) -> str: ...
    def peerPort(self, /) -> int: ...
    def preferredSecurityFlags(self, /) -> PySide6.QtBluetooth.QBluetooth.Security: ...
    def readData(self, maxSize: int, /) -> object: ...
    def setPreferredSecurityFlags(self, flags: PySide6.QtBluetooth.QBluetooth.Security, /) -> None: ...
    def setSocketDescriptor(self, socketDescriptor: int, socketType: PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol, /, socketState: PySide6.QtBluetooth.QBluetoothSocket.SocketState = ..., openMode: PySide6.QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def setSocketError(self, error: PySide6.QtBluetooth.QBluetoothSocket.SocketError, /) -> None: ...
    def setSocketState(self, state: PySide6.QtBluetooth.QBluetoothSocket.SocketState, /) -> None: ...
    def socketDescriptor(self, /) -> int: ...
    def socketType(self, /) -> PySide6.QtBluetooth.QBluetoothServiceInfo.Protocol: ...
    def state(self, /) -> PySide6.QtBluetooth.QBluetoothSocket.SocketState: ...
    def writeData(self, data: bytes | bytearray | memoryview, maxSize: int, /) -> int: ...


class QBluetoothUuid(PySide6.QtCore.QUuid):

    class CharacteristicType(enum.Enum):

        DeviceName                = ...  # 0x2a00
        Appearance                = ...  # 0x2a01
        PeripheralPrivacyFlag     = ...  # 0x2a02
        ReconnectionAddress       = ...  # 0x2a03
        PeripheralPreferredConnectionParameters = ...  # 0x2a04
        ServiceChanged            = ...  # 0x2a05
        AlertLevel                = ...  # 0x2a06
        TxPowerLevel              = ...  # 0x2a07
        DateTime                  = ...  # 0x2a08
        DayOfWeek                 = ...  # 0x2a09
        DayDateTime               = ...  # 0x2a0a
        ExactTime256              = ...  # 0x2a0c
        DSTOffset                 = ...  # 0x2a0d
        TimeZone                  = ...  # 0x2a0e
        LocalTimeInformation      = ...  # 0x2a0f
        TimeWithDST               = ...  # 0x2a11
        TimeAccuracy              = ...  # 0x2a12
        TimeSource                = ...  # 0x2a13
        ReferenceTimeInformation  = ...  # 0x2a14
        TimeUpdateControlPoint    = ...  # 0x2a16
        TimeUpdateState           = ...  # 0x2a17
        GlucoseMeasurement        = ...  # 0x2a18
        BatteryLevel              = ...  # 0x2a19
        TemperatureMeasurement    = ...  # 0x2a1c
        TemperatureType           = ...  # 0x2a1d
        IntermediateTemperature   = ...  # 0x2a1e
        MeasurementInterval       = ...  # 0x2a21
        BootKeyboardInputReport   = ...  # 0x2a22
        SystemID                  = ...  # 0x2a23
        ModelNumberString         = ...  # 0x2a24
        SerialNumberString        = ...  # 0x2a25
        FirmwareRevisionString    = ...  # 0x2a26
        HardwareRevisionString    = ...  # 0x2a27
        SoftwareRevisionString    = ...  # 0x2a28
        ManufacturerNameString    = ...  # 0x2a29
        IEEE1107320601RegulatoryCertificationDataList = ...  # 0x2a2a
        CurrentTime               = ...  # 0x2a2b
        MagneticDeclination       = ...  # 0x2a2c
        ScanRefresh               = ...  # 0x2a31
        BootKeyboardOutputReport  = ...  # 0x2a32
        BootMouseInputReport      = ...  # 0x2a33
        GlucoseMeasurementContext = ...  # 0x2a34
        BloodPressureMeasurement  = ...  # 0x2a35
        IntermediateCuffPressure  = ...  # 0x2a36
        HeartRateMeasurement      = ...  # 0x2a37
        BodySensorLocation        = ...  # 0x2a38
        HeartRateControlPoint     = ...  # 0x2a39
        AlertStatus               = ...  # 0x2a3f
        RingerControlPoint        = ...  # 0x2a40
        RingerSetting             = ...  # 0x2a41
        AlertCategoryIDBitMask    = ...  # 0x2a42
        AlertCategoryID           = ...  # 0x2a43
        AlertNotificationControlPoint = ...  # 0x2a44
        UnreadAlertStatus         = ...  # 0x2a45
        NewAlert                  = ...  # 0x2a46
        SupportedNewAlertCategory = ...  # 0x2a47
        SupportedUnreadAlertCategory = ...  # 0x2a48
        BloodPressureFeature      = ...  # 0x2a49
        HIDInformation            = ...  # 0x2a4a
        ReportMap                 = ...  # 0x2a4b
        HIDControlPoint           = ...  # 0x2a4c
        Report                    = ...  # 0x2a4d
        ProtocolMode              = ...  # 0x2a4e
        ScanIntervalWindow        = ...  # 0x2a4f
        PnPID                     = ...  # 0x2a50
        GlucoseFeature            = ...  # 0x2a51
        RecordAccessControlPoint  = ...  # 0x2a52
        RSCMeasurement            = ...  # 0x2a53
        RSCFeature                = ...  # 0x2a54
        SCControlPoint            = ...  # 0x2a55
        CSCMeasurement            = ...  # 0x2a5b
        CSCFeature                = ...  # 0x2a5c
        SensorLocation            = ...  # 0x2a5d
        CyclingPowerMeasurement   = ...  # 0x2a63
        CyclingPowerVector        = ...  # 0x2a64
        CyclingPowerFeature       = ...  # 0x2a65
        CyclingPowerControlPoint  = ...  # 0x2a66
        LocationAndSpeed          = ...  # 0x2a67
        Navigation                = ...  # 0x2a68
        PositionQuality           = ...  # 0x2a69
        LNFeature                 = ...  # 0x2a6a
        LNControlPoint            = ...  # 0x2a6b
        Elevation                 = ...  # 0x2a6c
        Pressure                  = ...  # 0x2a6d
        Temperature               = ...  # 0x2a6e
        Humidity                  = ...  # 0x2a6f
        TrueWindSpeed             = ...  # 0x2a70
        TrueWindDirection         = ...  # 0x2a71
        ApparentWindSpeed         = ...  # 0x2a72
        ApparentWindDirection     = ...  # 0x2a73
        GustFactor                = ...  # 0x2a74
        PollenConcentration       = ...  # 0x2a75
        UVIndex                   = ...  # 0x2a76
        Irradiance                = ...  # 0x2a77
        Rainfall                  = ...  # 0x2a78
        WindChill                 = ...  # 0x2a79
        HeatIndex                 = ...  # 0x2a7a
        DewPoint                  = ...  # 0x2a7b
        DescriptorValueChanged    = ...  # 0x2a7d
        AerobicHeartRateLowerLimit = ...  # 0x2a7e
        AerobicThreshold          = ...  # 0x2a7f
        Age                       = ...  # 0x2a80
        AnaerobicHeartRateLowerLimit = ...  # 0x2a81
        AnaerobicHeartRateUpperLimit = ...  # 0x2a82
        AnaerobicThreshold        = ...  # 0x2a83
        AerobicHeartRateUpperLimit = ...  # 0x2a84
        DateOfBirth               = ...  # 0x2a85
        DateOfThresholdAssessment = ...  # 0x2a86
        EmailAddress              = ...  # 0x2a87
        FatBurnHeartRateLowerLimit = ...  # 0x2a88
        FatBurnHeartRateUpperLimit = ...  # 0x2a89
        FirstName                 = ...  # 0x2a8a
        FiveZoneHeartRateLimits   = ...  # 0x2a8b
        Gender                    = ...  # 0x2a8c
        HeartRateMax              = ...  # 0x2a8d
        Height                    = ...  # 0x2a8e
        HipCircumference          = ...  # 0x2a8f
        LastName                  = ...  # 0x2a90
        MaximumRecommendedHeartRate = ...  # 0x2a91
        RestingHeartRate          = ...  # 0x2a92
        SportTypeForAerobicAnaerobicThresholds = ...  # 0x2a93
        ThreeZoneHeartRateLimits  = ...  # 0x2a94
        TwoZoneHeartRateLimits    = ...  # 0x2a95
        VO2Max                    = ...  # 0x2a96
        WaistCircumference        = ...  # 0x2a97
        Weight                    = ...  # 0x2a98
        DatabaseChangeIncrement   = ...  # 0x2a99
        UserIndex                 = ...  # 0x2a9a
        BodyCompositionFeature    = ...  # 0x2a9b
        BodyCompositionMeasurement = ...  # 0x2a9c
        WeightMeasurement         = ...  # 0x2a9d
        WeightScaleFeature        = ...  # 0x2a9e
        UserControlPoint          = ...  # 0x2a9f
        MagneticFluxDensity2D     = ...  # 0x2aa0
        MagneticFluxDensity3D     = ...  # 0x2aa1
        Language                  = ...  # 0x2aa2
        BarometricPressureTrend   = ...  # 0x2aa3

    class DescriptorType(enum.Enum):

        UnknownDescriptorType     = ...  # 0x0
        CharacteristicExtendedProperties = ...  # 0x2900
        CharacteristicUserDescription = ...  # 0x2901
        ClientCharacteristicConfiguration = ...  # 0x2902
        ServerCharacteristicConfiguration = ...  # 0x2903
        CharacteristicPresentationFormat = ...  # 0x2904
        CharacteristicAggregateFormat = ...  # 0x2905
        ValidRange                = ...  # 0x2906
        ExternalReportReference   = ...  # 0x2907
        ReportReference           = ...  # 0x2908
        EnvironmentalSensingConfiguration = ...  # 0x290b
        EnvironmentalSensingMeasurement = ...  # 0x290c
        EnvironmentalSensingTriggerSetting = ...  # 0x290d

    class ProtocolUuid(enum.Enum):

        Sdp                       = ...  # 0x1
        Udp                       = ...  # 0x2
        Rfcomm                    = ...  # 0x3
        Tcp                       = ...  # 0x4
        TcsBin                    = ...  # 0x5
        TcsAt                     = ...  # 0x6
        Att                       = ...  # 0x7
        Obex                      = ...  # 0x8
        Ip                        = ...  # 0x9
        Ftp                       = ...  # 0xa
        Http                      = ...  # 0xc
        Wsp                       = ...  # 0xe
        Bnep                      = ...  # 0xf
        Upnp                      = ...  # 0x10
        Hidp                      = ...  # 0x11
        HardcopyControlChannel    = ...  # 0x12
        HardcopyDataChannel       = ...  # 0x14
        HardcopyNotification      = ...  # 0x16
        Avctp                     = ...  # 0x17
        Avdtp                     = ...  # 0x19
        Cmtp                      = ...  # 0x1b
        UdiCPlain                 = ...  # 0x1d
        McapControlChannel        = ...  # 0x1e
        McapDataChannel           = ...  # 0x1f
        L2cap                     = ...  # 0x100

    class ServiceClassUuid(enum.Enum):

        ServiceDiscoveryServer    = ...  # 0x1000
        BrowseGroupDescriptor     = ...  # 0x1001
        PublicBrowseGroup         = ...  # 0x1002
        SerialPort                = ...  # 0x1101
        LANAccessUsingPPP         = ...  # 0x1102
        DialupNetworking          = ...  # 0x1103
        IrMCSync                  = ...  # 0x1104
        ObexObjectPush            = ...  # 0x1105
        OBEXFileTransfer          = ...  # 0x1106
        IrMCSyncCommand           = ...  # 0x1107
        Headset                   = ...  # 0x1108
        AudioSource               = ...  # 0x110a
        AudioSink                 = ...  # 0x110b
        AV_RemoteControlTarget    = ...  # 0x110c
        AdvancedAudioDistribution = ...  # 0x110d
        AV_RemoteControl          = ...  # 0x110e
        AV_RemoteControlController = ...  # 0x110f
        HeadsetAG                 = ...  # 0x1112
        PANU                      = ...  # 0x1115
        NAP                       = ...  # 0x1116
        GN                        = ...  # 0x1117
        DirectPrinting            = ...  # 0x1118
        ReferencePrinting         = ...  # 0x1119
        BasicImage                = ...  # 0x111a
        ImagingResponder          = ...  # 0x111b
        ImagingAutomaticArchive   = ...  # 0x111c
        ImagingReferenceObjects   = ...  # 0x111d
        Handsfree                 = ...  # 0x111e
        HandsfreeAudioGateway     = ...  # 0x111f
        DirectPrintingReferenceObjectsService = ...  # 0x1120
        ReflectedUI               = ...  # 0x1121
        BasicPrinting             = ...  # 0x1122
        PrintingStatus            = ...  # 0x1123
        HumanInterfaceDeviceService = ...  # 0x1124
        HardcopyCableReplacement  = ...  # 0x1125
        HCRPrint                  = ...  # 0x1126
        HCRScan                   = ...  # 0x1127
        SIMAccess                 = ...  # 0x112d
        PhonebookAccessPCE        = ...  # 0x112e
        PhonebookAccessPSE        = ...  # 0x112f
        PhonebookAccess           = ...  # 0x1130
        HeadsetHS                 = ...  # 0x1131
        MessageAccessServer       = ...  # 0x1132
        MessageNotificationServer = ...  # 0x1133
        MessageAccessProfile      = ...  # 0x1134
        GNSS                      = ...  # 0x1135
        GNSSServer                = ...  # 0x1136
        Display3D                 = ...  # 0x1137
        Glasses3D                 = ...  # 0x1138
        Synchronization3D         = ...  # 0x1139
        MPSProfile                = ...  # 0x113a
        MPSService                = ...  # 0x113b
        PnPInformation            = ...  # 0x1200
        GenericNetworking         = ...  # 0x1201
        GenericFileTransfer       = ...  # 0x1202
        GenericAudio              = ...  # 0x1203
        GenericTelephony          = ...  # 0x1204
        VideoSource               = ...  # 0x1303
        VideoSink                 = ...  # 0x1304
        VideoDistribution         = ...  # 0x1305
        HDP                       = ...  # 0x1400
        HDPSource                 = ...  # 0x1401
        HDPSink                   = ...  # 0x1402
        GenericAccess             = ...  # 0x1800
        GenericAttribute          = ...  # 0x1801
        ImmediateAlert            = ...  # 0x1802
        LinkLoss                  = ...  # 0x1803
        TxPower                   = ...  # 0x1804
        CurrentTimeService        = ...  # 0x1805
        ReferenceTimeUpdateService = ...  # 0x1806
        NextDSTChangeService      = ...  # 0x1807
        Glucose                   = ...  # 0x1808
        HealthThermometer         = ...  # 0x1809
        DeviceInformation         = ...  # 0x180a
        HeartRate                 = ...  # 0x180d
        PhoneAlertStatusService   = ...  # 0x180e
        BatteryService            = ...  # 0x180f
        BloodPressure             = ...  # 0x1810
        AlertNotificationService  = ...  # 0x1811
        HumanInterfaceDevice      = ...  # 0x1812
        ScanParameters            = ...  # 0x1813
        RunningSpeedAndCadence    = ...  # 0x1814
        CyclingSpeedAndCadence    = ...  # 0x1816
        CyclingPower              = ...  # 0x1818
        LocationAndNavigation     = ...  # 0x1819
        EnvironmentalSensing      = ...  # 0x181a
        BodyComposition           = ...  # 0x181b
        UserData                  = ...  # 0x181c
        WeightScale               = ...  # 0x181d
        BondManagement            = ...  # 0x181e
        ContinuousGlucoseMonitoring = ...  # 0x181f


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid.DescriptorType, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtCore.QUuid, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> bool: ...
    def __lshift__(self, s: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __ne__(self, b: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> bool: ...
    def __rshift__(self, s: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    @staticmethod
    def characteristicToString(uuid: PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType, /) -> str: ...
    @staticmethod
    def descriptorToString(uuid: PySide6.QtBluetooth.QBluetoothUuid.DescriptorType, /) -> str: ...
    def minimumSize(self, /) -> int: ...
    @staticmethod
    def protocolToString(uuid: PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid, /) -> str: ...
    @staticmethod
    def serviceClassToString(uuid: PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid, /) -> str: ...
    def toUInt16(self, /) -> typing.Tuple[int, bool]: ...
    def toUInt32(self, /) -> typing.Tuple[int, bool]: ...


class QIntList: ...


class QLowEnergyAdvertisingData(Shiboken.Object):

    class Discoverability(enum.Enum):

        DiscoverabilityNone       = ...  # 0x0
        DiscoverabilityLimited    = ...  # 0x1
        DiscoverabilityGeneral    = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyAdvertisingData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingData, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingData, /) -> bool: ...
    def discoverability(self, /) -> PySide6.QtBluetooth.QLowEnergyAdvertisingData.Discoverability: ...
    def includePowerLevel(self, /) -> bool: ...
    @staticmethod
    def invalidManufacturerId() -> int: ...
    def localName(self, /) -> str: ...
    def manufacturerData(self, /) -> PySide6.QtCore.QByteArray: ...
    def manufacturerId(self, /) -> int: ...
    def rawData(self, /) -> PySide6.QtCore.QByteArray: ...
    def services(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def setDiscoverability(self, mode: PySide6.QtBluetooth.QLowEnergyAdvertisingData.Discoverability, /) -> None: ...
    def setIncludePowerLevel(self, doInclude: bool, /) -> None: ...
    def setLocalName(self, name: str, /) -> None: ...
    def setManufacturerData(self, id: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setRawData(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setServices(self, services: collections.abc.Sequence[PySide6.QtBluetooth.QBluetoothUuid], /) -> None: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyAdvertisingData, /) -> None: ...


class QLowEnergyAdvertisingParameters(Shiboken.Object):

    class AddressInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, AddressInfo: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.AddressInfo, /) -> None: ...
        @typing.overload
        def __init__(self, addr: PySide6.QtBluetooth.QBluetoothAddress, t: PySide6.QtBluetooth.QLowEnergyController.RemoteAddressType, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.AddressInfo, /) -> bool: ...
        def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.AddressInfo, /) -> bool: ...

    class FilterPolicy(enum.Enum):

        IgnoreWhiteList           = ...  # 0x0
        UseWhiteListForScanning   = ...  # 0x1
        UseWhiteListForConnecting = ...  # 0x2
        UseWhiteListForScanningAndConnecting = ...  # 0x3

    class Mode(enum.Enum):

        AdvInd                    = ...  # 0x0
        AdvScanInd                = ...  # 0x2
        AdvNonConnInd             = ...  # 0x3


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters, /) -> bool: ...
    def filterPolicy(self, /) -> PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.FilterPolicy: ...
    def maximumInterval(self, /) -> int: ...
    def minimumInterval(self, /) -> int: ...
    def mode(self, /) -> PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.Mode: ...
    def setInterval(self, minimum: int, maximum: int, /) -> None: ...
    def setMode(self, mode: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.Mode, /) -> None: ...
    def setWhiteList(self, whiteList: collections.abc.Sequence[PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.AddressInfo], policy: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.FilterPolicy, /) -> None: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters, /) -> None: ...
    def whiteList(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyAdvertisingParameters.AddressInfo]: ...


class QLowEnergyCharacteristic(Shiboken.Object):

    CCCDDisable               = ...  # type: PySide6.QtCore.QByteArray
    CCCDEnableIndication      = ...  # type: PySide6.QtCore.QByteArray
    CCCDEnableNotification    = ...  # type: PySide6.QtCore.QByteArray

    class PropertyType(enum.Flag):

        Unknown                   = ...  # 0x0
        Broadcasting              = ...  # 0x1
        Read                      = ...  # 0x2
        WriteNoResponse           = ...  # 0x4
        Write                     = ...  # 0x8
        Notify                    = ...  # 0x10
        Indicate                  = ...  # 0x20
        WriteSigned               = ...  # 0x40
        ExtendedProperty          = ...  # 0x80


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyCharacteristic, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyCharacteristic, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyCharacteristic, /) -> bool: ...
    def clientCharacteristicConfiguration(self, /) -> PySide6.QtBluetooth.QLowEnergyDescriptor: ...
    def descriptor(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> PySide6.QtBluetooth.QLowEnergyDescriptor: ...
    def descriptors(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyDescriptor]: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def properties(self, /) -> PySide6.QtBluetooth.QLowEnergyCharacteristic.PropertyType: ...
    def uuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...


class QLowEnergyCharacteristicData(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyCharacteristicData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyCharacteristicData, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyCharacteristicData, /) -> bool: ...
    def addDescriptor(self, descriptor: PySide6.QtBluetooth.QLowEnergyDescriptorData, /) -> None: ...
    def descriptors(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyDescriptorData]: ...
    def isValid(self, /) -> bool: ...
    def maximumValueLength(self, /) -> int: ...
    def minimumValueLength(self, /) -> int: ...
    def properties(self, /) -> PySide6.QtBluetooth.QLowEnergyCharacteristic.PropertyType: ...
    def readConstraints(self, /) -> PySide6.QtBluetooth.QBluetooth.AttAccessConstraint: ...
    def setDescriptors(self, descriptors: collections.abc.Sequence[PySide6.QtBluetooth.QLowEnergyDescriptorData], /) -> None: ...
    def setProperties(self, properties: PySide6.QtBluetooth.QLowEnergyCharacteristic.PropertyType, /) -> None: ...
    def setReadConstraints(self, constraints: PySide6.QtBluetooth.QBluetooth.AttAccessConstraint, /) -> None: ...
    def setUuid(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def setValue(self, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setValueLength(self, minimum: int, maximum: int, /) -> None: ...
    def setWriteConstraints(self, constraints: PySide6.QtBluetooth.QBluetooth.AttAccessConstraint, /) -> None: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyCharacteristicData, /) -> None: ...
    def uuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...
    def writeConstraints(self, /) -> PySide6.QtBluetooth.QBluetooth.AttAccessConstraint: ...


class QLowEnergyConnectionParameters(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyConnectionParameters, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyConnectionParameters, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyConnectionParameters, /) -> bool: ...
    def latency(self, /) -> int: ...
    def maximumInterval(self, /) -> float: ...
    def minimumInterval(self, /) -> float: ...
    def setIntervalRange(self, minimum: float, maximum: float, /) -> None: ...
    def setLatency(self, latency: int, /) -> None: ...
    def setSupervisionTimeout(self, timeout: int, /) -> None: ...
    def supervisionTimeout(self, /) -> int: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyConnectionParameters, /) -> None: ...


class QLowEnergyController(PySide6.QtCore.QObject):

    connected                : typing.ClassVar[Signal] = ... # connected()
    connectionUpdated        : typing.ClassVar[Signal] = ... # connectionUpdated(QLowEnergyConnectionParameters)
    disconnected             : typing.ClassVar[Signal] = ... # disconnected()
    discoveryFinished        : typing.ClassVar[Signal] = ... # discoveryFinished()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QLowEnergyController::Error)
    mtuChanged               : typing.ClassVar[Signal] = ... # mtuChanged(int)
    rssiRead                 : typing.ClassVar[Signal] = ... # rssiRead(short)
    serviceDiscovered        : typing.ClassVar[Signal] = ... # serviceDiscovered(QBluetoothUuid)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QLowEnergyController::ControllerState)

    class ControllerState(enum.Enum):

        UnconnectedState          = ...  # 0x0
        ConnectingState           = ...  # 0x1
        ConnectedState            = ...  # 0x2
        DiscoveringState          = ...  # 0x3
        DiscoveredState           = ...  # 0x4
        ClosingState              = ...  # 0x5
        AdvertisingState          = ...  # 0x6

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        UnknownError              = ...  # 0x1
        UnknownRemoteDeviceError  = ...  # 0x2
        NetworkError              = ...  # 0x3
        InvalidBluetoothAdapterError = ...  # 0x4
        ConnectionError           = ...  # 0x5
        AdvertisingError          = ...  # 0x6
        RemoteHostClosedError     = ...  # 0x7
        AuthorizationError        = ...  # 0x8
        MissingPermissionsError   = ...  # 0x9
        RssiReadError             = ...  # 0xa

    class RemoteAddressType(enum.Enum):

        PublicAddress             = ...  # 0x0
        RandomAddress             = ...  # 0x1

    class Role(enum.Enum):

        CentralRole               = ...  # 0x0
        PeripheralRole            = ...  # 0x1


    def addService(self, service: PySide6.QtBluetooth.QLowEnergyServiceData, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyService: ...
    def connectToDevice(self, /) -> None: ...
    @typing.overload
    @staticmethod
    def createCentral(remoteDevice: PySide6.QtBluetooth.QBluetoothDeviceInfo, localDevice: PySide6.QtBluetooth.QBluetoothAddress, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyController: ...
    @typing.overload
    @staticmethod
    def createCentral(remoteDevice: PySide6.QtBluetooth.QBluetoothDeviceInfo, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyController: ...
    @typing.overload
    @staticmethod
    def createPeripheral(localDevice: PySide6.QtBluetooth.QBluetoothAddress, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyController: ...
    @typing.overload
    @staticmethod
    def createPeripheral(parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyController: ...
    def createServiceObject(self, service: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /, parent: PySide6.QtCore.QObject | None = ...) -> PySide6.QtBluetooth.QLowEnergyService: ...
    def disconnectFromDevice(self, /) -> None: ...
    def discoverServices(self, /) -> None: ...
    def error(self, /) -> PySide6.QtBluetooth.QLowEnergyController.Error: ...
    def errorString(self, /) -> str: ...
    def localAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def mtu(self, /) -> int: ...
    def readRssi(self, /) -> None: ...
    def remoteAddress(self, /) -> PySide6.QtBluetooth.QBluetoothAddress: ...
    def remoteAddressType(self, /) -> PySide6.QtBluetooth.QLowEnergyController.RemoteAddressType: ...
    def remoteDeviceUuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def remoteName(self, /) -> str: ...
    def requestConnectionUpdate(self, parameters: PySide6.QtBluetooth.QLowEnergyConnectionParameters, /) -> None: ...
    def role(self, /) -> PySide6.QtBluetooth.QLowEnergyController.Role: ...
    def services(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def setRemoteAddressType(self, type: PySide6.QtBluetooth.QLowEnergyController.RemoteAddressType, /) -> None: ...
    def startAdvertising(self, parameters: PySide6.QtBluetooth.QLowEnergyAdvertisingParameters, advertisingData: PySide6.QtBluetooth.QLowEnergyAdvertisingData, /, scanResponseData: PySide6.QtBluetooth.QLowEnergyAdvertisingData = ...) -> None: ...
    def state(self, /) -> PySide6.QtBluetooth.QLowEnergyController.ControllerState: ...
    def stopAdvertising(self, /) -> None: ...


class QLowEnergyDescriptor(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyDescriptor, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyDescriptor, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyDescriptor, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def type(self, /) -> PySide6.QtBluetooth.QBluetoothUuid.DescriptorType: ...
    def uuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...


class QLowEnergyDescriptorData(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyDescriptorData, /) -> None: ...
    @typing.overload
    def __init__(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyDescriptorData, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyDescriptorData, /) -> bool: ...
    def isReadable(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def isWritable(self, /) -> bool: ...
    def readConstraints(self, /) -> PySide6.QtBluetooth.QBluetooth.AttAccessConstraint: ...
    def setReadPermissions(self, readable: bool, /, constraints: PySide6.QtBluetooth.QBluetooth.AttAccessConstraint = ...) -> None: ...
    def setUuid(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def setValue(self, value: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setWritePermissions(self, writable: bool, /, constraints: PySide6.QtBluetooth.QBluetooth.AttAccessConstraint = ...) -> None: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyDescriptorData, /) -> None: ...
    def uuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def value(self, /) -> PySide6.QtCore.QByteArray: ...
    def writeConstraints(self, /) -> PySide6.QtBluetooth.QBluetooth.AttAccessConstraint: ...


class QLowEnergyService(PySide6.QtCore.QObject):

    characteristicChanged    : typing.ClassVar[Signal] = ... # characteristicChanged(QLowEnergyCharacteristic,QByteArray)
    characteristicRead       : typing.ClassVar[Signal] = ... # characteristicRead(QLowEnergyCharacteristic,QByteArray)
    characteristicWritten    : typing.ClassVar[Signal] = ... # characteristicWritten(QLowEnergyCharacteristic,QByteArray)
    descriptorRead           : typing.ClassVar[Signal] = ... # descriptorRead(QLowEnergyDescriptor,QByteArray)
    descriptorWritten        : typing.ClassVar[Signal] = ... # descriptorWritten(QLowEnergyDescriptor,QByteArray)
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QLowEnergyService::ServiceError)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QLowEnergyService::ServiceState)

    class DiscoveryMode(enum.Enum):

        FullDiscovery             = ...  # 0x0
        SkipValueDiscovery        = ...  # 0x1

    class ServiceError(enum.Enum):

        NoError                   = ...  # 0x0
        OperationError            = ...  # 0x1
        CharacteristicWriteError  = ...  # 0x2
        DescriptorWriteError      = ...  # 0x3
        UnknownError              = ...  # 0x4
        CharacteristicReadError   = ...  # 0x5
        DescriptorReadError       = ...  # 0x6

    class ServiceState(enum.Enum):

        InvalidService            = ...  # 0x0
        DiscoveryRequired         = ...  # 0x1
        RemoteService             = ...  # 0x1
        DiscoveringService        = ...  # 0x2
        RemoteServiceDiscovering  = ...  # 0x2
        RemoteServiceDiscovered   = ...  # 0x3
        ServiceDiscovered         = ...  # 0x3
        LocalService              = ...  # 0x4

    class ServiceType(enum.Flag):

        PrimaryService            = ...  # 0x1
        IncludedService           = ...  # 0x2

    class WriteMode(enum.Enum):

        WriteWithResponse         = ...  # 0x0
        WriteWithoutResponse      = ...  # 0x1
        WriteSigned               = ...  # 0x2


    def characteristic(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> PySide6.QtBluetooth.QLowEnergyCharacteristic: ...
    def characteristics(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyCharacteristic]: ...
    @typing.overload
    def contains(self, characteristic: PySide6.QtBluetooth.QLowEnergyCharacteristic, /) -> bool: ...
    @typing.overload
    def contains(self, descriptor: PySide6.QtBluetooth.QLowEnergyDescriptor, /) -> bool: ...
    def discoverDetails(self, /, mode: PySide6.QtBluetooth.QLowEnergyService.DiscoveryMode = ...) -> None: ...
    def error(self, /) -> PySide6.QtBluetooth.QLowEnergyService.ServiceError: ...
    def includedServices(self, /) -> typing.List[PySide6.QtBluetooth.QBluetoothUuid]: ...
    def readCharacteristic(self, characteristic: PySide6.QtBluetooth.QLowEnergyCharacteristic, /) -> None: ...
    def readDescriptor(self, descriptor: PySide6.QtBluetooth.QLowEnergyDescriptor, /) -> None: ...
    def serviceName(self, /) -> str: ...
    def serviceUuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...
    def state(self, /) -> PySide6.QtBluetooth.QLowEnergyService.ServiceState: ...
    def type(self, /) -> PySide6.QtBluetooth.QLowEnergyService.ServiceType: ...
    def writeCharacteristic(self, characteristic: PySide6.QtBluetooth.QLowEnergyCharacteristic, newValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /, mode: PySide6.QtBluetooth.QLowEnergyService.WriteMode = ...) -> None: ...
    def writeDescriptor(self, descriptor: PySide6.QtBluetooth.QLowEnergyDescriptor, newValue: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...


class QLowEnergyServiceData(Shiboken.Object):

    class ServiceType(enum.Enum):

        ServiceTypePrimary        = ...  # 0x2800
        ServiceTypeSecondary      = ...  # 0x2801


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtBluetooth.QLowEnergyServiceData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, b: PySide6.QtBluetooth.QLowEnergyServiceData, /) -> bool: ...
    def __ne__(self, b: PySide6.QtBluetooth.QLowEnergyServiceData, /) -> bool: ...
    def addCharacteristic(self, characteristic: PySide6.QtBluetooth.QLowEnergyCharacteristicData, /) -> None: ...
    def addIncludedService(self, service: PySide6.QtBluetooth.QLowEnergyService, /) -> None: ...
    def characteristics(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyCharacteristicData]: ...
    def includedServices(self, /) -> typing.List[PySide6.QtBluetooth.QLowEnergyService]: ...
    def isValid(self, /) -> bool: ...
    def setCharacteristics(self, characteristics: collections.abc.Sequence[PySide6.QtBluetooth.QLowEnergyCharacteristicData], /) -> None: ...
    def setIncludedServices(self, services: collections.abc.Sequence[PySide6.QtBluetooth.QLowEnergyService], /) -> None: ...
    def setType(self, type: PySide6.QtBluetooth.QLowEnergyServiceData.ServiceType, /) -> None: ...
    def setUuid(self, uuid: PySide6.QtBluetooth.QBluetoothUuid | PySide6.QtBluetooth.QBluetoothUuid.CharacteristicType | PySide6.QtBluetooth.QBluetoothUuid.DescriptorType | PySide6.QtBluetooth.QBluetoothUuid.ProtocolUuid | PySide6.QtBluetooth.QBluetoothUuid.ServiceClassUuid | PySide6.QtCore.QUuid, /) -> None: ...
    def swap(self, other: PySide6.QtBluetooth.QLowEnergyServiceData, /) -> None: ...
    def type(self, /) -> PySide6.QtBluetooth.QLowEnergyServiceData.ServiceType: ...
    def uuid(self, /) -> PySide6.QtBluetooth.QBluetoothUuid: ...


# eof
