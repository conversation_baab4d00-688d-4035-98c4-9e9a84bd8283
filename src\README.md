### FactoryEye
Industrial camera management and analysis tool for IDS camera.

## Run code guide
# Make resource file
- Run the below command
```bash
pyside6-rcc resources.qrc -o resources_rc.py
```

# Run source
- Run the below command
```bash
python ./src/main.py
```


## Build product version guide
# Create Logo image
- Run the below command
```bash
python convert_icon.py
```

# Build source to create executable file
- Run the below command
```bash
python build.py
```

- Check setup.exe in the below path
```bash
.\src\factoryeye\dist\FactoryEye
```


# Make setup version
- Fristly need to install NSIS in the below path
```bash
.\reference/setup/nsis-3.11-setup.exe
```

- Run the below command
```bash
python create_installer.py
```

- Check setup.exe in the below path
```bash
.\src\factoryeye\dist\FactoryEye_Setup.exe
```