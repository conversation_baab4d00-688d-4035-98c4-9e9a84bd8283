import os
import subprocess
import shutil
from pathlib import Path

def create_installer():
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Ensure the dist directory exists
    dist_dir = project_root / "dist"
    dist_dir.mkdir(exist_ok=True)
    
    # First, run the PyInstaller build
    print("Building application with PyInstaller...")
    build_result = subprocess.run(["python", "build.py"], check=True)
    if build_result.returncode != 0:
        raise Exception("PyInstaller build failed")
    
    # Check if NSIS is installed
    nsis_path = r"C:\Program Files (x86)\NSIS\makensis.exe"
    if not os.path.exists(nsis_path):
        raise Exception("NSIS not found. Please install NSIS from https://nsis.sourceforge.io/")
    
    # Create the installer
    print("Creating installer with NSIS...")
    nsis_result = subprocess.run([nsis_path, "installer.nsi"], check=True)
    if nsis_result.returncode != 0:
        raise Exception("NSIS build failed")
    
    # Move the installer to the dist directory
    installer_name = "FactoryEye_Setup.exe"
    if os.path.exists(installer_name):
        shutil.move(installer_name, os.path.join("dist", installer_name))
    
    print(f"\nInstaller created successfully: {os.path.join('dist', installer_name)}")

if __name__ == "__main__":
    try:
        create_installer()
    except Exception as e:
        print(f"Error creating installer: {e}")