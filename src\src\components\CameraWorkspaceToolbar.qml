import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../constants/WindowsConstants.js" as WindowsConstants
import "./separators"

Rectangle {
    id: root
    Layout.fillWidth: true
    height: WindowsConstants.topBarHeight
    color: "darkgray"
    z: 2

    // Properties
    property string serialNumber
    property var drawingCanvas
    property int cameraspaceId
    property alias penButton: penBtn
    property alias lineButton: lineBtn
    property alias rectangleButton: rectBtn
    property alias circleButton: circleBtn
    property alias distanceButton: distanceBtn
    property alias textButton: textBtn
    property var cameraWindow

    // Signals
    signal drawingClicked
    signal cameraStreamStopped
    signal updateDrawingProperties

    // ButtonGroup to manage selection
    ButtonGroup {
        id: shapeButtonGroup
        exclusive: true
        buttons: [penBtn, lineBtn, rectBtn, circleBtn, distanceBtn, textBtn]

        onClicked: button => {
            if (button === checkedOldButton) {
                // If clicking the same button, uncheck it
                checkedOldButton = null
                button.checked = false
            } else {
                // If clicking a different button, update checkedButton
                checkedOldButton = button
            }
        }
    }

    RowLayout {
        anchors.centerIn: parent
        spacing: 5

        ToolButton {
            icon.source: "qrc:/resources/icons/camera_close.png"
            onClicked: {
                cameraStreamStopped();
            }
        }

        VerticalSeparator {
            height: root.height * 0.8
        }

        CheckableButton {
            id: penBtn
            icon.source: "qrc:/resources/icons/pen.png"
            ButtonGroup.group: shapeButtonGroup
        }

        CheckableButton {
            id: lineBtn
            icon.source: "qrc:/resources/icons/line.png"
            ButtonGroup.group: shapeButtonGroup
        }

        CheckableButton {
            id: rectBtn
            icon.source: "qrc:/resources/icons/rectangle.png"
            ButtonGroup.group: shapeButtonGroup
        }

        CheckableButton {
            id: circleBtn
            icon.source: "qrc:/resources/icons/circle.png"
            ButtonGroup.group: shapeButtonGroup
        }

        CheckableButton {
            id: distanceBtn
            icon.source: "qrc:/resources/icons/distance.png"
            ButtonGroup.group: shapeButtonGroup
        }

        CheckableButton {
            id: textBtn
            icon.source: "qrc:/resources/icons/text.png"
            ButtonGroup.group: shapeButtonGroup
        }

        VerticalSeparator {
            height: root.height * 0.8
        }

        ToolButton {
            icon.source: "qrc:/resources/icons/drawing_setting.png"
            onClicked: drawingClicked()
        }

        ToolButton {
            icon.source: "qrc:/resources/icons/backarrow.png"
            onClicked: {
                if (drawingCanvas.available && drawingCanvas.paths.length > 0) {
                    drawingCanvas.paths.pop();
                    drawingCanvas.paths = [...drawingCanvas.paths];
                    cameraWindow.updateDrawingProperties();
                    workspaceDatabase.remove_last_drawing(cameraspaceId);
                }
            }
        }

        ToolButton {
            icon.source: "qrc:/resources/icons/remove_b.png"
            onClicked: {
                if (drawingCanvas.available) {
                    drawingCanvas.paths = [];
                    cameraWindow.updateDrawingProperties();
                    workspaceDatabase.clear_drawings(cameraspaceId);
                }
            }
        }
    }
}
