import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

import "../constants/WindowsConstants.js" as WindowsConstants

Rectangle {
    width: WindowsConstants.subWindowWidth - 30
    height: 30
    color: "transparent"
    property string title: "Title"
    property real sliderValue: 1.0
    property real sliderFrom: 0.0
    property real sliderTo: 1.0
    property int extendRate: 255

    RowLayout {
        anchors.fill: parent
        Text {
            text: title
            horizontalAlignment: Text.AlignHCenter
        }
        Slider {
            id: redSlider
            Layout.fillWidth: true
            value: sliderValue
            from: sliderFrom
            to: sliderTo

            onValueChanged: {
                sliderValue = value;
            }
        }
        Text {
            horizontalAlignment: Text.AlignHCenter
            text: Math.round(redSlider.value * extendRate)
        }
    }
}
