[{"classes": [{"className": "QtColorButton", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "background<PERSON><PERSON><PERSON>ed", "read": "isBackgroundCheckered", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackground<PERSON>he<PERSON>ed"}], "qualifiedClassName": "QtColorButton", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "setColor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolButton"}]}], "inputFile": "qtcolorbutton_p.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>", "lineNumber": 21, "object": true, "qualifiedClassName": "qdesigner_internal::Buddy<PERSON><PERSON>or", "slots": [{"access": "public", "index": 0, "name": "updateBackground", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 1, "name": "widgetRemoved", "returnType": "void"}, {"access": "public", "index": 2, "name": "autoBuddy", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ConnectionEdit", "name": "ConnectionEdit"}]}], "inputFile": "buddyeditor.h", "outputRevision": 69}, {"classes": [{"className": "BuddyEditorPlugin", "interfaces": [[{"className": "QDesignerFormEditorPluginInterface", "id": "\"org.qt-project.Qt.Designer.QDesignerFormEditorPluginInterface\""}]], "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::BuddyEditorPlugin", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "activeFormWindowChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "addFormWindow", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "removeFormWindow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerFormEditorPluginInterface"}]}], "inputFile": "buddyeditor_plugin.h", "outputRevision": 69}, {"classes": [{"className": "BuddyEditorTool", "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::BuddyEditorTool", "superClasses": [{"access": "public", "name": "QDesignerFormWindowToolInterface"}]}], "inputFile": "buddyeditor_tool.h", "outputRevision": 69}, {"classes": [{"className": "ButtonGroupMenu", "lineNumber": 24, "object": true, "qualifiedClassName": "qdesigner_internal::ButtonGroupMenu", "slots": [{"access": "private", "index": 0, "name": "selectGroup", "returnType": "void"}, {"access": "private", "index": 1, "name": "breakGroup", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "ButtonGroupTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 53, "object": true, "qualifiedClassName": "qdesigner_internal::ButtonGroupTaskMenu", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}, {"className": "ButtonTaskMenu", "lineNumber": 71, "object": true, "qualifiedClassName": "qdesigner_internal::ButtonTaskMenu", "slots": [{"access": "private", "index": 0, "name": "createGroup", "returnType": "void"}, {"access": "private", "arguments": [{"name": "a", "type": "QAction*"}], "index": 1, "name": "addToGroup", "returnType": "void"}, {"access": "private", "index": 2, "name": "removeFromGroup", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}, {"className": "CommandLinkButtonTaskMenu", "lineNumber": 117, "object": true, "qualifiedClassName": "qdesigner_internal::CommandLinkButtonTaskMenu", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ButtonTaskMenu", "name": "ButtonTaskMenu"}]}], "inputFile": "button_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "ComboBoxTaskMenu", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::ComboBoxTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editItems", "returnType": "void"}, {"access": "private", "index": 1, "name": "updateSelection", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "combobox_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "ContainerWidgetTaskMenu", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::ContainerWidgetTaskMenu", "slots": [{"access": "private", "index": 0, "name": "removeCurrentPage", "returnType": "void"}, {"access": "private", "index": 1, "name": "addPage", "returnType": "void"}, {"access": "private", "index": 2, "name": "addPageAfter", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}, {"className": "WizardContainerWidgetTaskMenu", "lineNumber": 72, "object": true, "qualifiedClassName": "qdesigner_internal::WizardContainerWidgetTaskMenu", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ContainerWidgetTaskMenu", "name": "ContainerWidgetTaskMenu"}]}, {"className": "MdiContainerWidgetTaskMenu", "lineNumber": 87, "object": true, "qualifiedClassName": "qdesigner_internal::MdiContainerWidgetTaskMenu", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ContainerWidgetTaskMenu", "name": "ContainerWidgetTaskMenu"}]}, {"className": "ContainerWidgetTaskMenuFactory", "lineNumber": 102, "object": true, "qualifiedClassName": "qdesigner_internal::ContainerWidgetTaskMenuFactory", "superClasses": [{"access": "public", "name": "QExtensionFactory"}]}], "inputFile": "containerwidget_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "GroupBoxTaskMenu", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::GroupBoxTaskMenu", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "groupbox_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "InPlaceEditor", "lineNumber": 22, "object": true, "qualifiedClassName": "qdesigner_internal::InPlaceEditor", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::TextPropertyEditor", "name": "TextPropertyEditor"}]}, {"className": "TaskMenuInlineEditor", "lineNumber": 40, "object": true, "qualifiedClassName": "qdesigner_internal::TaskMenuInlineEditor", "slots": [{"access": "public", "index": 0, "name": "editText", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 1, "name": "updateText", "returnType": "void"}, {"access": "private", "index": 2, "name": "updateSelection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "inplace_editor.h", "outputRevision": 69}, {"classes": [{"className": "InPlaceWidgetHelper", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::InPlaceWidgetHelper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "inplace_widget_helper.h", "outputRevision": 69}, {"classes": [{"className": "AbstractItemEditor", "lineNumber": 40, "object": true, "qualifiedClassName": "qdesigner_internal::AbstractItemEditor", "slots": [{"access": "public", "index": 0, "name": "cacheReloaded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 1, "name": "propertyChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 2, "name": "resetProperty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "ItemListEditor", "lineNumber": 87, "object": true, "qualifiedClassName": "qdesigner_internal::ItemListEditor", "signals": [{"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 0, "name": "indexChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}, {"name": "role", "type": "int"}, {"name": "v", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "itemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 2, "name": "itemInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 3, "name": "itemDeleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 4, "name": "itemMovedUp", "returnType": "void"}, {"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 5, "name": "itemMovedDown", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "newListItemButtonClicked", "returnType": "void"}, {"access": "private", "index": 7, "name": "deleteListItemButtonClicked", "returnType": "void"}, {"access": "private", "index": 8, "name": "moveListItemUpButtonClicked", "returnType": "void"}, {"access": "private", "index": 9, "name": "moveListItemDownButtonClicked", "returnType": "void"}, {"access": "private", "index": 10, "name": "listWidgetCurrentRowChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 11, "name": "listWidgetItemChanged", "returnType": "void"}, {"access": "private", "index": 12, "name": "toggle<PERSON><PERSON><PERSON><PERSON><PERSON>er", "returnType": "void"}, {"access": "private", "index": 13, "name": "cacheReloaded", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::AbstractItemEditor", "name": "AbstractItemEditor"}]}], "inputFile": "itemlisteditor.h", "outputRevision": 69}, {"classes": [{"className": "LabelTaskMenu", "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::LabelTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editRichText", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "label_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "LayoutWidgetTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 21, "object": true, "qualifiedClassName": "LayoutWidgetTaskMenu", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}, {"className": "SpacerTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 38, "object": true, "qualifiedClassName": "SpacerTaskMenu", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}], "inputFile": "layouttaskmenu.h", "outputRevision": 69}, {"classes": [{"className": "LineEditTaskMenu", "lineNumber": 17, "object": true, "qualifiedClassName": "qdesigner_internal::LineEditTaskMenu", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "lineedit_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "ListWidgetTaskMenu", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::ListWidgetTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editItems", "returnType": "void"}, {"access": "private", "index": 1, "name": "updateSelection", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "listwidget_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "ListWidgetEditor", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::ListWidgetEditor", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "listwidgeteditor.h", "outputRevision": 69}, {"classes": [{"className": "MenuTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 25, "object": true, "qualifiedClassName": "qdesigner_internal::MenuTaskMenu", "slots": [{"access": "private", "index": 0, "name": "removeMenu", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}, {"className": "MenuBarTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 47, "object": true, "qualifiedClassName": "qdesigner_internal::MenuBarTaskMenu", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}], "inputFile": "menutaskmenu.h", "outputRevision": 69}, {"classes": [{"className": "TableWidgetTaskMenu", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::TableWidgetTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editItems", "returnType": "void"}, {"access": "private", "index": 1, "name": "updateSelection", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "tablewidget_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "TableWidgetEditor", "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::TableWidgetEditor", "slots": [{"access": "private", "arguments": [{"name": "currentRow", "type": "int"}, {"name": "currentCol", "type": "int"}], "index": 0, "name": "tableWidgetCurrentCellChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QTableWidgetItem*"}], "index": 1, "name": "tableWidgetItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 2, "name": "columnEditorIndexChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}, {"name": "role", "type": "int"}, {"name": "v", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "name": "columnEditorItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 4, "name": "columnEditorItemInserted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 5, "name": "columnEditorItemDeleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 6, "name": "columnEditorItemMovedUp", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 7, "name": "columnEditorItemMovedDown", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 8, "name": "rowEditorIndexChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}, {"name": "role", "type": "int"}, {"name": "v", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 9, "name": "rowEditorItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 10, "name": "rowEditorItemInserted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 11, "name": "rowEditorItemDeleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 12, "name": "rowEditorItemMovedUp", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 13, "name": "rowEditorItemMovedDown", "returnType": "void"}, {"access": "private", "index": 14, "name": "toggle<PERSON><PERSON><PERSON><PERSON><PERSON>er", "returnType": "void"}, {"access": "private", "index": 15, "name": "cacheReloaded", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::AbstractItemEditor", "name": "AbstractItemEditor"}]}, {"className": "TableWidgetEditorDialog", "lineNumber": 76, "object": true, "qualifiedClassName": "qdesigner_internal::TableWidgetEditorDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "tablewidgeteditor.h", "outputRevision": 69}, {"classes": [{"className": "TaskMenuComponent", "lineNumber": 18, "object": true, "qualifiedClassName": "qdesigner_internal::TaskMenuComponent", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "taskmenu_component.h", "outputRevision": 69}, {"classes": [{"className": "TextEditTaskMenu", "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::TextEditTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editText", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "textedit_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "ToolBarTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::ToolBarTaskMenu", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}, {"className": "StatusBarTaskMenu", "interfaces": [[{"className": "QDesignerTaskMenuExtension", "id": "\"org.qt-project.Qt.Designer.TaskMenu\""}]], "lineNumber": 35, "object": true, "qualifiedClassName": "qdesigner_internal::StatusBarTaskMenu", "slots": [{"access": "private", "index": 0, "name": "removeStatusBar", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerTaskMenuExtension"}]}], "inputFile": "toolbar_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "TreeWidgetTaskMenu", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::TreeWidgetTaskMenu", "slots": [{"access": "private", "index": 0, "name": "editItems", "returnType": "void"}, {"access": "private", "index": 1, "name": "updateSelection", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerTaskMenu", "name": "QDesignerTaskMenu"}]}], "inputFile": "treewidget_taskmenu.h", "outputRevision": 69}, {"classes": [{"className": "TreeWidgetEditor", "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::TreeWidgetEditor", "slots": [{"access": "private", "index": 0, "name": "newItemButtonClicked", "returnType": "void"}, {"access": "private", "index": 1, "name": "newSubItemButtonClicked", "returnType": "void"}, {"access": "private", "index": 2, "name": "deleteItemButtonClicked", "returnType": "void"}, {"access": "private", "index": 3, "name": "moveItemUpButtonClicked", "returnType": "void"}, {"access": "private", "index": 4, "name": "moveItemDownButtonClicked", "returnType": "void"}, {"access": "private", "index": 5, "name": "moveItemRightButtonClicked", "returnType": "void"}, {"access": "private", "index": 6, "name": "moveItemLeftButtonClicked", "returnType": "void"}, {"access": "private", "index": 7, "name": "treeWidgetCurrentItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QTreeWidgetItem*"}, {"name": "column", "type": "int"}], "index": 8, "name": "treeWidgetItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 9, "name": "columnEditorIndexChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}, {"name": "role", "type": "int"}, {"name": "v", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 10, "name": "columnEditorItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 11, "name": "columnEditorItemInserted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 12, "name": "columnEditorItemDeleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 13, "name": "columnEditorItemMovedUp", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 14, "name": "columnEditorItemMovedDown", "returnType": "void"}, {"access": "private", "index": 15, "name": "toggle<PERSON><PERSON><PERSON><PERSON><PERSON>er", "returnType": "void"}, {"access": "private", "index": 16, "name": "cacheReloaded", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::AbstractItemEditor", "name": "AbstractItemEditor"}]}, {"className": "TreeWidgetEditorDialog", "lineNumber": 75, "object": true, "qualifiedClassName": "qdesigner_internal::TreeWidgetEditorDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "treewidgeteditor.h", "outputRevision": 69}, {"classes": [{"className": "WidgetBox", "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetBox", "slots": [{"access": "private", "arguments": [{"name": "name", "type": "QString"}, {"name": "xml", "type": "QString"}, {"name": "global_mouse_pos", "type": "QPoint"}], "index": 0, "name": "handleMousePress", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerWidgetBox", "name": "QDesignerWidgetBox"}]}], "inputFile": "widgetbox.h", "outputRevision": 69}, {"classes": [{"className": "WidgetBoxCategoryListView", "lineNumber": 25, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetBoxCategoryListView", "signals": [{"access": "public", "index": 0, "name": "scratchPadChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "xml", "type": "QString"}, {"name": "globalPos", "type": "QPoint"}], "index": 1, "name": "widgetBoxPressed", "returnType": "void"}, {"access": "public", "index": 2, "name": "itemRemoved", "returnType": "void"}, {"access": "public", "index": 3, "name": "lastItemRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "needle", "type": "QString"}, {"name": "caseSensitivity", "type": "Qt::CaseSensitivity"}], "index": 4, "name": "filter", "returnType": "void"}, {"access": "public", "index": 5, "name": "removeCurrentItem", "returnType": "void"}, {"access": "public", "index": 6, "name": "editCurrentItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 7, "name": "slotPressed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QListView"}]}], "inputFile": "widgetboxcategorylistview.h", "outputRevision": 69}, {"classes": [{"className": "WidgetBoxTreeWidget", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetBoxTreeWidget", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "dom_xml", "type": "QString"}, {"name": "global_mouse_pos", "type": "QPoint"}], "index": 0, "name": "widgetBoxPressed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"type": "QString"}], "index": 1, "name": "filter", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotSave", "returnType": "void"}, {"access": "private", "index": 3, "name": "slotScratchPadItemDeleted", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotLastScratchPadItemDeleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QTreeWidgetItem*"}], "index": 5, "name": "handleMousePress", "returnType": "void"}, {"access": "private", "index": 6, "name": "deleteScratchpad", "returnType": "void"}, {"access": "private", "index": 7, "name": "slotListMode", "returnType": "void"}, {"access": "private", "index": 8, "name": "slotIconMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTreeWidget"}]}], "inputFile": "widgetboxtreewidget.h", "outputRevision": 69}, {"classes": [{"className": "QToolBarActionProvider", "interfaces": [[{"className": "QDesignerActionProviderExtension", "id": "\"org.qt-project.Qt.Designer.ActionProvider\""}]], "lineNumber": 37, "object": true, "qualifiedClassName": "qdesigner_internal::QToolBarActionProvider", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "ActionProviderBase"}]}, {"className": "QMenuBarActionProvider", "interfaces": [[{"className": "QDesignerActionProviderExtension", "id": "\"org.qt-project.Qt.Designer.ActionProvider\""}]], "lineNumber": 55, "object": true, "qualifiedClassName": "qdesigner_internal::QMenuBarActionProvider", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "ActionProviderBase"}]}, {"className": "QMenuActionProvider", "interfaces": [[{"className": "QDesignerActionProviderExtension", "id": "\"org.qt-project.Qt.Designer.ActionProvider\""}]], "lineNumber": 70, "object": true, "qualifiedClassName": "qdesigner_internal::QMenuActionProvider", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "ActionProviderBase"}]}], "inputFile": "default_actionprovider.h", "outputRevision": 69}, {"classes": [{"className": "QStackedWidgetContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 22, "object": true, "qualifiedClassName": "qdesigner_internal::QStackedWidgetContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}, {"className": "QTabWidgetContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 46, "object": true, "qualifiedClassName": "qdesigner_internal::QTabWidgetContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}, {"className": "QToolBoxContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 70, "object": true, "qualifiedClassName": "qdesigner_internal::QToolBoxContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}, {"className": "QScrollAreaContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 158, "object": true, "qualifiedClassName": "qdesigner_internal::QScrollAreaContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "SingleChildContainer<QScrollArea>"}]}, {"className": "QDockWidgetContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 167, "object": true, "qualifiedClassName": "qdesigner_internal::QDockWidgetContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "SingleChildContainer<QDockWidget>"}]}], "inputFile": "default_container.h", "outputRevision": 69}, {"classes": [{"className": "QDesignerLayoutDecorationFactory", "interfaces": [[{"className": "QAbstractExtensionFactory", "id": "\"org.qt-project.Qt.QAbstractExtensionFactory\""}]], "lineNumber": 16, "object": true, "qualifiedClassName": "qdesigner_internal::QDesignerLayoutDecorationFactory", "superClasses": [{"access": "public", "name": "QExtensionFactory"}]}], "inputFile": "default_layoutdecoration.h", "outputRevision": 69}, {"classes": [{"className": "DeviceProfileDialog", "lineNumber": 37, "object": true, "qualifiedClassName": "qdesigner_internal::DeviceProfileDialog", "slots": [{"access": "private", "arguments": [{"type": "bool"}], "index": 0, "name": "setOkButtonEnabled", "returnType": "void"}, {"access": "private", "arguments": [{"name": "name", "type": "QString"}], "index": 1, "name": "nameChanged", "returnType": "void"}, {"access": "private", "index": 2, "name": "save", "returnType": "void"}, {"access": "private", "index": 3, "name": "open", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "deviceprofiledialog.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON>_<PERSON>r", "lineNumber": 30, "object": true, "qualifiedClassName": "qdesigner_internal::<PERSON><PERSON>_<PERSON>oser", "slots": [{"access": "private", "index": 0, "name": "syncSpinBoxes", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "dpi_chooser.h", "outputRevision": 69}, {"classes": [{"className": "EmbeddedOptionsControl", "lineNumber": 21, "object": true, "qualifiedClassName": "qdesigner_internal::EmbeddedOptionsControl", "slots": [{"access": "public", "index": 0, "name": "loadSettings", "returnType": "void"}, {"access": "public", "index": 1, "name": "saveSettings", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotAdd", "returnType": "void"}, {"access": "private", "index": 3, "name": "slotEdit", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotDelete", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 5, "name": "slotProfileIndexChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "embeddedoptionspage.h", "outputRevision": 69}, {"classes": [{"className": "FormEditor", "lineNumber": 17, "object": true, "qualifiedClassName": "qdesigner_internal::FormEditor", "slots": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 0, "name": "slotQrcFileChangedExternally", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDesignerFormEditorInterface"}]}], "inputFile": "formeditor.h", "outputRevision": 69}, {"classes": [{"className": "FormWindow", "lineNumber": 44, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindow", "signals": [{"access": "public", "arguments": [{"name": "menu", "type": "QMenu*"}, {"name": "widget", "type": "QWidget*"}], "index": 0, "name": "contextMenuRequested", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "deleteWidgets", "returnType": "void"}, {"access": "public", "index": 2, "name": "raiseWidgets", "returnType": "void"}, {"access": "public", "index": 3, "name": "lowerWidgets", "returnType": "void"}, {"access": "public", "index": 4, "name": "copy", "returnType": "void"}, {"access": "public", "index": 5, "name": "cut", "returnType": "void"}, {"access": "public", "index": 6, "name": "paste", "returnType": "void"}, {"access": "public", "index": 7, "name": "selectAll", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}, {"name": "container", "type": "QWidget*"}], "index": 8, "name": "createLayout", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}], "index": 9, "isCloned": true, "name": "createLayout", "returnType": "void"}, {"access": "public", "arguments": [{"name": "container", "type": "QWidget*"}, {"name": "newType", "type": "int"}], "index": 10, "name": "morphLayout", "returnType": "void"}, {"access": "public", "arguments": [{"name": "w", "type": "QWidget*"}], "index": 11, "name": "breakLayout", "returnType": "void"}, {"access": "public", "index": 12, "name": "editContents", "returnType": "void"}, {"access": "private", "index": 13, "name": "selectionChangedTimerDone", "returnType": "void"}, {"access": "private", "index": 14, "name": "checkSelection", "returnType": "void"}, {"access": "private", "index": 15, "name": "checkSelectionNow", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAction*"}], "index": 16, "name": "slotSelectWidget", "returnType": "void"}, {"access": "private", "arguments": [{"type": "bool"}], "index": 17, "name": "slotCleanChanged", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::FormWindowBase", "name": "FormWindowBase"}]}], "inputFile": "formwindow.h", "outputRevision": 69}, {"classes": [{"className": "FormWindowWidgetStack", "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindowWidgetStack", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 0, "name": "currentToolChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "tool", "type": "QDesignerFormWindowToolInterface*"}], "index": 1, "name": "addTool", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tool", "type": "QDesignerFormWindowToolInterface*"}], "index": 2, "name": "setCurrentTool", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 3, "name": "setCurrentTool", "returnType": "void"}, {"access": "public", "index": 4, "name": "setSenderAsCurrentTool", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "formwindow_widgetstack.h", "outputRevision": 69}, {"classes": [{"className": "FormWindowCursor", "lineNumber": 17, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindowCursor", "slots": [{"access": "public", "index": 0, "name": "update", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerFormWindowCursorInterface"}]}], "inputFile": "formwindowcursor.h", "outputRevision": 69}, {"classes": [{"className": "FormWindowManager", "lineNumber": 31, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindowManager", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "addFormWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "removeFormWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "setActiveFormWindow", "returnType": "void"}, {"access": "public", "index": 3, "name": "closeAllPreviews", "returnType": "void"}, {"access": "public", "index": 4, "name": "deviceProfilesChanged", "returnType": "void"}, {"access": "private", "index": 5, "name": "slotActionCutActivated", "returnType": "void"}, {"access": "private", "index": 6, "name": "slotActionCopyActivated", "returnType": "void"}, {"access": "private", "index": 7, "name": "slotActionPasteActivated", "returnType": "void"}, {"access": "private", "index": 8, "name": "slotActionDeleteActivated", "returnType": "void"}, {"access": "private", "index": 9, "name": "slotActionSelectAllActivated", "returnType": "void"}, {"access": "private", "index": 10, "name": "slotActionLowerActivated", "returnType": "void"}, {"access": "private", "index": 11, "name": "slotActionRaiseActivated", "returnType": "void"}, {"access": "private", "index": 12, "name": "createLayout", "returnType": "void"}, {"access": "private", "index": 13, "name": "slotActionBreakLayoutActivated", "returnType": "void"}, {"access": "private", "index": 14, "name": "slotActionAdjustSizeActivated", "returnType": "void"}, {"access": "private", "index": 15, "name": "slotActionSimplifyLayoutActivated", "returnType": "void"}, {"access": "private", "index": 16, "name": "showPreview", "returnType": "void"}, {"access": "private", "arguments": [{"name": "style", "type": "QString"}, {"name": "deviceProfileIndex", "type": "int"}], "index": 17, "name": "slotActionGroupPreviewInStyle", "returnType": "void"}, {"access": "private", "index": 18, "name": "slotActionShowFormWindowSettingsDialog", "returnType": "void"}, {"access": "private", "index": 19, "name": "slotUpdateActions", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerFormWindowManager", "name": "QDesignerFormWindowManager"}]}], "inputFile": "formwindowmanager.h", "outputRevision": 69}, {"classes": [{"className": "FormWindowSettings", "lineNumber": 25, "object": true, "qualifiedClassName": "qdesigner_internal::FormWindowSettings", "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "formwindowsettings.h", "outputRevision": 69}, {"classes": [{"className": "ItemViewPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::ItemViewPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "itemview_propertysheet.h", "outputRevision": 69}, {"classes": [{"className": "LayoutPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::LayoutPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "layout_propertysheet.h", "outputRevision": 69}, {"classes": [{"className": "LinePropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 15, "object": true, "qualifiedClassName": "qdesigner_internal::LinePropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "line_propertysheet.h", "outputRevision": 69}, {"classes": [{"className": "PreviewActionGroup", "lineNumber": 29, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewActionGroup", "signals": [{"access": "public", "arguments": [{"name": "style", "type": "QString"}, {"name": "deviceProfileIndex", "type": "int"}], "index": 0, "name": "preview", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "updateDeviceProfiles", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QAction*"}], "index": 2, "name": "slotTriggered", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QActionGroup"}]}], "inputFile": "previewactiongroup.h", "outputRevision": 69}, {"classes": [{"className": "QLayoutWidgetPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 15, "object": true, "qualifiedClassName": "qdesigner_internal::QLayoutWidgetPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "qlayoutwidget_propertysheet.h", "outputRevision": 69}, {"classes": [{"className": "QMainWindowContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 18, "object": true, "qualifiedClassName": "qdesigner_internal::QMainWindowContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}], "inputFile": "qmainwindow_container.h", "outputRevision": 69}, {"classes": [{"className": "QMdiAreaContainer", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 20, "object": true, "qualifiedClassName": "qdesigner_internal::QMdiAreaContainer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}, {"className": "QMdiAreaPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 46, "object": true, "qualifiedClassName": "qdesigner_internal::QMdiAreaPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "qmdiarea_container.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interfaces": [[{"className": "QDesignerContainerExtension", "id": "\"org.qt-project.Qt.Designer.Container\""}]], "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::Q<PERSON><PERSON><PERSON>Container", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerContainerExtension"}]}, {"className": "QWizardPagePropertySheet", "lineNumber": 49, "object": true, "qualifiedClassName": "qdesigner_internal::QWizardPagePropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}, {"className": "QWizardPropertySheet", "lineNumber": 67, "object": true, "qualifiedClassName": "qdesigner_internal::QWizardPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "qwizard_container.h", "outputRevision": 69}, {"classes": [{"className": "SpacerPropertySheet", "interfaces": [[{"className": "QDesignerPropertySheetExtension", "id": "\"org.qt-project.Qt.Designer.PropertySheet\""}]], "lineNumber": 15, "object": true, "qualifiedClassName": "qdesigner_internal::SpacerPropertySheet", "superClasses": [{"access": "public", "name": "QDesignerPropertySheet"}]}], "inputFile": "spacer_propertysheet.h", "outputRevision": 69}, {"classes": [{"className": "TemplateOptionsWidget", "lineNumber": 26, "object": true, "qualifiedClassName": "qdesigner_internal::TemplateOptionsWidget", "slots": [{"access": "private", "index": 0, "name": "addTemplatePath", "returnType": "void"}, {"access": "private", "index": 1, "name": "removeTemplatePath", "returnType": "void"}, {"access": "private", "index": 2, "name": "templatePathSelectionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "templateoptionspage.h", "outputRevision": 69}, {"classes": [{"className": "WidgetEditorTool", "lineNumber": 22, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetEditorTool", "superClasses": [{"access": "public", "name": "QDesignerFormWindowToolInterface"}]}], "inputFile": "tool_widgeteditor.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 24, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetHandle", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::InvisibleWidget", "name": "InvisibleWidget"}]}, {"className": "WidgetSelection", "lineNumber": 73, "object": true, "qualifiedClassName": "qdesigner_internal::WidgetSelection", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "widgetselection.h", "outputRevision": 69}, {"classes": [{"className": "ObjectInspector", "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::ObjectInspector", "slots": [{"access": "public", "index": 0, "name": "mainContainerChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "selected", "type": "QItemSelection"}, {"name": "deselected", "type": "QItemSelection"}], "index": 1, "name": "slotSelectionChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "pos", "type": "QPoint"}], "index": 2, "name": "slotPopupContextMenu", "returnType": "void"}, {"access": "private", "arguments": [{"name": "column", "type": "int"}], "index": 3, "name": "slotHeaderDoubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerObjectInspector", "name": "QDesignerObjectInspector"}]}], "inputFile": "objectinspector.h", "outputRevision": 69}, {"classes": [{"className": "TabOrderEditor", "lineNumber": 22, "object": true, "qualifiedClassName": "qdesigner_internal::TabOrderEditor", "slots": [{"access": "public", "arguments": [{"name": "background", "type": "QWidget*"}], "index": 0, "name": "setBackground", "returnType": "void"}, {"access": "public", "index": 1, "name": "updateBackground", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QWidget*"}], "index": 2, "name": "widgetRemoved", "returnType": "void"}, {"access": "public", "index": 3, "name": "initTabOrder", "returnType": "void"}, {"access": "private", "index": 4, "name": "showTabOrderDialog", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "tabordereditor.h", "outputRevision": 69}, {"classes": [{"className": "TabOrderEditorTool", "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::TabOrderEditorTool", "superClasses": [{"access": "public", "name": "QDesignerFormWindowToolInterface"}]}], "inputFile": "tabordereditor_tool.h", "outputRevision": 69}, {"classes": [{"className": "DesignerPropertyManager", "lineNumber": 70, "object": true, "qualifiedClassName": "qdesigner_internal::DesignerPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "enableSubPropertyHandling", "type": "bool"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "attribute", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "setAttribute", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "name": "slotValueChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 4, "name": "slotPropertyDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtVariantPropertyManager"}]}, {"className": "DesignerEditorFactory", "lineNumber": 203, "object": true, "qualifiedClassName": "qdesigner_internal::DesignerEditorFactory", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 0, "name": "resetProperty", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 1, "name": "slotEditorDestroyed", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "attribute", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "slotAttributeChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 3, "name": "slotPropertyChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 4, "name": "slotValueChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 5, "name": "slotStringTextChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QKeySequence"}], "index": 6, "name": "slotKeySequenceChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QPalette"}], "index": 7, "name": "slotPaletteChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 8, "name": "slotPixmapChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 9, "name": "slotIconChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 10, "name": "slotIconThemeChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "int"}], "index": 11, "name": "slotIconThemeEnumChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 12, "name": "slotUintChanged", "returnType": "void"}, {"access": "private", "arguments": [{"type": "int"}], "index": 13, "name": "slotIntChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 14, "name": "slotLongLongChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 15, "name": "slotULongLongChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 16, "name": "slotUrlChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QString"}], "index": 17, "name": "slotByteArrayChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "value", "type": "QStringList"}], "index": 18, "name": "slotStringListChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtVariantEditorFactory"}]}], "inputFile": "designerpropertymanager.h", "outputRevision": 69}, {"classes": [{"className": "NewDynamicPropertyDialog", "lineNumber": 34, "object": true, "qualifiedClassName": "qdesigner_internal::NewDynamicPropertyDialog", "slots": [{"access": "private", "arguments": [{"name": "btn", "type": "QAbstractButton*"}], "index": 0, "name": "buttonBoxClicked", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QString"}], "index": 1, "name": "nameChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "newdynamicpropertydialog.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 21, "object": true, "qualifiedClassName": "qdesigner_internal::<PERSON><PERSON><PERSON><PERSON><PERSON>", "slots": [{"access": "private", "index": 0, "name": "buildButtonColorChanged", "returnType": "void"}, {"access": "private", "index": 1, "name": "activeRadioClicked", "returnType": "void"}, {"access": "private", "index": 2, "name": "inactiveRadioClicked", "returnType": "void"}, {"access": "private", "index": 3, "name": "disabledRadioClicked", "returnType": "void"}, {"access": "private", "index": 4, "name": "computeRadioClicked", "returnType": "void"}, {"access": "private", "index": 5, "name": "detailsRadioClicked", "returnType": "void"}, {"access": "private", "arguments": [{"name": "palette", "type": "QPalette"}], "index": 6, "name": "paletteChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "pos", "type": "QPoint"}], "index": 7, "name": "viewContextMenuRequested", "returnType": "void"}, {"access": "private", "index": 8, "name": "save", "returnType": "void"}, {"access": "private", "index": 9, "name": "load", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 77, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "colorRole", "read": "colorRole", "required": false, "scriptable": true, "stored": true, "type": "QPalette::ColorRole", "user": false}], "qualifiedClassName": "qdesigner_internal::<PERSON><PERSON><PERSON><PERSON>l", "signals": [{"access": "public", "arguments": [{"name": "palette", "type": "QPalette"}], "index": 0, "name": "paletteChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractTableModel"}]}, {"className": "BrushEditor", "lineNumber": 122, "object": true, "qualifiedClassName": "qdesigner_internal::BrushEditor", "signals": [{"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 0, "name": "changed", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "brushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "RoleEditor", "lineNumber": 141, "object": true, "qualifiedClassName": "qdesigner_internal::RoleEditor", "signals": [{"access": "public", "arguments": [{"name": "widget", "type": "QWidget*"}], "index": 0, "name": "changed", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "emitResetProperty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "ColorDelegate", "lineNumber": 159, "object": true, "qualifiedClassName": "qdesigner_internal::ColorDelegate", "superClasses": [{"access": "public", "name": "QStyledItemDelegate"}]}], "inputFile": "paletteeditor.h", "outputRevision": 69}, {"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON>or<PERSON><PERSON><PERSON>", "lineNumber": 18, "object": true, "qualifiedClassName": "qdesigner_internal::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "palette", "type": "QPalette"}], "index": 0, "name": "paletteChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "palette", "type": "QPalette"}], "index": 1, "name": "setPalette", "returnType": "void"}, {"access": "private", "index": 2, "name": "showPaletteEditor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolButton"}]}], "inputFile": "paletteeditorbutton.h", "outputRevision": 69}, {"classes": [{"className": "IconThemeDialog", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::IconThemeDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "IconThemeEnumDialog", "lineNumber": 38, "object": true, "qualifiedClassName": "qdesigner_internal::IconThemeEnumDialog", "superClasses": [{"access": "public", "name": "QDialog"}]}, {"className": "PixmapEditor", "lineNumber": 49, "object": true, "qualifiedClassName": "qdesigner_internal::PixmapEditor", "signals": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 0, "name": "pathChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "themeEnum", "type": "int"}], "index": 1, "name": "themeEnumChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "QString"}], "index": 2, "name": "themeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "path", "type": "QString"}], "index": 3, "name": "set<PERSON>ath", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "QString"}], "index": 4, "name": "setTheme", "returnType": "void"}, {"access": "public", "arguments": [{"name": "e", "type": "int"}], "index": 5, "name": "setThemeEnum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixmap", "type": "QPixmap"}], "index": 6, "name": "setDefaultPixmap", "returnType": "void"}, {"access": "public", "arguments": [{"name": "icon", "type": "QIcon"}], "index": 7, "name": "setDefaultPixmapIcon", "returnType": "void"}, {"access": "private", "index": 8, "name": "defaultActionActivated", "returnType": "void"}, {"access": "private", "index": 9, "name": "resourceActionActivated", "returnType": "void"}, {"access": "private", "index": 10, "name": "fileActionActivated", "returnType": "void"}, {"access": "private", "index": 11, "name": "themeEnumActionActivated", "returnType": "void"}, {"access": "private", "index": 12, "name": "themeActionActivated", "returnType": "void"}, {"access": "private", "index": 13, "name": "copyActionActivated", "returnType": "void"}, {"access": "private", "index": 14, "name": "pasteActionActivated", "returnType": "void"}, {"access": "private", "index": 15, "name": "clipboardDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "pixmapeditor.h", "outputRevision": 69}, {"classes": [{"className": "PreviewFrame", "lineNumber": 17, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewFrame", "superClasses": [{"access": "public", "name": "QFrame"}]}], "inputFile": "previewframe.h", "outputRevision": 69}, {"classes": [{"className": "PreviewWidget", "lineNumber": 13, "object": true, "qualifiedClassName": "qdesigner_internal::PreviewWidget", "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "previewwidget.h", "outputRevision": 69}, {"classes": [{"className": "PropertyEditor", "lineNumber": 37, "object": true, "qualifiedClassName": "qdesigner_internal::PropertyEditor", "slots": [{"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 0, "name": "slotResetProperty", "returnType": "void"}, {"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "enableSubPropertyHandling", "type": "bool"}], "index": 1, "name": "slotValueChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "action", "type": "QAction*"}], "index": 2, "name": "slotViewTriggered", "returnType": "void"}, {"access": "private", "arguments": [{"name": "action", "type": "QAction*"}], "index": 3, "name": "slotAddDynamicProperty", "returnType": "void"}, {"access": "private", "index": 4, "name": "slotRemoveDynamicProperty", "returnType": "void"}, {"access": "private", "arguments": [{"name": "sort", "type": "bool"}], "index": 5, "name": "slotSorting", "returnType": "void"}, {"access": "private", "arguments": [{"name": "color", "type": "bool"}], "index": 6, "name": "slotColoring", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QtBrowserItem*"}], "index": 7, "name": "slotCurrentItemChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "pattern", "type": "QString"}], "index": 8, "name": "setFilter", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::QDesignerPropertyEditor", "name": "QDesignerPropertyEditor"}]}], "inputFile": "propertyeditor.h", "outputRevision": 69}, {"classes": [{"className": "QLongLongValidator", "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "qlonglong", "user": false, "write": "setBottom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "qlonglong", "user": false, "write": "setTop"}], "qualifiedClassName": "qdesigner_internal::QLongLongValidator", "superClasses": [{"access": "public", "name": "QValidator"}]}, {"className": "QULongLongValidator", "lineNumber": 41, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "qulonglong", "user": false, "write": "setBottom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "qulonglong", "user": false, "write": "setTop"}], "qualifiedClassName": "qdesigner_internal::QULongLongValidator", "superClasses": [{"access": "public", "name": "QValidator"}]}], "inputFile": "qlonglongvalidator.h", "outputRevision": 69}, {"classes": [{"className": "ResetWidget", "lineNumber": 24, "object": true, "qualifiedClassName": "qdesigner_internal::ResetWidget", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 0, "name": "resetProperty", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "slotClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "ResetDecorator", "lineNumber": 50, "object": true, "qualifiedClassName": "qdesigner_internal::ResetDecorator", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 0, "name": "resetProperty", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 1, "name": "slotPropertyChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 2, "name": "slotEditorDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "resetdecorator.h", "outputRevision": 69}, {"classes": [{"className": "StringListEditor", "lineNumber": 15, "object": true, "qualifiedClassName": "qdesigner_internal::StringListEditor", "slots": [{"access": "private", "index": 0, "name": "upButtonClicked", "returnType": "void"}, {"access": "private", "index": 1, "name": "downButtonClicked", "returnType": "void"}, {"access": "private", "index": 2, "name": "newButtonClicked", "returnType": "void"}, {"access": "private", "index": 3, "name": "deleteButtonClicked", "returnType": "void"}, {"access": "private", "arguments": [{"name": "text", "type": "QString"}], "index": 4, "name": "valueEdited", "returnType": "void"}, {"access": "private", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 5, "name": "currentIndexChanged", "returnType": "void"}, {"access": "private", "index": 6, "name": "currentValueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}, {"access": "private", "name": "Ui::<PERSON><PERSON>"}]}], "inputFile": "stringlisteditor.h", "outputRevision": 69}, {"classes": [{"className": "StringListEditorButton", "lineNumber": 16, "object": true, "qualifiedClassName": "qdesigner_internal::StringListEditorButton", "signals": [{"access": "public", "arguments": [{"name": "stringList", "type": "QStringList"}], "index": 0, "name": "stringListChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "stringList", "type": "QStringList"}], "index": 1, "name": "setStringList", "returnType": "void"}, {"access": "private", "index": 2, "name": "showStringListEditor", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolButton"}]}], "inputFile": "stringlisteditorbutton.h", "outputRevision": 69}, {"classes": [{"className": "TextEditor", "lineNumber": 29, "object": true, "qualifiedClassName": "qdesigner_internal::TextEditor", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "textChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 1, "name": "setText", "returnType": "void"}, {"access": "private", "index": 2, "name": "buttonClicked", "returnType": "void"}, {"access": "private", "index": 3, "name": "resourceActionActivated", "returnType": "void"}, {"access": "private", "index": 4, "name": "fileActionActivated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "texteditor.h", "outputRevision": 69}, {"classes": [{"className": "QtBoolEdit", "lineNumber": 68, "object": true, "qualifiedClassName": "QtBoolEdit", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "toggled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtpropertybrowserutils_p.h", "outputRevision": 69}, {"classes": [{"className": "SignalSlotEditor", "lineNumber": 19, "object": true, "qualifiedClassName": "qdesigner_internal::SignalSlotEditor", "superClasses": [{"access": "public", "fullyQualifiedName": "qdesigner_internal::ConnectionEdit", "name": "ConnectionEdit"}]}], "inputFile": "signalsloteditor.h", "outputRevision": 69}, {"classes": [{"className": "ConnectionModel", "lineNumber": 63, "object": true, "qualifiedClassName": "qdesigner_internal::ConnectionModel", "slots": [{"access": "private", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 0, "name": "connectionAdded", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 1, "name": "connectionRemoved", "returnType": "void"}, {"access": "private", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 2, "name": "aboutToRemoveConnection", "returnType": "void"}, {"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 3, "name": "aboutToAddConnection", "returnType": "void"}, {"access": "private", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 4, "name": "connectionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "signalsloteditor_p.h", "outputRevision": 69}, {"classes": [{"className": "SignalSlotEditorTool", "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::SignalSlotEditorTool", "superClasses": [{"access": "public", "name": "QDesignerFormWindowToolInterface"}]}], "inputFile": "signalsloteditor_tool.h", "outputRevision": 69}, {"classes": [{"className": "SignalSlotEditorWindow", "lineNumber": 25, "object": true, "qualifiedClassName": "qdesigner_internal::SignalSlotEditorWindow", "slots": [{"access": "public", "arguments": [{"name": "form", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "setActiveFormWindow", "returnType": "void"}, {"access": "private", "arguments": [{"name": "con", "type": "qdesigner_internal::Connection*"}], "index": 1, "name": "updateDialogSelection", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 2, "name": "updateEditorSelection", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}, {"name": "object", "type": "QObject*"}, {"name": "newName", "type": "QString"}, {"name": "old<PERSON>ame", "type": "QString"}], "index": 3, "name": "objectNameChanged", "returnType": "void"}, {"access": "private", "index": 4, "name": "addConnection", "returnType": "void"}, {"access": "private", "index": 5, "name": "removeConnection", "returnType": "void"}, {"access": "private", "index": 6, "name": "updateUi", "returnType": "void"}, {"access": "private", "index": 7, "name": "resizeColumns", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "signalsloteditorwindow.h", "outputRevision": 69}, {"classes": [{"className": "AbstractFindWidget", "lineNumber": 32, "object": true, "qualifiedClassName": "AbstractFindWidget", "slots": [{"access": "public", "index": 0, "name": "activate", "returnType": "void"}, {"access": "public", "index": 1, "name": "deactivate", "returnType": "void"}, {"access": "public", "index": 2, "name": "findNext", "returnType": "void"}, {"access": "public", "index": 3, "name": "find<PERSON>revious", "returnType": "void"}, {"access": "public", "index": 4, "name": "findCurrentText", "returnType": "void"}, {"access": "private", "index": 5, "name": "updateButtons", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "abstractfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "ItemViewFindWidget", "lineNumber": 26, "object": true, "qualifiedClassName": "ItemViewFindWidget", "superClasses": [{"access": "public", "name": "AbstractFindWidget"}]}], "inputFile": "itemviewfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "TextEditFindWidget", "lineNumber": 24, "object": true, "qualifiedClassName": "TextEditFindWidget", "superClasses": [{"access": "public", "name": "AbstractFindWidget"}]}], "inputFile": "texteditfindwidget_p.h", "outputRevision": 69}, {"classes": [{"className": "ConnectDialog", "lineNumber": 28, "object": true, "qualifiedClassName": "qdesigner_internal::ConnectDialog", "slots": [{"access": "private", "index": 0, "name": "populateLists", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 1, "name": "selectSignal", "returnType": "void"}, {"access": "private", "arguments": [{"name": "item", "type": "QListWidgetItem*"}], "index": 2, "name": "selectSlot", "returnType": "void"}, {"access": "private", "index": 3, "name": "populateSignalList", "returnType": "void"}, {"access": "private", "arguments": [{"name": "signal", "type": "QString"}], "index": 4, "name": "populateSlotList", "returnType": "void"}, {"access": "private", "index": 5, "isCloned": true, "name": "populateSlotList", "returnType": "void"}, {"access": "private", "index": 6, "name": "editSignals", "returnType": "void"}, {"access": "private", "index": 7, "name": "editSlots", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "connectdialog_p.h", "outputRevision": 69}, {"classes": [{"className": "QtButtonPropertyBrowser", "lineNumber": 24, "object": true, "qualifiedClassName": "QtButtonPropertyBrowser", "signals": [{"access": "public", "arguments": [{"name": "item", "type": "QtBrowserItem*"}], "index": 0, "name": "collapsed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QtBrowserItem*"}], "index": 1, "name": "expanded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyBrowser"}]}], "inputFile": "qtbuttonpropertybrowser_p.h", "outputRevision": 69}, {"classes": [{"className": "QtSpinBoxFactory", "lineNumber": 26, "object": true, "qualifiedClassName": "QtSpinBoxFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtIntPropertyManager>"}]}, {"className": "QtSliderFactory", "lineNumber": 46, "object": true, "qualifiedClassName": "QtSliderFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtIntPropertyManager>"}]}, {"className": "QtScrollBarFactory", "lineNumber": 66, "object": true, "qualifiedClassName": "QtScrollBarFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtIntPropertyManager>"}]}, {"className": "QtCheckBoxFactory", "lineNumber": 86, "object": true, "qualifiedClassName": "QtCheckBoxFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtBoolPropertyManager>"}]}, {"className": "QtDoubleSpinBoxFactory", "lineNumber": 106, "object": true, "qualifiedClassName": "QtDoubleSpinBoxFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtDoublePropertyManager>"}]}, {"className": "QtLineEditFactory", "lineNumber": 126, "object": true, "qualifiedClassName": "QtLineEditFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtStringPropertyManager>"}]}, {"className": "QtDateEditFactory", "lineNumber": 146, "object": true, "qualifiedClassName": "QtDateEditFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtDatePropertyManager>"}]}, {"className": "QtTimeEditFactory", "lineNumber": 166, "object": true, "qualifiedClassName": "QtTimeEditFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtTimePropertyManager>"}]}, {"className": "QtDateTimeEditFactory", "lineNumber": 186, "object": true, "qualifiedClassName": "QtDateTimeEditFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtDateTimePropertyManager>"}]}, {"className": "QtKeySequenceEditorFactory", "lineNumber": 206, "object": true, "qualifiedClassName": "QtKeySequenceEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtKeySequencePropertyManager>"}]}, {"className": "QtCharEditorFactory", "lineNumber": 226, "object": true, "qualifiedClassName": "QtCharEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtCharPropertyManager>"}]}, {"className": "QtEnumEditorFactory", "lineNumber": 246, "object": true, "qualifiedClassName": "QtEnumEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtEnumPropertyManager>"}]}, {"className": "QtCursorEditorFactory", "lineNumber": 266, "object": true, "qualifiedClassName": "QtCursorEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtCursorPropertyManager>"}]}, {"className": "QtColorEditorFactory", "lineNumber": 286, "object": true, "qualifiedClassName": "QtColorEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtColorPropertyManager>"}]}, {"className": "QtFontEditorFactory", "lineNumber": 306, "object": true, "qualifiedClassName": "QtFontEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtFontPropertyManager>"}]}], "inputFile": "qteditorfactory_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGroupBoxPropertyBrowser", "lineNumber": 24, "object": true, "qualifiedClassName": "QtGroupBoxPropertyBrowser", "superClasses": [{"access": "public", "name": "QtAbstractPropertyBrowser"}]}], "inputFile": "qtgroupboxpropertybrowser_p.h", "outputRevision": 69}, {"classes": [{"className": "QtAbstractPropertyManager", "lineNumber": 71, "object": true, "qualifiedClassName": "QtAbstractPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "parent", "type": "QtProperty*"}, {"name": "after", "type": "QtProperty*"}], "index": 0, "name": "propertyInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 1, "name": "propertyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "parent", "type": "QtProperty*"}], "index": 2, "name": "propertyRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 3, "name": "propertyDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QtAbstractEditorFactoryBase", "lineNumber": 101, "object": true, "qualifiedClassName": "QtAbstractEditorFactoryBase", "slots": [{"access": "protected", "arguments": [{"name": "manager", "type": "QObject*"}], "index": 0, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QtAbstractPropertyBrowser", "lineNumber": 211, "object": true, "qualifiedClassName": "QtAbstractPropertyBrowser", "signals": [{"access": "public", "arguments": [{"type": "QtBrowserItem*"}], "index": 0, "name": "currentItemChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 1, "name": "addProperty", "returnType": "QtBrowserItem*"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "afterProperty", "type": "QtProperty*"}], "index": 2, "name": "insertProperty", "returnType": "QtBrowserItem*"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}], "index": 3, "name": "removeProperty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qtpropertybrowser_p.h", "outputRevision": 69}, {"classes": [{"className": "QtGroupPropertyManager", "lineNumber": 28, "object": true, "qualifiedClassName": "QtGroupPropertyManager", "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtIntPropertyManager", "lineNumber": 44, "object": true, "qualifiedClassName": "QtIntPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "int"}, {"name": "maxVal", "type": "int"}], "index": 1, "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "step", "type": "int"}], "index": 2, "name": "singleStepChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 3, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "int"}], "index": 4, "name": "setMinimum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "maxVal", "type": "int"}], "index": 5, "name": "setMaximum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "int"}, {"name": "maxVal", "type": "int"}], "index": 6, "name": "setRang<PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "step", "type": "int"}], "index": 7, "name": "setSingleStep", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtBoolPropertyManager", "lineNumber": 78, "object": true, "qualifiedClassName": "QtBoolPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "bool"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "bool"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtDoublePropertyManager", "lineNumber": 104, "object": true, "qualifiedClassName": "QtDoublePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "double"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "double"}, {"name": "maxVal", "type": "double"}], "index": 1, "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "step", "type": "double"}], "index": 2, "name": "singleStepChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 3, "name": "decimalsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "double"}], "index": 4, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "double"}], "index": 5, "name": "setMinimum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "maxVal", "type": "double"}], "index": 6, "name": "setMaximum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "double"}, {"name": "maxVal", "type": "double"}], "index": 7, "name": "setRang<PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "step", "type": "double"}], "index": 8, "name": "setSingleStep", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 9, "name": "setDecimals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtStringPropertyManager", "lineNumber": 141, "object": true, "qualifiedClassName": "QtStringPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QString"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "regExp", "type": "QRegularExpression"}], "index": 1, "name": "regExpChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QString"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "regExp", "type": "QRegularExpression"}], "index": 3, "name": "setRegExp", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtDatePropertyManager", "lineNumber": 169, "object": true, "qualifiedClassName": "QtDatePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QDate"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QDate"}, {"name": "maxVal", "type": "QDate"}], "index": 1, "name": "rangeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QDate"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QDate"}], "index": 3, "name": "setMinimum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "maxVal", "type": "QDate"}], "index": 4, "name": "setMaximum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QDate"}, {"name": "maxVal", "type": "QDate"}], "index": 5, "name": "setRang<PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtTimePropertyManager", "lineNumber": 200, "object": true, "qualifiedClassName": "QtTimePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QTime"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QTime"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtDateTimePropertyManager", "lineNumber": 225, "object": true, "qualifiedClassName": "QtDateTimePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QDateTime"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QDateTime"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtKeySequencePropertyManager", "lineNumber": 250, "object": true, "qualifiedClassName": "QtKeySequencePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QKeySequence"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QKeySequence"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtCharPropertyManager", "lineNumber": 275, "object": true, "qualifiedClassName": "QtCharPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QChar"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QChar"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtLocalePropertyManager", "lineNumber": 301, "object": true, "qualifiedClassName": "QtLocalePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QLocale"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QLocale"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtPointPropertyManager", "lineNumber": 328, "object": true, "qualifiedClassName": "QtPointPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QPoint"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QPoint"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtPointFPropertyManager", "lineNumber": 355, "object": true, "qualifiedClassName": "QtPointFPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QPointF"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 1, "name": "decimalsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QPointF"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 3, "name": "setDecimals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtSizePropertyManager", "lineNumber": 385, "object": true, "qualifiedClassName": "QtSizePropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSize"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSize"}, {"name": "maxVal", "type": "QSize"}], "index": 1, "name": "rangeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSize"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSize"}], "index": 3, "name": "setMinimum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "maxVal", "type": "QSize"}], "index": 4, "name": "setMaximum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSize"}, {"name": "maxVal", "type": "QSize"}], "index": 5, "name": "setRang<PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtSizeFPropertyManager", "lineNumber": 418, "object": true, "qualifiedClassName": "QtSizeFPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSizeF"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSizeF"}, {"name": "maxVal", "type": "QSizeF"}], "index": 1, "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 2, "name": "decimalsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSizeF"}], "index": 3, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSizeF"}], "index": 4, "name": "setMinimum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "maxVal", "type": "QSizeF"}], "index": 5, "name": "setMaximum", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "minVal", "type": "QSizeF"}, {"name": "maxVal", "type": "QSizeF"}], "index": 6, "name": "setRang<PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 7, "name": "setDecimals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtRectPropertyManager", "lineNumber": 454, "object": true, "qualifiedClassName": "QtRectPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QRect"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "constraint", "type": "QRect"}], "index": 1, "name": "constraintChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QRect"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "constraint", "type": "QRect"}], "index": 3, "name": "setConstraint", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtRectFPropertyManager", "lineNumber": 484, "object": true, "qualifiedClassName": "QtRectFPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QRectF"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "constraint", "type": "QRectF"}], "index": 1, "name": "constraintChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 2, "name": "decimalsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QRectF"}], "index": 3, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "constraint", "type": "QRectF"}], "index": 4, "name": "setConstraint", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "prec", "type": "int"}], "index": 5, "name": "setDecimals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtEnumPropertyManager", "lineNumber": 517, "object": true, "qualifiedClassName": "QtEnumPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "names", "type": "QStringList"}], "index": 1, "name": "enumNamesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "icons", "type": "QMap<int,QIcon>"}], "index": 2, "name": "enumIconsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 3, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "names", "type": "QStringList"}], "index": 4, "name": "setEnumNames", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "icons", "type": "QMap<int,QIcon>"}], "index": 5, "name": "setEnumIcons", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtFlagPropertyManager", "lineNumber": 549, "object": true, "qualifiedClassName": "QtFlagPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "names", "type": "QStringList"}], "index": 1, "name": "flag<PERSON>ames<PERSON><PERSON>ed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "int"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "names", "type": "QStringList"}], "index": 3, "name": "setFlagNames", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtSizePolicyPropertyManager", "lineNumber": 579, "object": true, "qualifiedClassName": "QtSizePolicyPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSizePolicy"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QSizePolicy"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtFontPropertyManager", "lineNumber": 607, "object": true, "qualifiedClassName": "QtFontPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QFont"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QFont"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtColorPropertyManager", "lineNumber": 637, "object": true, "qualifiedClassName": "QtColorPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QColor"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QColor"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtCursorPropertyManager", "lineNumber": 665, "object": true, "qualifiedClassName": "QtCursorPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QCursor"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "QCursor"}], "index": 1, "name": "setValue", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}], "inputFile": "qtpropertymanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QtTreePropertyBrowser", "enums": [{"isClass": false, "isFlag": false, "name": "ResizeMode", "values": ["Interactive", "<PERSON><PERSON><PERSON>", "Fixed", "ResizeToContents"]}], "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "indentation", "read": "indentation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setIndentation"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "rootIsDecorated", "read": "rootIsDecorated", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRootIsDecorated"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "alternatingRowColors", "read": "alternatingRowColors", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlternatingRowColors"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "headerVisible", "read": "isHeaderVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHeaderVisible"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "resizeMode", "read": "resizeMode", "required": false, "scriptable": true, "stored": true, "type": "ResizeMode", "user": false, "write": "setResizeMode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "splitterPosition", "read": "splitterPosition", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSplitterPosition"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "propertiesWithoutValueMarked", "read": "propertiesWithoutValueMarked", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPropertiesWithoutValueMarked"}], "qualifiedClassName": "QtTreePropertyBrowser", "signals": [{"access": "public", "arguments": [{"name": "item", "type": "QtBrowserItem*"}], "index": 0, "name": "collapsed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QtBrowserItem*"}], "index": 1, "name": "expanded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyBrowser"}]}], "inputFile": "qttreepropertybrowser_p.h", "outputRevision": 69}, {"classes": [{"className": "QtVariantPropertyManager", "lineNumber": 46, "object": true, "qualifiedClassName": "QtVariantPropertyManager", "signals": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "attribute", "type": "QString"}, {"name": "val", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "attributeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "val", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 2, "name": "setValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QtProperty*"}, {"name": "attribute", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "name": "setAttribute", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtAbstractPropertyManager"}]}, {"className": "QtVariantEditorFactory", "lineNumber": 92, "object": true, "qualifiedClassName": "QtVariantEditorFactory", "superClasses": [{"access": "public", "name": "QtAbstractEditorFactory<QtVariantPropertyManager>"}]}], "inputFile": "qtvariantproperty_p.h", "outputRevision": 69}, {"classes": [{"className": "SignalSlotEditorPlugin", "interfaces": [[{"className": "QDesignerFormEditorPluginInterface", "id": "\"org.qt-project.Qt.Designer.QDesignerFormEditorPluginInterface\""}]], "lineNumber": 22, "object": true, "qualifiedClassName": "qdesigner_internal::SignalSlotEditorPlugin", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "activeFormWindowChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "addFormWindow", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "removeFormWindow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerFormEditorPluginInterface"}]}], "inputFile": "signalsloteditor_plugin.h", "outputRevision": 69}, {"classes": [{"className": "TabOrderEditorPlugin", "interfaces": [[{"className": "QDesignerFormEditorPluginInterface", "id": "\"org.qt-project.Qt.Designer.QDesignerFormEditorPluginInterface\""}]], "lineNumber": 23, "object": true, "qualifiedClassName": "qdesigner_internal::TabOrderEditorPlugin", "slots": [{"access": "public", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 0, "name": "activeFormWindowChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 1, "name": "addFormWindow", "returnType": "void"}, {"access": "private", "arguments": [{"name": "formWindow", "type": "QDesignerFormWindowInterface*"}], "index": 2, "name": "removeFormWindow", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QDesignerFormEditorPluginInterface"}]}], "inputFile": "tabordereditor_plugin.h", "outputRevision": 69}, {"classes": [{"className": "QtColorButtonPrivate", "lineNumber": 14, "object": true, "qualifiedClassName": "QtColorButtonPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtcolorbutton.cpp", "outputRevision": 69}, {"classes": [{"className": "QtCharEdit", "lineNumber": 1460, "object": true, "qualifiedClassName": "QtCharEdit", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "QChar"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "value", "type": "QChar"}], "index": 1, "name": "setValue", "returnType": "void"}, {"access": "private", "index": 2, "name": "slotClearChar", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "QtColorEditWidget", "lineNumber": 2065, "object": true, "qualifiedClassName": "QtColorEditWidget", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "QColor"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "value", "type": "QColor"}], "index": 1, "name": "setValue", "returnType": "void"}, {"access": "private", "index": 2, "name": "buttonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}, {"className": "QtFontEditWidget", "lineNumber": 2264, "object": true, "qualifiedClassName": "QtFontEditWidget", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "QFont"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "value", "type": "QFont"}], "index": 1, "name": "setValue", "returnType": "void"}, {"access": "private", "index": 2, "name": "buttonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "qteditorfactory.cpp", "outputRevision": 69}, {"classes": [{"className": "QtMetaEnumWrapper", "lineNumber": 359, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "policy", "read": "policy", "required": false, "scriptable": true, "stored": true, "type": "QSizePolicy::Policy", "user": false}], "qualifiedClassName": "QtMetaEnumWrapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qtpropertymanager.cpp", "outputRevision": 69}, {"classes": [{"className": "QtPropertyEditorView", "lineNumber": 90, "object": true, "qualifiedClassName": "QtPropertyEditorView", "superClasses": [{"access": "public", "name": "QTreeWidget"}]}, {"className": "QtPropertyEditorDelegate", "lineNumber": 189, "object": true, "qualifiedClassName": "QtPropertyEditorDelegate", "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 0, "name": "slotEditorDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QItemDelegate"}]}], "inputFile": "qttreepropertybrowser.cpp", "outputRevision": 69}, {"classes": [{"className": "InlineEditorModel", "lineNumber": 379, "object": true, "qualifiedClassName": "InlineEditorModel", "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}, {"className": "InlineEditor", "lineNumber": 467, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": true, "write": "setText"}], "qualifiedClassName": "InlineEditor", "slots": [{"access": "private", "arguments": [{"name": "idx", "type": "int"}], "index": 0, "name": "checkSelection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QComboBox"}]}, {"className": "ConnectionDelegate", "lineNumber": 540, "object": true, "qualifiedClassName": "ConnectionDelegate", "slots": [{"access": "private", "index": 0, "name": "emitCommitData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStyledItemDelegate"}]}], "inputFile": "signalsloteditorwindow.cpp", "outputRevision": 69}]