import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Basic

Dialog {
    header: null
    modal: true
    width: 150
    height: textInput.height

    // Properties for text input customization
    property int fontSize: 14
    property string fontFamily: "Arial"
    property color textColor: "black"

    // Remove all padding and margins
    padding: 0
    topPadding: 0
    bottomPadding: 0
    leftPadding: 0
    rightPadding: 0
    background: Rectangle {
        width: 150
        height: 16
        color: "#11111111"
        border.color: "#aaaaaaaa"
        border.width: 1
        radius: 2
    }

    signal textInputAccepted(string text)

    // Keep the positioning logic
    onVisibleChanged: {
        if (visible) {
            if (x < 0)
                x = 0;
            if (y < 0)
                y = 0;

            // Set focus to the text input when dialog opens
            textInput.forceActiveFocus();
        }
    }

    // Custom handling for enter key to accept the dialog
    onAccepted: applyText()
    onRejected: textInput.text = ""

    function applyText() {
      textInputAccepted(textInput.text);
      textInput.text = "";
      close();
    }

    // Remove standardButtons and replace with custom buttons
    standardButtons: Dialog.NoButton

    // Replace Column with just the TextField
    contentItem: TextField {
        id: textInput
        font.pixelSize: fontSize
        font.family: fontFamily
        color: textColor
        placeholderText: "Enter text..."
        placeholderTextColor: "#aaaaaa"
        selectByMouse: true
        verticalAlignment: TextInput.AlignVCenter
        background: Rectangle {
            color: "#ffffff"
            border.width: 0
        }

        // Padding for text input - adjust to position text lower
        leftPadding: 0
        rightPadding: 8
        topPadding: 15  // Increased top padding to lower text position
        bottomPadding: 0

        // Ensure cursor is visible
        cursorVisible: true
        focus: true

        // Handle Enter key press
        Keys.onReturnPressed: applyText()
        Keys.onEnterPressed: applyText()
        Keys.onEscapePressed: reject()

        Component.onCompleted: {
            // Force cursor to be visible when component loads
            forceActiveFocus();
        }
    }
}
