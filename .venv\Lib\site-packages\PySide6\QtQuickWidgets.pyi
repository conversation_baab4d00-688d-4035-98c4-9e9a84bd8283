# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtQuickWidgets, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtQuickWidgets`

import PySide6.QtQuickWidgets
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets
import PySide6.QtQml
import PySide6.QtQuick

import enum
import typing
from PySide6.QtCore import Signal


class QIntList: ...


class QQuickWidget(PySide6.QtWidgets.QWidget):

    sceneGraphError          : typing.ClassVar[Signal] = ... # sceneGraphError(QQuickWindow::SceneGraphError,QString)
    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged(QQuickWidget::Status)

    class ResizeMode(enum.Enum):

        SizeViewToRootObject      = ...  # 0x0
        SizeRootObjectToView      = ...  # 0x1

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Ready                     = ...  # 0x1
        Loading                   = ...  # 0x2
        Error                     = ...  # 0x3


    @typing.overload
    def __init__(self, engine: PySide6.QtQml.QQmlEngine, parent: PySide6.QtWidgets.QWidget, /, *, resizeMode: PySide6.QtQuickWidgets.QQuickWidget.ResizeMode | None = ..., status: PySide6.QtQuickWidgets.QQuickWidget.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, uri: str, typeName: str, /, parent: PySide6.QtWidgets.QWidget | None = ..., *, resizeMode: PySide6.QtQuickWidgets.QQuickWidget.ResizeMode | None = ..., status: PySide6.QtQuickWidgets.QQuickWidget.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., *, resizeMode: PySide6.QtQuickWidgets.QQuickWidget.ResizeMode | None = ..., status: PySide6.QtQuickWidgets.QQuickWidget.Status | None = ..., source: PySide6.QtCore.QUrl | None = ...) -> None: ...
    @typing.overload
    def __init__(self, source: PySide6.QtCore.QUrl | str, /, parent: PySide6.QtWidgets.QWidget | None = ..., *, resizeMode: PySide6.QtQuickWidgets.QQuickWidget.ResizeMode | None = ..., status: PySide6.QtQuickWidgets.QQuickWidget.Status | None = ...) -> None: ...

    def dragEnterEvent(self, arg__1: PySide6.QtGui.QDragEnterEvent, /) -> None: ...
    def dragLeaveEvent(self, arg__1: PySide6.QtGui.QDragLeaveEvent, /) -> None: ...
    def dragMoveEvent(self, arg__1: PySide6.QtGui.QDragMoveEvent, /) -> None: ...
    def dropEvent(self, arg__1: PySide6.QtGui.QDropEvent, /) -> None: ...
    def engine(self, /) -> PySide6.QtQml.QQmlEngine: ...
    def errors(self, /) -> typing.List[PySide6.QtQml.QQmlError]: ...
    def event(self, arg__1: PySide6.QtCore.QEvent, /) -> bool: ...
    def focusInEvent(self, event: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def focusNextPrevChild(self, next: bool, /) -> bool: ...
    def focusOutEvent(self, event: PySide6.QtGui.QFocusEvent, /) -> None: ...
    def format(self, /) -> PySide6.QtGui.QSurfaceFormat: ...
    def grabFramebuffer(self, /) -> PySide6.QtGui.QImage: ...
    def hideEvent(self, arg__1: PySide6.QtGui.QHideEvent, /) -> None: ...
    def initialSize(self, /) -> PySide6.QtCore.QSize: ...
    def keyPressEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def keyReleaseEvent(self, arg__1: PySide6.QtGui.QKeyEvent, /) -> None: ...
    def loadFromModule(self, uri: str, typeName: str, /) -> None: ...
    def mouseDoubleClickEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseMoveEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, arg__1: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def paintEvent(self, event: PySide6.QtGui.QPaintEvent, /) -> None: ...
    def quickWindow(self, /) -> PySide6.QtQuick.QQuickWindow: ...
    def resizeEvent(self, arg__1: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def resizeMode(self, /) -> PySide6.QtQuickWidgets.QQuickWidget.ResizeMode: ...
    def rootContext(self, /) -> PySide6.QtQml.QQmlContext: ...
    def rootObject(self, /) -> PySide6.QtQuick.QQuickItem: ...
    def setClearColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setContent(self, url: PySide6.QtCore.QUrl | str, component: PySide6.QtQml.QQmlComponent, item: PySide6.QtCore.QObject, /) -> None: ...
    def setFormat(self, format: PySide6.QtGui.QSurfaceFormat | PySide6.QtGui.QSurfaceFormat.FormatOption, /) -> None: ...
    def setInitialProperties(self, initialProperties: typing.Dict[str, typing.Any], /) -> None: ...
    def setResizeMode(self, arg__1: PySide6.QtQuickWidgets.QQuickWidget.ResizeMode, /) -> None: ...
    def setSource(self, arg__1: PySide6.QtCore.QUrl | str, /) -> None: ...
    def showEvent(self, arg__1: PySide6.QtGui.QShowEvent, /) -> None: ...
    def sizeHint(self, /) -> PySide6.QtCore.QSize: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def status(self, /) -> PySide6.QtQuickWidgets.QQuickWidget.Status: ...
    def timerEvent(self, arg__1: PySide6.QtCore.QTimerEvent, /) -> None: ...
    def wheelEvent(self, arg__1: PySide6.QtGui.QWheelEvent, /) -> None: ...


# eof
