import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import "../constants/WindowsConstants.js" as WindowsConstants
import "../constants/NumberConstants.js" as NumberConstants
import "../constants/TextConstants.js" as TextConstants
import "../components"
import "../components/dialogs"

Window {
    id: cameraWindow
    visible: true
    width: WindowsConstants.cameraWindowSize.width
    height: WindowsConstants.cameraWindowSize.height
    minimumWidth: WindowsConstants.cameraWindowSize.width
    minimumHeight: WindowsConstants.cameraWindowSize.height
    title: modelName + "(" + serialNumber + ")" + " [" + cameraStreamWidth + " × " + cameraStreamHeight +"]" + " [Window ID:" + cameraspaceId + "]"

    // Property to track if window is being closed programmatically
    property bool isProgrammaticClose: false
    property int workspaceId
    property int cameraspaceId
    property string modelName
    property string serialNumber
    property int cameraStreamWidth
    property int cameraStreamHeight
    property bool isActive: false
    property var drawingWindow: null  // Add this line to declare the drawingWindow property
    property var workspaceDatabase: null  // Add this property
    property alias drawingCanvas: drawingCanvas  // This exposes the internal drawingCanvas to the outside

    // Drawing style properties - updated from DrawingWindow
    property color drawingColor: "red"  // Default value
    property int drawingLineWidth: 2    // Default value
    property int drawingLineStyle: 0    // Default - Solid

    property var checkedOldButton: null  // Add property to track checked button
    property bool isCameraReady: false
    property var pendingIplSettings: null

    signal cameraWorkSpaceClosed

    onXChanged: {
        if (workspaceDatabase && cameraspaceId) {
            workspaceDatabase.set_cameraspace_field(cameraspaceId, "window_x", x);
        }
    }
    
    onYChanged: {
        if (workspaceDatabase && cameraspaceId) {
            workspaceDatabase.set_cameraspace_field(cameraspaceId, "window_y", y);
        }
    }
    
    onWidthChanged: {
        if (workspaceDatabase && cameraspaceId) {
            workspaceDatabase.set_cameraspace_field(cameraspaceId, "window_w", width);
        }
    }
    
    onHeightChanged: {
        if (workspaceDatabase && cameraspaceId) {
            workspaceDatabase.set_cameraspace_field(cameraspaceId, "window_h", height);
        }
    }

    // Function to update drawing properties directly from DrawingWindow
    function updateDrawingProperties() {
        drawingCanvas.startPoint = Qt.point(0, 0);
        drawingCanvas.endPoint = Qt.point(0, 0);
        drawingCanvas.currentPath = [];
        if (drawingWindow) {
            // Update color from RGBA values from DrawingWindow sliders
            drawingColor = Qt.rgba(drawingWindow.redSlider.sliderValue, drawingWindow.greenSlider.sliderValue, drawingWindow.blueSlider.sliderValue, drawingWindow.alphaSlider.sliderValue);

            // Update line width from DrawingWindow spinbox
            drawingLineWidth = drawingWindow.lineWidth.value;

            // Update line style from DrawingWindow combobox
            drawingLineStyle = drawingWindow.lineStyle.currentIndex;

            // Force canvas repaint with new settings
            if (drawingCanvas.available) {
                drawingCanvas.requestPaint();
            }
        } else {
            // Fallback to database if DrawingWindow reference is not set
            var linestyle = workspaceDatabase.get_linestyle();
            if (linestyle) {
                drawingColor = Qt.rgba(linestyle["red"], linestyle["green"], linestyle["blue"], linestyle["alpha"]);
                drawingLineWidth = linestyle["line_width"];
                drawingLineStyle = linestyle["line_style"];
                if (drawingCanvas.available) {
                    drawingCanvas.requestPaint();
                }
            }
        }
    }

    Component.onCompleted: {
        var db_cameraSettings = workspaceDatabase.get_camera_settings(serialNumber);
        
        // Store IPL settings for later use
        pendingIplSettings = workspaceDatabase.get_camera_ipl_settings(serialNumber, cameraspaceId);

        // Start camera when window is shown
        cameraListModel.start_camera_stream(serialNumber, cameraspaceId, db_cameraSettings || {});

        if(db_cameraSettings && db_cameraSettings.width > 0 && db_cameraSettings.height > 0) {
            cameraWindow.cameraStreamWidth = db_cameraSettings.width;
            cameraWindow.cameraStreamHeight = db_cameraSettings.height;
        }
        else {
            var cameraSettings = cameraListModel.get_camera_settings(serialNumber);
            workspaceDatabase.add_camera_setting(serialNumber, cameraSettings.frame_rate, cameraSettings.exposure_time, cameraSettings.gain, cameraSettings.gain, cameraSettings.width, cameraSettings.height);
            cameraWindow.cameraStreamWidth = cameraSettings.width;
            cameraWindow.cameraStreamHeight = cameraSettings.height;
        }

        var drawings = workspaceDatabase.get_drawings(cameraspaceId);
        for (var i = 0; i < drawings.length; i++) {
            var drawingData = JSON.parse(drawings[i].json_data);
            drawingCanvas.paths.push(drawingData);
        }

        // Load saved camera position and zoom
        var cameraspaceInfo = workspaceDatabase.get_cameraspace(cameraspaceId);
        if (cameraspaceInfo) {
            cameraArea.zoomFactor = cameraspaceInfo.zoom;
            cameraView.scale = cameraspaceInfo.zoom;
            drawingCanvas.scale = cameraspaceInfo.zoom;
            viewContainer.x = cameraspaceInfo.pan_x;
            viewContainer.y = cameraspaceInfo.pan_y;
        }
    }

    // Connect to camera frame signal
    Connections {
        target: cameraListModel
        function onCameraFrameReady(cameraSerialNumber, cameraspaceIdValue) {
            if (cameraSerialNumber === serialNumber) {
                cameraView.source = "image://live_" + cameraSerialNumber + "/" + Math.random();
                
                // Apply path transformations after first frame if not done yet
                if (!isCameraReady && pendingIplSettings) {
                    isCameraReady = true;

                    if(cameraspaceId === cameraspaceIdValue)
                        applyPathTransformations(pendingIplSettings);    
                    pendingIplSettings = null;  // Clear pending settings
                }
            }
        }
    }

    function applyPathTransformations(iplSettings) {
        // Apply IPL settings to camera without path updates
        if (iplSettings) {
            if (iplSettings.rotation !== undefined && iplSettings.rotation !== 0) {
                // Use rotate_right or rotate_left based on rotation value
                var rotations = (iplSettings.rotation / 90) % 4;  // Convert to number of 90-degree rotations
                if (rotations > 0) {
                    for (var i = 0; i < rotations; i++) {
                        cameraListModel.rotate_left(serialNumber);
                    }
                } else if (rotations < 0) {
                    for (var i = 0; i < Math.abs(rotations); i++) {
                        cameraListModel.rotate_right(serialNumber);
                    }
                }
            }

            if (iplSettings.flip_h === 1) {
                cameraListModel.flip_horizontal(serialNumber);
            }
            if (iplSettings.flip_v === 1) {
                cameraListModel.flip_vertical(serialNumber);
            }
        }
    }

    // Connect to DrawingWindow directly
    Connections {
        target: drawingWindow
        function onDrawingSettingValueChanged() {
            updateDrawingProperties();
        }
    }

    onClosing: function () {
        try {
            // Check if this is a close button click or programmatic close
            if (!isProgrammaticClose || workspaceId == NumberConstants.noWorkspaceId) {
                workspaceDatabase.remove_cameraspace(cameraspaceId); // Remove from database using cameraspaceId
            }
            if (serialNumber) {
                cameraListModel.stop_camera_stream(serialNumber);
            } else {
                console.log("Window closing, No valid serial number found.");
            }
            cameraWorkSpaceClosed();
        } catch (error) {
            console.error("Error closing camera window:", error);
        }
    }

    // Function to close the window programmatically
    function closeWindow() {
        isProgrammaticClose = true;
        close();
        isProgrammaticClose = false; // Reset flag
    }

    // Add property to track if this is an application shutdown
    property bool isApplicationClosing: false

    Component.onDestruction: {
        // Cleanup code when window is destroyed
        if (serialNumber) {
            cameraListModel.stop_camera_stream(serialNumber);
        }
    }

    onActiveChanged: {
        isActive = cameraWindow.active;
        if (isActive) {
            var settings = cameraListModel.get_camera_settings(cameraWindow.serialNumber);
            if (settings) {
                // Pass settings to BrightnessWindow
                brightnessWindow.serialNumber = cameraWindow.serialNumber;
                brightnessWindow.updateSettings(cameraspaceId, settings);
            }

            // Update drawing properties whenever window becomes active
            updateDrawingProperties();

            // Update IPL window with active camera
            iplWindow.activeSerialNumber = cameraWindow.serialNumber;
            iplWindow.cameraspaceId = cameraWindow.cameraspaceId;
        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        CameraWorkspaceToolbar {
            id: cameraWorkspaceToolbar
            serialNumber: cameraWindow.serialNumber
            drawingCanvas: drawingCanvas
            cameraspaceId: cameraWindow.cameraspaceId
            cameraWindow: cameraWindow

            onDrawingClicked: {
                drawingWindow.visible = !drawingWindow.visible;
                workspaceDatabase.set_workspace_subwindow_status(menuBar.selectedWorkspaceId, "camera_drawing", drawingWindow.visible);
            }

            onCameraStreamStopped: {
                console.log("cameraStream Stopped clicked");
            }
        }

        Rectangle {
            id: cameraArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#3c3c3c"
            property real zoomFactor: 1.0
            property real minZoom: 0.5
            property real maxZoom: 5.0
            property real zoomStep: 0.25
            clip: true

            // Add watchers for zoom and pan changes
            onZoomFactorChanged: {
                if (workspaceDatabase && cameraspaceId) {
                    workspaceDatabase.set_cameraspace_field(cameraspaceId, "zoom", zoomFactor);
                }
            }

            // Container for synchronized movement
            Item {
                id: viewContainer
                width: parent.width
                height: parent.height

                // Add watchers for pan position changes
                onXChanged: {
                    if (workspaceDatabase && cameraspaceId) {
                        workspaceDatabase.set_cameraspace_field(cameraspaceId, "pan_x", x);
                    }
                }
                
                onYChanged: {
                    if (workspaceDatabase && cameraspaceId) {
                        workspaceDatabase.set_cameraspace_field(cameraspaceId, "pan_y", y);
                    }
                }

                Image {
                    id: cameraView
                    width: parent.width
                    height: parent.height
                    fillMode: Image.PreserveAspectFit
                    cache: false
                    source: "image://live_" + serialNumber + "/" + Math.random()
                    scale: cameraArea.zoomFactor
                    transformOrigin: Item.Center

                    // Calculate actual image display size and position
                    property rect displayRect: {
                        let w = width;
                        let h = height;
                        let x = 0;
                        let y = 0;
                        if (sourceSize.width / sourceSize.height > width / height) {
                            h = width * (sourceSize.height / sourceSize.width);
                            y = (height - h) / 2;
                        } else {
                            w = height * (sourceSize.width / sourceSize.height);
                            x = (width - w) / 2;
                        }

                        if(w == 0 || h == 0) {
                            // cameraStreamWidth = width;
                            // cameraStreamHeight = height;
                            
                            return Qt.rect(0, 0, width, height);
                        }
                        return Qt.rect(x, y, w, h);
                    }
                }

                Canvas {
                    id: drawingCanvas
                    x: cameraView.displayRect.x
                    y: cameraView.displayRect.y
                    width: cameraView.displayRect.width
                    height: cameraView.displayRect.height
                    scale: cameraArea.zoomFactor
                    transformOrigin: Item.Center
                    z: 1

                    renderStrategy: Canvas.Cooperative
                    renderTarget: Canvas.FramebufferObject
                    enabled: cameraWorkspaceToolbar.penButton.checked || 
                            cameraWorkspaceToolbar.lineButton.checked || 
                            cameraWorkspaceToolbar.rectangleButton.checked || 
                            cameraWorkspaceToolbar.circleButton.checked || 
                            cameraWorkspaceToolbar.distanceButton.checked || 
                            cameraWorkspaceToolbar.textButton.checked

                    // Store drawing paths
                    property var paths: []
                    property var currentPath: []

                    // Keep track of starting points for shapes
                    property point startPoint: Qt.point(0, 0)
                    property point endPoint: Qt.point(0, 0)

                    // Add text properties
                    property string currentText: ""
                    property int textFontSize: 14
                    property string textFontFamily: "Arial"
                    property point textPosition: Qt.point(0, 0)

                    function getWRate(w) {
                        return width / w;
                    }

                    function getHRate(h) {
                        return height / h;
                    }

                    // Add property to track canvas bounds
                    property rect canvasBounds: Qt.rect(x, y, width, height)

                    // Update when camera view display rect changes
                    Connections {
                        target: cameraView
                        function onDisplayRectChanged() {
                            drawingCanvas.x = cameraView.displayRect.x;
                            drawingCanvas.y = cameraView.displayRect.y;
                            drawingCanvas.width = cameraView.displayRect.width;
                            drawingCanvas.height = cameraView.displayRect.height;
                            drawingCanvas.canvasBounds = Qt.rect(drawingCanvas.x, drawingCanvas.y, drawingCanvas.width, drawingCanvas.height);
                            drawingCanvas.requestPaint();
                        }
                    }

                    // Add arrow drawing function
                    function drawArrow(ctx, fromX, fromY, toX, toY, arrowSize) {
                        var angle = Math.atan2(toY - fromY, toX - fromX);
                        var arrowLength = arrowSize;
                        var arrowWidth = arrowSize / 2;

                        // Calculate arrow points
                        var arrowX1 = toX - arrowLength * Math.cos(angle - Math.PI / 6);
                        var arrowY1 = toY - arrowLength * Math.sin(angle - Math.PI / 6);
                        var arrowX2 = toX - arrowLength * Math.cos(angle + Math.PI / 6);
                        var arrowY2 = toY - arrowLength * Math.sin(angle + Math.PI / 6);

                        // Draw arrow
                        ctx.beginPath();
                        ctx.moveTo(toX, toY);
                        ctx.lineTo(arrowX1, arrowY1);
                        ctx.moveTo(toX, toY);
                        ctx.lineTo(arrowX2, arrowY2);
                        ctx.stroke();
                    }

                    // Add the function to draw rotated text
                    function drawRotatedText(ctx, text, startX, startY, endX, endY, font, color, alpha) {
                        // Calculate center point
                        var centerX = (startX + endX) / 2;
                        var centerY = (startY + endY) / 2;
                        
                        // Calculate angle
                        var angle = Math.atan2(endY - startY, endX - startX);
                        
                        // Save context state
                        ctx.save();
                        
                        // Set text properties
                        ctx.font = font;
                        ctx.fillStyle = color;
                        ctx.globalAlpha = alpha;
                        
                        // Move to center point and rotate
                        ctx.translate(centerX, centerY);
                        ctx.rotate(angle);
                        
                        // Measure text width for centering
                        var textWidth = ctx.measureText(text).width;
                        
                        // Draw text centered above the line
                        ctx.fillText(text, -textWidth/2, -5);
                        
                        // Restore context state
                        ctx.restore();
                    }

                    // Store drawing path with settings
                    function savePath(path) {
                        // Store current drawing settings with the path
                        path.color = drawingColor.toString();
                        path.lineWidth = drawingLineWidth;
                        path.lineStyle = drawingLineStyle;
                        // Store alpha value separately to ensure it's preserved
                        path.alpha = drawingColor.a;                        

                        var pathCopy = JSON.parse(JSON.stringify(path));
                        workspaceDatabase.save_drawing(cameraspaceId, JSON.stringify(pathCopy));

                        for(var i=0; i<paths.length; i++) {
                            // Save the path to the database
                            pathCopy = JSON.parse(JSON.stringify(paths[i]));
                            workspaceDatabase.update_drawing(cameraspaceId, i+1, JSON.stringify(pathCopy));
                        }

                        paths.push(path);
                        updateDrawingProperties();
                    }

                    onPaint: {
                        var ctx = getContext("2d");
                        ctx.reset();

                        // Draw all stored paths
                        for (var i = 0; i < paths.length; i++) {
                            var path = paths[i];

                            // Set line properties from stored path settings
                            ctx.lineWidth = path.lineWidth;
                            // Use globalAlpha to ensure transparency works
                            ctx.globalAlpha = path.alpha;
                            ctx.strokeStyle = path.color;
                            ctx.lineCap = "round";
                            ctx.lineJoin = "round";

                            // Apply line style
                            if (path.lineStyle === 1) {
                                // Dashed
                                ctx.setLineDash([path.lineWidth * 3, path.lineWidth * 1.5]);
                            } else if (path.lineStyle === 2) {
                                // Dotted
                                ctx.setLineDash([path.lineWidth, path.lineWidth]);
                            } else {
                                ctx.setLineDash([]); // Solid line (default)
                            }

                            // Handle different types of paths (freehand vs shapes)
                            if (path.type === NumberConstants.tbDrawingType.pen && path.path.length > 0) {
                                // Draw pen
                                ctx.beginPath();
                                ctx.moveTo(path.path[0].x * getWRate(path.w), path.path[0].y * getHRate(path.h));
                                for (var j = 1; j < path.path.length; j++) {
                                    ctx.lineTo(path.path[j].x * getWRate(path.w), path.path[j].y * getHRate(path.h));
                                }
                                ctx.stroke();
                            } else if (path.type === NumberConstants.tbDrawingType.text) {
                                // Draw text
                                ctx.font = path.fontSize + "px " + path.fontFamily;
                                ctx.fillStyle = path.color;
                                ctx.globalAlpha = path.alpha;
                                ctx.fillText(path.text, path.x * getWRate(path.w), path.y * getHRate(path.h));
                            } else if (path.type === NumberConstants.tbDrawingType.line) {
                                // Draw line
                                ctx.beginPath();
                                ctx.moveTo(path.startX * getWRate(path.w), path.startY * getHRate(path.h));
                                ctx.lineTo(path.endX * getWRate(path.w), path.endY * getHRate(path.h));
                                ctx.stroke();
                            } else if (path.type === NumberConstants.tbDrawingType.rectangle) {
                                // Draw rectangle
                                ctx.beginPath();
                                ctx.rect(Math.min(path.startX, path.endX) * getWRate(path.w), Math.min(path.startY, path.endY) * getHRate(path.h), Math.abs(path.endX - path.startX) * getWRate(path.w), Math.abs(path.endY - path.startY) * getHRate(path.h));
                                ctx.stroke();
                            } else if (path.type === NumberConstants.tbDrawingType.circle) {
                                // Draw circle/ellipse
                                ctx.beginPath();
                                ctx.ellipse(Math.min(path.startX, path.endX) * getWRate(path.w), Math.min(path.startY, path.endY) * getHRate(path.h), Math.abs(path.endX - path.startX) * getWRate(path.w), Math.abs(path.endY - path.startY) * getHRate(path.h), 0, 0, 2 * Math.PI);
                                ctx.stroke();
                            } else if (path.type === NumberConstants.tbDrawingType.distance) {
                                // Draw distance line
                                ctx.beginPath();
                                ctx.moveTo(path.startX * getWRate(path.w), path.startY * getHRate(path.h));
                                ctx.lineTo(path.endX * getWRate(path.w), path.endY * getHRate(path.h));
                                ctx.stroke();

                                // Draw arrows at both ends
                                drawArrow(ctx, path.startX * getWRate(path.w), path.startY * getHRate(path.h), path.endX * getWRate(path.w), path.endY * getHRate(path.h), 10);
                                drawArrow(ctx, path.endX * getWRate(path.w), path.endY * getHRate(path.h), path.startX * getWRate(path.w), path.startY * getHRate(path.h), 10);

                                var iplSettings = workspaceDatabase.get_camera_ipl_settings(serialNumber, cameraspaceId);
                                // Calculate and display distance
                                var dx = (path.endX - path.startX) * (iplSettings.rotation % 180 === 0 ? cameraStreamWidth : cameraStreamHeight) /path.w;
                                var dy = (path.endY - path.startY) * (iplSettings.rotation % 180 === 0 ? cameraStreamHeight : cameraStreamWidth) /path.h;
                                var distance = Math.sqrt(dx * dx + dy * dy);

                                // Use the new function to draw rotated text
                                drawRotatedText(
                                    ctx,
                                    distance.toFixed(1) + "px",
                                    path.startX * getWRate(path.w),
                                    path.startY * getHRate(path.h),
                                    path.endX * getWRate(path.w),
                                    path.endY * getHRate(path.h),
                                    TextConstants.fontArrowText,
                                    TextConstants.colorDistanceText,
                                    path.alpha
                                );
                            }
                        }

                        // Draw current preview
                        drawPreview(ctx);
                    }

                    function drawPreview(ctx) {
                        // Set line properties for current drawing preview - use current settings
                        ctx.lineWidth = drawingLineWidth;
                        ctx.globalAlpha = drawingColor.a;
                        ctx.strokeStyle = drawingColor.toString();
                        ctx.lineCap = "round";
                        ctx.lineJoin = "round";

                        // Apply current line style for preview
                        if (drawingLineStyle === 1) {
                            ctx.setLineDash([drawingLineWidth * 3, drawingLineWidth * 1.5]);
                        } else if (drawingLineStyle === 2) {
                            ctx.setLineDash([drawingLineWidth, drawingLineWidth]);
                        } else {
                            ctx.setLineDash([]);
                        }

                        // Draw current path for freehand drawing
                        if (cameraWorkspaceToolbar.penButton.checked && currentPath.length > 1) {
                            ctx.beginPath();
                            ctx.moveTo(currentPath[0].x, currentPath[0].y);
                            for (var k = 1; k < currentPath.length; k++) {
                                ctx.lineTo(currentPath[k].x, currentPath[k].y);
                            }
                            ctx.stroke();
                        }

                        // Draw current shape preview while dragging
                        if ((startPoint.x !== endPoint.x || startPoint.y !== endPoint.y)) {
                            if (cameraWorkspaceToolbar.lineButton.checked) {
                                ctx.beginPath();
                                ctx.moveTo(startPoint.x, startPoint.y);
                                ctx.lineTo(endPoint.x, endPoint.y);
                                ctx.stroke();
                            } else if (cameraWorkspaceToolbar.rectangleButton.checked) {
                                ctx.beginPath();
                                ctx.rect(Math.min(startPoint.x, endPoint.x), 
                                       Math.min(startPoint.y, endPoint.y), 
                                       Math.abs(endPoint.x - startPoint.x), 
                                       Math.abs(endPoint.y - startPoint.y));
                                ctx.stroke();
                            } else if (cameraWorkspaceToolbar.circleButton.checked) {
                                ctx.beginPath();
                                ctx.ellipse(Math.min(startPoint.x, endPoint.x), 
                                          Math.min(startPoint.y, endPoint.y), 
                                          Math.abs(endPoint.x - startPoint.x), 
                                          Math.abs(endPoint.y - startPoint.y), 
                                          0, 0, 2 * Math.PI);
                                ctx.stroke();
                            } else if (cameraWorkspaceToolbar.distanceButton.checked) {
                                ctx.beginPath();
                                ctx.moveTo(startPoint.x, startPoint.y);
                                ctx.lineTo(endPoint.x, endPoint.y);
                                ctx.stroke();
                                drawArrow(ctx, startPoint.x, startPoint.y, endPoint.x, endPoint.y, 10);
                                drawArrow(ctx, endPoint.x, endPoint.y, startPoint.x, startPoint.y, 10);

                                var iplSettings = workspaceDatabase.get_camera_ipl_settings(serialNumber, cameraspaceId);

                                var dx = (endPoint.x - startPoint.x) * (iplSettings.rotation % 180 === 0 ? cameraStreamWidth : cameraStreamHeight) / drawingCanvas.width;
                                var dy = (endPoint.y - startPoint.y) * (iplSettings.rotation % 180 === 0 ? cameraStreamHeight : cameraStreamWidth) / drawingCanvas.height;
                                var distance = Math.sqrt(dx * dx + dy * dy);

                                drawRotatedText(
                                    ctx,
                                    distance.toFixed(1) + "px",
                                    startPoint.x,
                                    startPoint.y,
                                    endPoint.x,
                                    endPoint.y,
                                    TextConstants.fontArrowText,
                                    TextConstants.colorDistanceText,
                                    drawingColor.a
                                );
                            }
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        enabled: parent.enabled

                        onPressed: mouse => {
                            var point = Qt.point(mouse.x, mouse.y);
                            parent.startPoint = point;
                            parent.endPoint = point;
                            if (cameraWorkspaceToolbar.penButton.checked) {
                                parent.currentPath = [{
                                    x: point.x,
                                    y: point.y
                                }];
                            } else if (cameraWorkspaceToolbar.textButton.checked) {
                                parent.textPosition = point;
                                var dialogPos = drawingCanvas.mapToItem(cameraWindow.contentItem, point.x, point.y);
                                textInputDialog.x = dialogPos.x;
                                textInputDialog.y = dialogPos.y;
                                textInputDialog.open();
                            }
                            parent.requestPaint();
                        }

                        onPositionChanged: mouse => {
                            if (mouse.buttons & Qt.LeftButton) {
                                var point = Qt.point(mouse.x, mouse.y);
                                parent.endPoint = point;
                                if (cameraWorkspaceToolbar.penButton.checked) {
                                    parent.currentPath.push({
                                        x: point.x,
                                        y: point.y
                                    });
                                }
                                parent.requestPaint();
                            }
                        }

                        onReleased: mouse => {
                            var point = Qt.point(mouse.x, mouse.y);
                            parent.endPoint = point;
                            if (cameraWorkspaceToolbar.penButton.checked && parent.currentPath.length > 0) {
                                parent.savePath({
                                    type: NumberConstants.tbDrawingType.pen,
                                    path: parent.currentPath,
                                    w: drawingCanvas.width,
                                    h: drawingCanvas.height
                                });
                            } else if ((cameraWorkspaceToolbar.lineButton.checked || 
                                       cameraWorkspaceToolbar.rectangleButton.checked || 
                                       cameraWorkspaceToolbar.circleButton.checked || 
                                       cameraWorkspaceToolbar.distanceButton.checked) && 
                                       (parent.startPoint.x !== parent.endPoint.x || 
                                        parent.startPoint.y !== parent.endPoint.y)) {
                                var type = cameraWorkspaceToolbar.lineButton.checked ? 
                                          NumberConstants.tbDrawingType.line : 
                                          cameraWorkspaceToolbar.rectangleButton.checked ? 
                                          NumberConstants.tbDrawingType.rectangle : 
                                          cameraWorkspaceToolbar.circleButton.checked ? 
                                          NumberConstants.tbDrawingType.circle : 
                                          NumberConstants.tbDrawingType.distance;
                                parent.savePath({
                                    type: type,
                                    startX: parent.startPoint.x,
                                    startY: parent.startPoint.y,
                                    endX: parent.endPoint.x,
                                    endY: parent.endPoint.y,
                                    w: drawingCanvas.width,
                                    h: drawingCanvas.height
                                });
                            }
                        }
                    }

                    Behavior on scale {
                        NumberAnimation {
                            duration: 100
                        }
                    }
                    Behavior on x {
                        NumberAnimation {
                            duration: 50
                        }
                    }
                    Behavior on y {
                        NumberAnimation {
                            duration: 50
                        }
                    }
                }
            }

            // Panning MouseArea
            MouseArea {
                id: panningMouseArea
                anchors.fill: parent
                acceptedButtons: Qt.LeftButton
                preventStealing: true
                enabled: !cameraWorkspaceToolbar.penButton.checked && !cameraWorkspaceToolbar.lineButton.checked && !cameraWorkspaceToolbar.rectangleButton.checked && !cameraWorkspaceToolbar.circleButton.checked && !cameraWorkspaceToolbar.distanceButton.checked && !cameraWorkspaceToolbar.textButton.checked

                property point lastPos
                property bool panning: false

                onPressed: function (mouse) {
                    lastPos = Qt.point(mouse.x, mouse.y);
                    panning = true;
                }

                onPositionChanged: function (mouse) {
                    if (panning) {
                        var delta = Qt.point(mouse.x - lastPos.x, mouse.y - lastPos.y);
                        viewContainer.x += delta.x;
                        viewContainer.y += delta.y;
                        lastPos = Qt.point(mouse.x, mouse.y);
                    }
                }

                onReleased: {
                    panning = false;
                }
            }

            // Add smooth animations for movement
            Behavior on scale {
                NumberAnimation {
                    duration: 100
                }
            }

            Behavior on x {
                NumberAnimation {
                    duration: 50
                }
            }

            Behavior on y {
                NumberAnimation {
                    duration: 50
                }
            }

            // Add MouseArea for wheel zoom
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.NoButton
                onWheel: function (wheel) {
                    var oldZoom = cameraArea.zoomFactor;
                    var newZoom;
                    if (wheel.angleDelta.y > 0) {
                        // Zoom in
                        newZoom = Math.min(oldZoom + cameraArea.zoomStep, cameraArea.maxZoom);
                    } else {
                        // Zoom out
                        newZoom = Math.max(oldZoom - cameraArea.zoomStep, cameraArea.minZoom);
                    }
                    if (newZoom !== oldZoom) {
                        cameraArea.zoomFactor = newZoom;
                        cameraView.scale = newZoom;
                        drawingCanvas.scale = newZoom;

                        // Center the view when zoomed out
                        if (newZoom <= 1.0) {
                            viewContainer.x = 0;
                            viewContainer.y = 0;
                        }
                    }
                }
            }
        }
    }

    MouseArea {
        id: cameraMouseArea
        anchors.fill: parent
        acceptedButtons: Qt.RightButton
        onClicked: mouse => {
            if (mouse.button === Qt.RightButton) {
                cameraContextMenu.popup(mouse.x, mouse.y);
            }
        }
    }

    CameraContextMenu {
        id: cameraContextMenu
        cameraView: cameraView
        drawingCanvas: drawingCanvas
        cameraArea: cameraArea
        cameraWindow: cameraWindow
    }

    // Text input dialog
    InputDialog {
        id: textInputDialog

        // Set font properties from canvas settings
        fontSize: drawingCanvas.textFontSize
        fontFamily: drawingCanvas.textFontFamily
        textColor: drawingColor  // Apply current drawing color to text input

        onTextInputAccepted: function (inputTextData) {
            if (inputTextData.trim() !== "") {
                var textPath = {
                    type: NumberConstants.tbDrawingType.text,
                    text: inputTextData,
                    x: drawingCanvas.textPosition.x,
                    y: drawingCanvas.textPosition.y + 12,
                    fontSize: drawingCanvas.textFontSize,
                    fontFamily: drawingCanvas.textFontFamily,
                    w: drawingCanvas.width,
                    h: drawingCanvas.height
                };
                drawingCanvas.savePath(textPath);
            }
        }
    }
}
